#!/usr/bin/env python3
"""
Manual test script for quiz randomization functionality
This script helps verify the randomization implementation by:
1. Testing the randomization logic directly
2. Checking database schema
3. Verifying session handling
"""

import sqlite3
import json
import random
import os
from datetime import datetime

def test_database_schema():
    """Test that the database has the required randomization fields"""
    print("🔍 Testing database schema...")
    
    db_path = os.path.join('instance', 'quiz.db')
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check quiz table for randomize_questions column
        cursor.execute("PRAGMA table_info(quiz)")
        quiz_columns = [column[1] for column in cursor.fetchall()]
        
        if 'randomize_questions' in quiz_columns:
            print("✅ Quiz table has randomize_questions column")
        else:
            print("❌ Quiz table missing randomize_questions column")
            return False
        
        # Check quiz_attempt table for question_order column
        cursor.execute("PRAGMA table_info(quiz_attempt)")
        attempt_columns = [column[1] for column in cursor.fetchall()]
        
        if 'question_order' in attempt_columns:
            print("✅ QuizAttempt table has question_order column")
        else:
            print("❌ QuizAttempt table missing question_order column")
            return False
        
        # Check sample data
        cursor.execute("SELECT id, title, randomize_questions FROM quiz LIMIT 5")
        quizzes = cursor.fetchall()
        
        print("\n📋 Sample quiz randomization settings:")
        for quiz_id, title, randomize in quizzes:
            status = "✅ Enabled" if randomize else "❌ Disabled"
            print(f"  Quiz {quiz_id}: {title[:40]}... - {status}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def test_randomization_logic():
    """Test the randomization logic with sample data"""
    print("\n🎲 Testing randomization logic...")
    
    # Simulate questions
    sample_questions = [
        {'id': 1, 'text': 'Question 1'},
        {'id': 2, 'text': 'Question 2'},
        {'id': 3, 'text': 'Question 3'},
        {'id': 4, 'text': 'Question 4'},
        {'id': 5, 'text': 'Question 5'}
    ]
    
    print(f"Original order: {[q['id'] for q in sample_questions]}")
    
    # Test randomization multiple times
    orders = []
    for i in range(5):
        question_ids = [q['id'] for q in sample_questions]
        random.shuffle(question_ids)
        orders.append(question_ids)
        print(f"Random order {i+1}: {question_ids}")
    
    # Check if we got different orders
    unique_orders = len(set(map(tuple, orders)))
    if unique_orders > 1:
        print(f"✅ Generated {unique_orders} different orders out of 5 attempts")
    else:
        print("⚠️  All orders were the same (could happen by chance)")
    
    return True

def test_small_quiz_logic():
    """Test that small quizzes (≤2 questions) don't get randomized"""
    print("\n🔢 Testing small quiz logic...")
    
    # Test with 1 question
    questions_1 = [{'id': 1, 'text': 'Only question'}]
    should_randomize_1 = len(questions_1) > 2
    print(f"1 question quiz - Should randomize: {should_randomize_1} ✅")
    
    # Test with 2 questions
    questions_2 = [{'id': 1, 'text': 'Question 1'}, {'id': 2, 'text': 'Question 2'}]
    should_randomize_2 = len(questions_2) > 2
    print(f"2 question quiz - Should randomize: {should_randomize_2} ✅")
    
    # Test with 3 questions
    questions_3 = [{'id': 1, 'text': 'Q1'}, {'id': 2, 'text': 'Q2'}, {'id': 3, 'text': 'Q3'}]
    should_randomize_3 = len(questions_3) > 2
    print(f"3 question quiz - Should randomize: {should_randomize_3} ✅")
    
    return True

def test_json_serialization():
    """Test JSON serialization for question order storage"""
    print("\n💾 Testing JSON serialization...")
    
    # Test question order serialization
    question_order = [3, 1, 4, 2, 5]
    
    # Serialize
    json_string = json.dumps(question_order)
    print(f"Serialized: {json_string}")
    
    # Deserialize
    restored_order = json.loads(json_string)
    print(f"Deserialized: {restored_order}")
    
    if question_order == restored_order:
        print("✅ JSON serialization works correctly")
        return True
    else:
        print("❌ JSON serialization failed")
        return False

def test_session_key_generation():
    """Test session key generation for different quizzes"""
    print("\n🔑 Testing session key generation...")
    
    quiz_ids = [1, 2, 3, 10, 100]
    
    for quiz_id in quiz_ids:
        session_key = f'quiz_{quiz_id}_question_order'
        print(f"Quiz {quiz_id}: {session_key}")
    
    print("✅ Session key generation works correctly")
    return True

def check_existing_quizzes():
    """Check existing quizzes and their randomization settings"""
    print("\n📊 Checking existing quizzes...")
    
    db_path = os.path.join('instance', 'quiz.db')
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get quiz details with question counts
        cursor.execute("""
            SELECT q.id, q.title, q.randomize_questions, COUNT(qu.id) as question_count
            FROM quiz q
            LEFT JOIN question qu ON q.id = qu.quiz_id
            GROUP BY q.id, q.title, q.randomize_questions
            ORDER BY q.id
        """)
        
        quizzes = cursor.fetchall()
        
        print("Quiz Analysis:")
        print("-" * 80)
        print(f"{'ID':<4} {'Title':<30} {'Randomize':<10} {'Questions':<10} {'Status'}")
        print("-" * 80)
        
        for quiz_id, title, randomize, question_count in quizzes:
            randomize_text = "Yes" if randomize else "No"
            
            # Determine if randomization should be effective
            if randomize and question_count > 2:
                status = "✅ Active"
            elif randomize and question_count <= 2:
                status = "⚠️  Disabled (≤2 Q)"
            elif not randomize:
                status = "❌ Disabled"
            else:
                status = "❓ Unknown"
            
            title_short = title[:28] + ".." if len(title) > 30 else title
            print(f"{quiz_id:<4} {title_short:<30} {randomize_text:<10} {question_count:<10} {status}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Run all manual tests"""
    print("🧪 Quiz Randomization Manual Test Suite")
    print("=" * 60)
    print("This script tests the randomization implementation components.")
    print()
    
    tests = [
        ("Database Schema", test_database_schema),
        ("Randomization Logic", test_randomization_logic),
        ("Small Quiz Logic", test_small_quiz_logic),
        ("JSON Serialization", test_json_serialization),
        ("Session Key Generation", test_session_key_generation),
        ("Existing Quizzes", check_existing_quizzes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All manual tests passed!")
        print("\n📝 Next steps:")
        print("1. Start the Flask app: python app.py")
        print("2. Create a new quiz with randomization enabled")
        print("3. Have multiple students attempt the same quiz")
        print("4. Verify they see questions in different orders")
    else:
        print("⚠️  Some tests failed. Check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
