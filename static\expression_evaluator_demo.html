<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math Expression Evaluator Demo</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['\\(', '\\)']],
                displayMath: [['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .expression-input-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: flex-start;
        }
        .expression-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .operation-select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .evaluate-btn {
            padding: 10px 20px;
            background-color: #6f42c1;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .evaluate-btn:hover {
            background-color: #5a2d91;
        }
        .evaluate-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            border-left: 4px solid #6f42c1;
            display: none;
        }
        .result-container.success {
            border-left-color: #28a745;
            background-color: #f8fff9;
        }
        .result-container.error {
            border-left-color: #dc3545;
            background-color: #fff8f8;
            color: #721c24;
        }
        .examples {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .example-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }
        .example-item:hover {
            background-color: #e9ecef;
        }
        .variable-input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🧠 Math Expression Evaluator Demo</h1>
            <p>Powered by SymPy - Test mathematical expression evaluation</p>
        </div>
        
        <div class="expression-input-container">
            <input type="text" class="expression-input" id="expression-input" 
                   placeholder="Enter math expression (e.g., 2*x + 3*x - 5)" />
            <select class="operation-select" id="operation-select">
                <option value="simplify">Simplify</option>
                <option value="expand">Expand</option>
                <option value="factor">Factor</option>
                <option value="differentiate">Differentiate</option>
                <option value="integrate">Integrate</option>
                <option value="solve">Solve for x</option>
            </select>
            <input type="text" class="variable-input" id="variable-input" 
                   placeholder="Variable" value="x" />
            <button class="evaluate-btn" id="evaluate-btn" onclick="evaluateExpression()">
                Evaluate
            </button>
        </div>
        
        <div class="result-container" id="result-container">
            <div id="result-content"></div>
        </div>
        
        <div class="examples">
            <h3>Example Expressions (Click to Try):</h3>
            
            <div class="example-item" onclick="tryExample('2*x + 3*x - 5', 'simplify')">
                <strong>Simplify:</strong> 2*x + 3*x - 5 → 5*x - 5
            </div>
            
            <div class="example-item" onclick="tryExample('(x + 1)^2', 'expand')">
                <strong>Expand:</strong> (x + 1)^2 → x² + 2x + 1
            </div>
            
            <div class="example-item" onclick="tryExample('x^2 - 1', 'factor')">
                <strong>Factor:</strong> x² - 1 → (x - 1)(x + 1)
            </div>
            
            <div class="example-item" onclick="tryExample('x^2 + 3*x', 'differentiate')">
                <strong>Differentiate:</strong> x² + 3x → 2x + 3
            </div>
            
            <div class="example-item" onclick="tryExample('2*x + 3', 'integrate')">
                <strong>Integrate:</strong> 2x + 3 → x² + 3x
            </div>
            
            <div class="example-item" onclick="tryExample('x^2 - 4', 'solve')">
                <strong>Solve:</strong> x² - 4 = 0 → x = ±2
            </div>
            
            <div class="example-item" onclick="tryExample('sin(x)^2 + cos(x)^2', 'simplify')">
                <strong>Trigonometric:</strong> sin²(x) + cos²(x) → 1
            </div>
            
            <div class="example-item" onclick="tryExample('exp(x) * exp(y)', 'simplify')">
                <strong>Exponential:</strong> e^x × e^y → e^(x+y)
            </div>
        </div>
    </div>

    <script>
        // Handle operation change to show/hide variable input
        document.getElementById('operation-select').addEventListener('change', function() {
            const variableInput = document.getElementById('variable-input');
            const operation = this.value;
            
            if (operation === 'differentiate' || operation === 'integrate' || operation === 'solve') {
                variableInput.style.display = 'inline-block';
            } else {
                variableInput.style.display = 'none';
            }
        });

        function tryExample(expression, operation) {
            document.getElementById('expression-input').value = expression;
            document.getElementById('operation-select').value = operation;
            
            // Trigger operation change event
            document.getElementById('operation-select').dispatchEvent(new Event('change'));
            
            evaluateExpression();
        }

        async function evaluateExpression() {
            const expressionInput = document.getElementById('expression-input');
            const operationSelect = document.getElementById('operation-select');
            const variableInput = document.getElementById('variable-input');
            const evaluateBtn = document.getElementById('evaluate-btn');
            const resultContainer = document.getElementById('result-container');
            const resultContent = document.getElementById('result-content');
            
            const expression = expressionInput.value.trim();
            
            if (!expression) {
                alert('Please enter a mathematical expression');
                return;
            }
            
            // Show loading state
            evaluateBtn.disabled = true;
            evaluateBtn.textContent = '🔄 Evaluating...';
            resultContainer.style.display = 'block';
            resultContainer.className = 'result-container';
            resultContent.innerHTML = '<em>Evaluating expression...</em>';
            
            try {
                const operation = operationSelect.value;
                const variable = variableInput.value || 'x';
                
                const response = await fetch('/api/evaluate_expression', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        expression: expression,
                        operation: operation,
                        variable: variable
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResult(data);
                } else {
                    displayError(data.error, data.suggestion);
                }
                
            } catch (error) {
                displayError('Network error: Could not connect to server', 'Please check your internet connection and try again.');
            } finally {
                // Reset button state
                evaluateBtn.disabled = false;
                evaluateBtn.textContent = 'Evaluate';
            }
        }

        function displayResult(data) {
            const resultContainer = document.getElementById('result-container');
            const resultContent = document.getElementById('result-content');
            
            let html = `
                <div style="margin-bottom: 10px;">
                    <strong>Original:</strong> ${data.original_expression}
                </div>
                <div style="margin-bottom: 10px; font-size: 1.1em; color: #28a745;">
                    <strong>${data.operation.charAt(0).toUpperCase() + data.operation.slice(1)}:</strong> 
                    <span class="tex2jax_process">\\(${data.result_latex}\\)</span>
                </div>
            `;
            
            if (data.variables && data.variables.length > 0) {
                html += `<div style="font-size: 0.9em; color: #6c757d;">Variables: ${data.variables.join(', ')}</div>`;
            }
            
            if (data.is_constant && data.numerical_value !== null) {
                html += `<div style="font-size: 0.9em; color: #6c757d;">Numerical value: ${data.numerical_value}</div>`;
            }
            
            if (Array.isArray(data.result_latex)) {
                html = `
                    <div style="margin-bottom: 10px;">
                        <strong>Original:</strong> ${data.original_expression}
                    </div>
                    <div style="margin-bottom: 10px; font-size: 1.1em; color: #28a745;">
                        <strong>Solutions:</strong><br>
                `;
                data.result_latex.forEach((solution, index) => {
                    html += `<span class="tex2jax_process">\\(x_{${index+1}} = ${solution}\\)</span><br>`;
                });
                html += '</div>';
            }
            
            resultContent.innerHTML = html;
            resultContainer.className = 'result-container success';
            
            // Re-render MathJax
            if (window.MathJax) {
                MathJax.typesetPromise([resultContent]).catch(function (err) {
                    console.log('MathJax error:', err.message);
                });
            }
        }

        function displayError(error, suggestion) {
            const resultContainer = document.getElementById('result-container');
            const resultContent = document.getElementById('result-content');
            
            let html = `<div><strong>Error:</strong> ${error}</div>`;
            if (suggestion) {
                html += `<div style="margin-top: 10px; font-size: 0.9em;"><strong>Suggestion:</strong> ${suggestion}</div>`;
            }
            
            resultContent.innerHTML = html;
            resultContainer.className = 'result-container error';
        }

        // Allow Enter key to evaluate
        document.getElementById('expression-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                evaluateExpression();
            }
        });
    </script>
</body>
</html>
