#!/usr/bin/env python3
"""
Migration script to update existing teacher email addresses to comply with new validation rules.
This script will help migrate teachers from other domains to @jpischool.com domain.
"""

from app import app, db, User

def migrate_teacher_emails():
    """Migrate existing teacher emails to @jpischool.com domain"""
    
    try:
        with app.app_context():
            # Query for all <NAME_EMAIL> domains
            teachers = User.query.filter_by(role='teacher').all()
            non_jpischool_teachers = [t for t in teachers if not t.email.endswith('@jpischool.com')]
            
            if not non_jpischool_teachers:
                print("✅ All teachers already have @jpischool.com email addresses.")
                return
            
            print("Teachers that need email migration:")
            print("=" * 60)
            
            for teacher in non_jpischool_teachers:
                current_email = teacher.email
                # Extract username part before @ symbol
                username = current_email.split('@')[0]
                new_email = f"{username}@jpischool.com"
                
                print(f"Teacher: {teacher.name}")
                print(f"  Current: {current_email}")
                print(f"  New:     {new_email}")
                print()
            
            # Ask for confirmation
            print("⚠️  IMPORTANT: This will update teacher email addresses in the database.")
            print("Make sure to inform the affected teachers about their new email addresses.")
            print()
            
            response = input("Do you want to proceed with the migration? (yes/no): ").lower().strip()
            
            if response in ['yes', 'y']:
                print("\nStarting migration...")
                
                for teacher in non_jpischool_teachers:
                    old_email = teacher.email
                    username = old_email.split('@')[0]
                    new_email = f"{username}@jpischool.com"
                    
                    # Check if new email already exists
                    existing_user = User.query.filter_by(email=new_email).first()
                    if existing_user:
                        print(f"❌ Cannot migrate {teacher.name}: Email {new_email} already exists")
                        continue
                    
                    # Update the email
                    teacher.email = new_email
                    print(f"✅ Migrated {teacher.name}: {old_email} → {new_email}")
                
                # Commit changes
                db.session.commit()
                print("\n✅ Migration completed successfully!")
                
                # Show updated list
                print("\nUpdated teacher list:")
                print("-" * 40)
                updated_teachers = User.query.filter_by(role='teacher').all()
                for teacher in updated_teachers:
                    print(f"  {teacher.name}: {teacher.email}")
                    
            else:
                print("Migration cancelled.")
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        db.session.rollback()

def show_migration_preview():
    """Show what the migration would do without making changes"""
    
    try:
        with app.app_context():
            teachers = User.query.filter_by(role='teacher').all()
            non_jpischool_teachers = [t for t in teachers if not t.email.endswith('@jpischool.com')]
            
            if not non_jpischool_teachers:
                print("✅ All teachers already have @jpischool.com email addresses.")
                return
            
            print("MIGRATION PREVIEW - No changes will be made")
            print("=" * 60)
            
            for teacher in non_jpischool_teachers:
                current_email = teacher.email
                username = current_email.split('@')[0]
                new_email = f"{username}@jpischool.com"
                
                # Check if new email would conflict
                existing_user = User.query.filter_by(email=new_email).first()
                conflict_status = " ❌ CONFLICT" if existing_user else " ✅ OK"
                
                print(f"Teacher: {teacher.name}")
                print(f"  {current_email} → {new_email}{conflict_status}")
                print()
    
    except Exception as e:
        print(f"❌ Error during preview: {e}")

if __name__ == "__main__":
    print("Teacher Email Migration Tool")
    print("=" * 40)
    print("1. Preview migration")
    print("2. Perform migration")
    print("3. Exit")
    
    choice = input("\nSelect an option (1-3): ").strip()
    
    if choice == "1":
        show_migration_preview()
    elif choice == "2":
        migrate_teacher_emails()
    elif choice == "3":
        print("Exiting...")
    else:
        print("Invalid choice. Exiting...")
