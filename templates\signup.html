{% extends "base.html" %}

{% block title %}Sign Up - Quiz Management System{% endblock %}

{% block content %}
    {# Remove the duplicate header section #}
    {# <header>
        <div class="container">
            <h1>Quiz Platform</h1>
            <!-- Add navigation if needed later -->
        </div>
    </header> #}

    <main class="page-content">
        <div class="signup-container">
            <h2>Create Your Account</h2>
            <form method="POST" action="{{ url_for('signup') }}" id="signup-form" novalidate>

                <div class="form-group">
                    <label for="name">Full Name</label>
                    <div class="input-wrapper">
                        <input required type="text" id="name" name="name" placeholder="Enter your full name">
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-wrapper">
                        <input required type="email" id="email" name="email" placeholder="Enter your email address">
                    </div>
                    <small class="email-hint" id="emailHint"></small>
                    <span id="email-error" class="error-message"></span>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-wrapper">
                        <input required type="password" id="password" name="password" placeholder="Choose a password">
                    </div>
                    <ul id="password-requirements" class="password-requirements">
                        <li id="pw-length" class="invalid">&#10060; At least 8 characters</li>
                        <li id="pw-upper" class="invalid">&#10060; At least one uppercase letter</li>
                        <li id="pw-lower" class="invalid">&#10060; At least one lowercase letter</li>
                        <li id="pw-digit" class="invalid">&#10060; At least one digit</li>
                        <li id="pw-special" class="invalid">&#10060; At least one special character</li>
                    </ul>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Re-enter Password</label>
                    <div class="input-wrapper">
                        <input required type="password" id="confirm_password" name="confirm_password" placeholder="Confirm your password">
                    </div>
                    <span id="password-match-error" class="error-message" style="display: none;">Passwords do not match.</span>
                </div>

                <div class="form-group">
                    <label for="role">Role</label>
                    <select id="role" name="role" required>
                         <option value="" disabled selected>Select your role</option>
            <option value="student">Student</option>
            <option value="teacher">Teacher</option>
            <option value="parent">Parent</option>
                    </select>
                </div>

                <div class="form-group" id="parent-email-group" style="display: none;">
                    <label for="parent_email">Parent's Email</label>
                    <div class="input-wrapper">
                        <input type="email" id="parent_email" name="parent_email" placeholder="Enter parent's email (if student)">
                    </div>
                    <small class="email-hint">Must end with @gmail.com or @yahoo.com</small>
                    <span id="parent-email-error" class="error-message"></span>
                </div>

                <button type="submit" class="auth-button">
                     Create Account
                </button>
    </form>

            <p class="switch-form">Already have an account? <a href="{{ url_for('login') }}">Log In</a></p>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Quiz Management System. All rights reserved.</p>
        </div>
    </footer>
{% endblock %}

{% block styles %}
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
<style>
    :root {
        /* Color System */
        --primary-color: #4A90E2;
        --primary-dark: #357ABD;
        --primary-light: #6BA4E7;
        --secondary-color: #2C3E50;
        --accent-color: #E74C3C;
        --text-primary: #2C3E50;
        --text-secondary: #7F8C8D;
        --background-light: #F5F7FA;
        --background-dark: #E4E9F2;
        --white: #FFFFFF;
        --error: #E74C3C;
        --success: #2ECC71;
        --warning: #F1C40F;

        /* Spacing System */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-xxl: 3rem;

        /* Border Radius */
        --radius-sm: 4px;
        --radius-md: 8px;
        --radius-lg: 15px;
        --radius-full: 100px;

        /* Shadows */
        --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
        --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
        --shadow-lg: 0 5px 15px rgba(0,0,0,0.1);
    }

    body {
        font-family: 'Poppins', sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, var(--background-light) 0%, var(--background-dark) 100%);
        color: var(--text-primary);
        line-height: 1.6;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .container {
        width: 90%;
        max-width: 1100px;
        margin: 0 auto;
        padding: 0 var(--spacing-md);
    }

    main.page-content {
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-xl) 0;
    }

    .signup-container {
        background-color: var(--white);
        padding: var(--spacing-xxl);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        width: 100%;
        max-width: 480px;
        box-sizing: border-box;
    }

    .signup-container h2 {
        text-align: center;
        margin-bottom: var(--spacing-xl);
        color: var(--secondary-color);
        font-weight: 700;
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .form-group label {
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--text-secondary);
        font-weight: 600;
    }

    .input-wrapper {
      position: relative;
      width: 100%;
    }

    .input-wrapper input {
        width: 100%;
        padding: var(--spacing-md);
        border: 2px solid var(--background-dark);
        border-radius: var(--radius-md);
      font-size: 1rem;
        color: var(--text-primary);
        background-color: var(--white);
        transition: all 0.3s ease;
      box-sizing: border-box;
    }

    .input-wrapper input:focus {
      outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px var(--primary-light);
    }

    .input-wrapper input::placeholder {
        color: var(--text-secondary);
        opacity: 0.7;
    }

    .form-group select {
        width: 100%;
        padding: var(--spacing-md);
        border: 2px solid var(--background-dark);
        border-radius: var(--radius-md);
        font-size: 1rem;
        color: var(--text-primary);
        background-color: var(--white);
        cursor: pointer;
        appearance: none;
        background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%234A90E2%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
        background-repeat: no-repeat;
        background-position: right var(--spacing-md) center;
        background-size: 12px;
        padding-right: var(--spacing-xxl);
    }

    .form-group select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px var(--primary-light);
    }

    .auth-button {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-xl);
        background-color: var(--primary-color);
        color: var(--white);
        border: none;
        border-radius: var(--radius-full);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
      transition: all 0.3s ease;
    }

    .auth-button:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .auth-button:active {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }

    .switch-form {
        text-align: center;
        margin-top: var(--spacing-lg);
        color: var(--text-secondary);
    }

    .switch-form a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
    }

    .switch-form a:hover {
        text-decoration: underline;
    }

    .email-hint {
        display: block;
        font-size: 0.9rem;
        margin-top: var(--spacing-xs);
        color: var(--text-secondary);
    }

    .error-message {
        color: var(--error);
        font-size: 0.9rem;
        margin-top: var(--spacing-xs);
    }

    footer {
        text-align: center;
        padding: var(--spacing-md) 0;
        margin-top: auto;
        color: var(--text-secondary);
        font-size: 0.9em;
    }

    @media (max-width: 480px) {
        .signup-container {
            padding: var(--spacing-xl);
            margin: var(--spacing-md);
        }
    }

    .password-requirements {
        list-style: none;
        padding-left: 0;
        margin-top: 0.5em;
        margin-bottom: 0;
    }
    .password-requirements li {
        font-size: 0.97em;
        margin-bottom: 0.2em;
        color: var(--error);
        display: flex;
        align-items: center;
        transition: color 0.2s;
    }
    .password-requirements li.valid {
        color: var(--success);
    }
    .password-requirements li.invalid {
        color: var(--error);
    }
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('signup-form');
    const roleSelect = document.getElementById('role');
    const parentEmailGroup = document.getElementById('parent-email-group');
    const emailInput = document.getElementById('email');
    const emailHint = document.getElementById('emailHint');
    const emailError = document.getElementById('email-error');
    const parentEmailInput = document.getElementById('parent_email');
    const parentEmailError = document.getElementById('parent-email-error');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const passwordMatchError = document.getElementById('password-match-error');

    // Function to validate email format (basic)
    function isValidEmailFormat(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    // Function to check email rules based on role
    function validateEmail(email, role) {
        emailError.textContent = ''; // Clear previous errors
        emailError.style.display = 'none';
        emailHint.textContent = ''; // Clear hints
        let isValid = true;

        if (!isValidEmailFormat(email)) {
            emailError.textContent = 'Invalid email format.';
            isValid = false;
        } else if (role === 'student') {
            emailHint.textContent = 'Must end with @jpischool.com';
            if (!email.endsWith('@jpischool.com')) {
                emailError.textContent = 'Student email must end with @jpischool.com.';
                isValid = false;
            }
        } else if (role === 'teacher') {
            emailHint.textContent = 'Must end with @jpischool.com';
            if (!email.endsWith('@jpischool.com')) {
                emailError.textContent = 'Teacher email must end with @jpischool.com.';
                isValid = false;
            }
        } else if (role === 'parent') {
            emailHint.textContent = 'Must end with @gmail.com or @yahoo.com';
            if (!(email.endsWith('@gmail.com') || email.endsWith('@yahoo.com'))) {
                emailError.textContent = 'Parent email must end with @gmail.com or @yahoo.com.';
                isValid = false;
            }
        }

        emailError.style.display = isValid ? 'none' : 'block';
        return isValid;
    }

    // Function to validate parent email
    function validateParentEmail(email) {
        parentEmailError.textContent = ''; // Clear previous errors
        parentEmailError.style.display = 'none';
        let isValid = true;

        if (!isValidEmailFormat(email)) {
            parentEmailError.textContent = 'Invalid email format.';
            isValid = false;
        } else if (!(email.endsWith('@gmail.com') || email.endsWith('@yahoo.com'))) {
            parentEmailError.textContent = 'Parent email must end with @gmail.com or @yahoo.com.';
            isValid = false;
        }

        parentEmailError.style.display = isValid ? 'none' : 'block';
        return isValid;
    }

    // Function to check if passwords match
    function checkPasswordsMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        if (confirmPassword && password !== confirmPassword) {
            passwordMatchError.style.display = 'block';
            // Add error class for visual feedback if needed
            confirmPasswordInput.classList.add('input-error');
            return false;
        } else {
            passwordMatchError.style.display = 'none';
            confirmPasswordInput.classList.remove('input-error');
            return true;
        }
    }

    // Show/hide Parent Email based on role
    roleSelect.addEventListener('change', function() {
        const selectedRole = this.value;
        parentEmailGroup.style.display = (selectedRole === 'student') ? 'block' : 'none';
        validateEmail(emailInput.value, selectedRole); // Re-validate user email on role change
        // Make parent email required only if student role is selected
        parentEmailInput.required = (selectedRole === 'student');
    });

    // Real-time validation for user email
    emailInput.addEventListener('input', function() {
        validateEmail(this.value, roleSelect.value);
    });

    // Real-time validation for parent email
    parentEmailInput.addEventListener('input', function() {
         // Only validate parent email if the field is visible (role is student)
         if (parentEmailGroup.style.display === 'block') {
             validateParentEmail(this.value);
         }
    });

    // Real-time validation for password match
    confirmPasswordInput.addEventListener('input', checkPasswordsMatch);
    passwordInput.addEventListener('input', checkPasswordsMatch); // Also check when original password changes

    // --- Password Requirements Live Feedback ---
    const pwReqs = {
        length: document.getElementById('pw-length'),
        upper: document.getElementById('pw-upper'),
        lower: document.getElementById('pw-lower'),
        digit: document.getElementById('pw-digit'),
        special: document.getElementById('pw-special')
    };
    passwordInput.addEventListener('input', function() {
        const val = passwordInput.value;
        // Check each requirement
        const checks = {
            length: val.length >= 8,
            upper: /[A-Z]/.test(val),
            lower: /[a-z]/.test(val),
            digit: /[0-9]/.test(val),
            special: /[^A-Za-z0-9]/.test(val)
        };
        for (const key in checks) {
            if (checks[key]) {
                pwReqs[key].classList.remove('invalid');
                pwReqs[key].classList.add('valid');
                pwReqs[key].innerHTML = '&#9989; ' + pwReqs[key].textContent.replace(/^.*? /, '');
            } else {
                pwReqs[key].classList.remove('valid');
                pwReqs[key].classList.add('invalid');
                pwReqs[key].innerHTML = '&#10060; ' + pwReqs[key].textContent.replace(/^.*? /, '');
            }
        }
    });

    // Form Submission Validation
    form.addEventListener('submit', function(event) {
        // Prevent default form submission to perform validation
        event.preventDefault();

        // Clear previous form-wide 'has-error' states if any
        document.querySelectorAll('.form-group.has-error').forEach(el => el.classList.remove('has-error'));

        let isValid = true;
        const role = roleSelect.value;

        // 1. Validate User Email
        if (!validateEmail(emailInput.value, role)) {
            isValid = false;
             emailInput.closest('.form-group').classList.add('has-error'); // Add visual indication
        }

        // 2. Validate Parent Email (if student)
        if (role === 'student') {
            if (!parentEmailInput.value) {
                parentEmailError.textContent = 'Parent email is required for student registration.';
                parentEmailError.style.display = 'block';
                isValid = false;
                parentEmailInput.closest('.form-group').classList.add('has-error'); // Add visual indication
            } else if (!validateParentEmail(parentEmailInput.value)) {
                 isValid = false;
                 parentEmailInput.closest('.form-group').classList.add('has-error'); // Add visual indication
            }
        }

        // 3. Check Password Match
        if (!checkPasswordsMatch()) {
            isValid = false;
            confirmPasswordInput.closest('.form-group').classList.add('has-error'); // Add visual indication
        }

        // Basic check for other required fields
        if (!document.getElementById('name').value.trim()) {
             document.getElementById('name').closest('.form-group').classList.add('has-error'); // Add visual indication if needed
             isValid = false;
        }
        if (!passwordInput.value) {
             passwordInput.closest('.form-group').classList.add('has-error'); // Add visual indication if needed
             isValid = false;
        }
        if (!confirmPasswordInput.value) {
             confirmPasswordInput.closest('.form-group').classList.add('has-error'); // Add visual indication if needed
             isValid = false;
        }
        if (!role) {
            roleSelect.closest('.form-group').classList.add('has-error'); // Add visual indication if needed
            isValid = false;
        }

        // If all checks pass, submit the form
        if (isValid) {
            form.submit();
        } else {
            // Optionally, display a general message or focus the first error field
            console.log("Form validation failed.");
        }
    });

    // Trigger initial state setup (e.g., hide parent email if default role isn't student)
    roleSelect.dispatchEvent(new Event('change'));
});
</script>
{% endblock %}