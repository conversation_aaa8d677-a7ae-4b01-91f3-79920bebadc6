#!/usr/bin/env python3
"""
Database Migration Script: Calculator Feature
Adds allow_calculator column to quiz table for enabling/disabling calculator during quiz attempts.
"""

import sqlite3
import os
from datetime import datetime

def get_database_path():
    """Get the path to the database file."""
    # Check if instance folder exists
    instance_path = os.path.join(os.getcwd(), 'instance')
    if os.path.exists(instance_path):
        db_path = os.path.join(instance_path, 'quiz.db')
    else:
        # Fallback to current directory
        db_path = os.path.join(os.getcwd(), 'quiz.db')
    
    return os.path.abspath(db_path)

def add_calculator_column():
    """Add the allow_calculator column to the quiz table."""
    
    db_path = get_database_path()
    print(f"=== Calculator Feature Database Migration ===")
    print(f"Started at: {datetime.now()}")
    print(f"Database path: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure the Flask application has been run at least once to create the database.")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Starting calculator migration...")
        
        # Check if allow_calculator column already exists in quiz table
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'allow_calculator' not in columns:
            print("➕ Adding allow_calculator column to quiz table...")
            cursor.execute("""
                ALTER TABLE quiz 
                ADD COLUMN allow_calculator BOOLEAN DEFAULT 0 NOT NULL
            """)
            print("✅ Added allow_calculator column")
            
            # Update existing quizzes to have calculator disabled by default (conservative approach)
            cursor.execute("""
                UPDATE quiz 
                SET allow_calculator = 0 
                WHERE allow_calculator IS NULL
            """)
            print("✅ Set default calculator setting for existing quizzes")
            
        else:
            print("ℹ️  allow_calculator column already exists")
        
        # Create index for better performance
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_quiz_allow_calculator ON quiz(allow_calculator)")
            print("✅ Created index for allow_calculator column")
        except sqlite3.Error as e:
            print(f"⚠️  Index creation warning: {e}")
        
        # Commit changes
        conn.commit()
        
        print("Verifying migration...")
        
        # Verify column was added
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'allow_calculator' in columns:
            print("✅ Migration verification successful")
            
            # Check some sample data
            cursor.execute("SELECT COUNT(*) FROM quiz WHERE allow_calculator = 0")
            disabled_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM quiz WHERE allow_calculator = 1")
            enabled_count = cursor.fetchone()[0]
            
            print(f"📊 Calculator settings: {disabled_count} quizzes with calculator disabled, {enabled_count} enabled")
            
            print(f"✅ Migration completed successfully")
            print(f"Completed at: {datetime.now()}")
            print()
            print("🎉 Calculator feature migration completed successfully!")
            print("Teachers can now enable/disable calculator for individual quizzes.")
            return True
        else:
            print("❌ Migration verification failed")
            print(f"Expected column 'allow_calculator' not found")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main migration function."""
    success = add_calculator_column()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Restart your Flask application")
        print("2. Teachers can now enable calculator in quiz creation/editing forms")
        print("3. Students will see calculator button during quiz attempts when enabled")
        print("4. Calculator state is preserved using sessionStorage during quiz attempts")
        return 0
    else:
        print("\n❌ Migration failed. Please check the errors above and try again.")
        return 1

if __name__ == "__main__":
    exit(main())
