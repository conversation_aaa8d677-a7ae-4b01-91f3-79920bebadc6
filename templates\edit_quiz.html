{% extends "base.html" %}

{% block title %}Edit Quiz: {{ quiz.title }}{% endblock %}

{% block content %}
<div class="container create-quiz-container">
    <h1>Edit Quiz: {{ quiz.get_display_title() }}</h1>

    {% if has_attempts %}
    <div class="alert alert-warning versioning-warning">
        <h3>⚠️ Quiz Versioning Notice</h3>
        <p><strong>This quiz has already been attempted by students.</strong></p>
        <p>To preserve the integrity of past attempts, your changes will create a <strong>new version</strong> of this quiz:</p>
        <ul>
            <li>✅ A new version (v{{ quiz.version + 1 }}) will be created with your changes</li>
            <li>🔒 The current version will be archived and remain read-only</li>
            <li>📊 All past attempts and scores will remain tied to the original version</li>
            <li>👥 Students will only see the new active version going forward</li>
        </ul>
    </div>
    {% endif %}

    <form method="post" action="{{ url_for('edit_quiz', quiz_id=quiz.id) }}" id="edit-quiz-form" enctype="multipart/form-data">
        <!-- Quiz Details Section -->
        <div class="form-section quiz-details-section">
            <h2>Quiz Details</h2>
            <div class="form-group">
                <label for="quiz_title">Quiz Title:</label>
                <input type="text" id="quiz_title" name="quiz_title" value="{{ quiz.title }}" required>
            </div>
            <div class="form-group">
                <label for="quiz_description">Description (Optional):</label>
                <textarea id="quiz_description" name="quiz_description" rows="3">{{ quiz.description }}</textarea>
            </div>
             <div class="form-row">
                <div class="form-group half-width">
                    <label for="time_limit">Time Limit (Minutes):</label>
                    <input type="number" id="time_limit" name="time_limit" value="{{ quiz.time_limit }}" min="1" required>
                </div>
                 <div class="form-group half-width">
                    <label for="difficulty">Difficulty:</label>
                    <select id="difficulty" name="difficulty" required>
                        <option value="easy" {% if quiz.difficulty == 'easy' %}selected{% endif %}>Easy</option>
                        <option value="medium" {% if quiz.difficulty == 'medium' %}selected{% endif %}>Medium</option>
                        <option value="hard" {% if quiz.difficulty == 'hard' %}selected{% endif %}>Hard</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                 <label class="group-label">Grading Thresholds (%):</label>
                 <div class="form-row grading-row">
                     <div class="grade-input">
                         <label for="grade_a">A ≥</label>
                         <input type="number" id="grade_a" name="grade_a" value="{{ quiz.grade_a_threshold }}" min="0" max="100" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_b">B ≥</label>
                         <input type="number" id="grade_b" name="grade_b" value="{{ quiz.grade_b_threshold }}" min="0" max="100" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_c">C ≥</label>
                         <input type="number" id="grade_c" name="grade_c" value="{{ quiz.grade_c_threshold }}" min="0" max="100" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_d">D ≥</label>
                         <input type="number" id="grade_d" name="grade_d" value="{{ quiz.grade_d_threshold }}" min="0" max="100" required>
                     </div>
                 </div>
            </div>

            <!-- Quiz Settings -->
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="randomize_questions" name="randomize_questions" {% if quiz.randomize_questions %}checked{% endif %}>
                    <label for="randomize_questions" class="checkbox-label">
                        <span class="checkbox-text">Randomize question order for each student</span>
                        <span class="checkbox-help">When enabled, each student will see questions in a different random order. Automatically disabled for quizzes with 2 or fewer questions.</span>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="allow_calculator" name="allow_calculator" {% if quiz.allow_calculator %}checked{% endif %}>
                    <label for="allow_calculator" class="checkbox-label">
                        <span class="checkbox-text">Allow calculator during quiz attempts</span>
                        <span class="checkbox-help">When enabled, students will have access to a basic calculator (digits, +, -, ×, ÷) during the quiz. Useful for math quizzes requiring calculations.</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Questions Section -->
        <div class="form-section questions-section">
            <h2>Questions</h2>
            <div id="questions-container">
                {% for question in questions %}
                <div class="question-card" data-question-index="{{ loop.index0 }}">
                     <div class="question-card-header">
                        <span class="question-card-number">Question {{ loop.index }}</span>
                        <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
                     </div>
                    <div class="form-group">
                        <label for="question_{{ loop.index }}">Question Text:</label>
                        <div class="question-input-container">
                            <textarea name="question[]" rows="3" required placeholder="Enter question text... (Math expressions like x^2 + 3x = 4 will be automatically formatted)" class="math-input" data-question-index="{{ loop.index }}">{{ question.question_text }}</textarea>
                            <button type="button" class="verify-expression-btn" onclick="verifyExpression({{ loop.index }})" title="Verify Math Expression">
                                🧠 Verify Expression
                            </button>
                        </div>
                        <input type="hidden" name="question_type[]" value="mcq"> <!-- Always MCQ -->

                        <!-- Expression Evaluation Result -->
                        <div class="expression-result-container" id="expression-result-{{ loop.index }}" style="display: none;">
                            <div class="expression-result-header">
                                <label class="expression-result-label">Expression Evaluation:</label>
                                <div class="expression-operations">
                                    <select class="operation-select" id="operation-select-{{ loop.index }}">
                                        <option value="simplify">Simplify</option>
                                        <option value="expand">Expand</option>
                                        <option value="factor">Factor</option>
                                        <option value="differentiate">Differentiate</option>
                                        <option value="integrate">Integrate</option>
                                        <option value="solve">Solve for x</option>
                                    </select>
                                    <input type="text" class="variable-input" id="variable-input-{{ loop.index }}" placeholder="Variable (x)" value="x" style="display: none;">
                                </div>
                            </div>
                            <div class="expression-result" id="expression-result-content-{{ loop.index }}">
                                <em>Click "Verify Expression" to evaluate...</em>
                            </div>
                        </div>

                        <!-- Math Preview -->
                        <div class="math-preview-container">
                            <label class="math-preview-label">Math Preview:</label>
                            <div class="math-preview" id="math-preview-{{ loop.index }}">
                                <em>Loading preview...</em>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div class="image-upload-container">
                            <label for="question_image_{{ loop.index }}" class="image-upload-label">Attach Image (optional):</label>
                            <input type="file" name="question_image[]" id="question_image_{{ loop.index }}" accept=".jpg,.jpeg,.png" class="image-upload-input">
                            <input type="hidden" name="existing_image[]" value="{{ question.image_filename or '' }}">
                            <div class="file-upload-help">
                                <small>Supported formats: JPG, JPEG, PNG. Max size: 5MB</small>
                            </div>

                            <!-- Existing Image Display -->
                            {% if question.image_filename %}
                            <div class="existing-image-container">
                                <label class="existing-image-label">Current Image:</label>
                                <div class="existing-image-preview">
                                    <img src="{{ url_for('static', filename='uploads/' + question.image_filename) }}" alt="Current Question Image" class="existing-image">
                                    <div class="existing-image-actions">
                                        <label class="checkbox-container">
                                            <input type="checkbox" name="remove_image[]" value="{{ loop.index0 }}">
                                            <span class="checkmark"></span>
                                            Remove current image
                                        </label>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- New Image Preview -->
                            <div class="image-preview" id="image-preview-{{ loop.index }}" style="display: none;">
                                <img src="" alt="Question Image Preview" class="preview-image">
                                <button type="button" class="remove-image-btn" onclick="removeImagePreview({{ loop.index }})">Remove New Image</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-row question-card-footer">
                        <!-- Type dropdown removed -->
                        <div class="form-group question-meta marks-only">
                            <label>Marks:</label>
                            <input type="number" name="question_marks[]" value="{{ question.marks }}" min="1" required class="marks-input" onchange="updateTotalMarks()">
                        </div>
                    </div>

                    <!-- MCQ Fields -->
                    <div class="mcq-fields">
                        <label class="group-label">Options & Correct Answer:</label>
                        <div class="option-group">
                            <span class="option-marker">A</span>
                            <input type="text" name="option1[]" placeholder="Option 1" value="{{ question.option1 }}" required>
                            <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="1" {% if question.correct_answer == '1' %}checked{% endif %} required title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">B</span>
                            <input type="text" name="option2[]" placeholder="Option 2" value="{{ question.option2 }}" required>
                            <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="2" {% if question.correct_answer == '2' %}checked{% endif %} title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">C</span>
                            <input type="text" name="option3[]" placeholder="Option 3" value="{{ question.option3 }}">
                             <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="3" {% if question.correct_answer == '3' %}checked{% endif %} title="Mark as correct">
                        </div>
                        <div class="option-group">
                             <span class="option-marker">D</span>
                            <input type="text" name="option4[]" placeholder="Option 4" value="{{ question.option4 }}">
                             <input type="radio" name="correct_answer[{{ loop.index0 }}]" value="4" {% if question.correct_answer == '4' %}checked{% endif %} title="Mark as correct">
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <button type="button" class="btn btn-outline" id="add-question-btn">+ Add Another Question</button>
        </div>
        
        <input type="hidden" id="total_marks" name="total_marks" value="{{ quiz.total_marks }}">
        <button type="submit" class="btn btn-primary btn-submit-quiz">Update Quiz</button>
    </form>
</div>

<!-- Reuse script from create_quiz.html, ensuring it handles initial data correctly -->
<script>
// Initialize questionIndex based on existing questions
let questionIndex = {{ questions|length }};

function addQuestion() {
    const container = document.getElementById('questions-container');
    const newQuestionCard = document.createElement('div');
    newQuestionCard.classList.add('question-card');
    newQuestionCard.dataset.questionIndex = questionIndex;
    const currentQuestionNumber = container.querySelectorAll('.question-card').length + 1; // Use current count + 1 for numbering
    
    newQuestionCard.innerHTML = `
        <div class="question-card-header">
            <span class="question-card-number">Question ${currentQuestionNumber}</span>
            <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
        </div>
        <div class="form-group">
            <label for="question_${currentQuestionNumber}">Question Text:</label>
            <div class="question-input-container">
                <textarea name="question[]" rows="3" required placeholder="Enter question text... (Math expressions like x^2 + 3x = 4 will be automatically formatted)" class="math-input" data-question-index="${currentQuestionNumber}"></textarea>
                <button type="button" class="verify-expression-btn" onclick="verifyExpression(${currentQuestionNumber})" title="Verify Math Expression">
                    🧠 Verify Expression
                </button>
            </div>
            <input type="hidden" name="question_type[]" value="mcq">

            <!-- Expression Evaluation Result -->
            <div class="expression-result-container" id="expression-result-${currentQuestionNumber}" style="display: none;">
                <div class="expression-result-header">
                    <label class="expression-result-label">Expression Evaluation:</label>
                    <div class="expression-operations">
                        <select class="operation-select" id="operation-select-${currentQuestionNumber}">
                            <option value="simplify">Simplify</option>
                            <option value="expand">Expand</option>
                            <option value="factor">Factor</option>
                            <option value="differentiate">Differentiate</option>
                            <option value="integrate">Integrate</option>
                            <option value="solve">Solve for x</option>
                        </select>
                        <input type="text" class="variable-input" id="variable-input-${currentQuestionNumber}" placeholder="Variable (x)" value="x" style="display: none;">
                    </div>
                </div>
                <div class="expression-result" id="expression-result-content-${currentQuestionNumber}">
                    <em>Click "Verify Expression" to evaluate...</em>
                </div>
            </div>

            <!-- Math Preview -->
            <div class="math-preview-container">
                <label class="math-preview-label">Math Preview:</label>
                <div class="math-preview" id="math-preview-${currentQuestionNumber}">
                    <em>Type math expressions to see preview...</em>
                </div>
            </div>

            <!-- Image Upload -->
            <div class="image-upload-container">
                <label for="question_image_${currentQuestionNumber}" class="image-upload-label">Attach Image (optional):</label>
                <input type="file" name="question_image[]" id="question_image_${currentQuestionNumber}" accept=".jpg,.jpeg,.png" class="image-upload-input">
                <input type="hidden" name="existing_image[]" value="">
                <div class="file-upload-help">
                    <small>Supported formats: JPG, JPEG, PNG. Max size: 5MB</small>
                </div>
                <div class="image-preview" id="image-preview-${currentQuestionNumber}" style="display: none;">
                    <img src="" alt="Question Image Preview" class="preview-image">
                    <button type="button" class="remove-image-btn" onclick="removeImagePreview(${currentQuestionNumber})">Remove Image</button>
                </div>
            </div>
        </div>
        <div class="form-row question-card-footer">
            <div class="form-group question-meta marks-only">
                <label>Marks:</label>
                <input type="number" name="question_marks[]" min="1" value="1" required class="marks-input" onchange="updateTotalMarks()">
            </div>
        </div>
        <div class="mcq-fields">
             <label class="group-label">Options & Correct Answer:</label>
             <div class="option-group">
                 <span class="option-marker">A</span>
                <input type="text" name="option1[]" placeholder="Option 1" required>
                <input type="radio" name="correct_answer[${questionIndex}]" value="1" required title="Mark as correct">
             </div>
             <div class="option-group">
                 <span class="option-marker">B</span>
                <input type="text" name="option2[]" placeholder="Option 2" required>
                 <input type="radio" name="correct_answer[${questionIndex}]" value="2" title="Mark as correct">
             </div>
            <div class="option-group">
                 <span class="option-marker">C</span>
                 <input type="text" name="option3[]" placeholder="Option 3">
                 <input type="radio" name="correct_answer[${questionIndex}]" value="3" title="Mark as correct">
             </div>
             <div class="option-group">
                  <span class="option-marker">D</span>
                 <input type="text" name="option4[]" placeholder="Option 4">
                 <input type="radio" name="correct_answer[${questionIndex}]" value="4" title="Mark as correct">
             </div>
        </div>
    `;
    container.appendChild(newQuestionCard);
    questionIndex++; // Increment the global index AFTER setting the name for the new card
    updateTotalMarks();
}

function removeQuestion(button) {
    const questionCard = button.closest('.question-card');
    questionCard.remove();
    // Renumber remaining questions
    const cards = document.querySelectorAll('#questions-container .question-card');
    cards.forEach((card, index) => {
        card.querySelector('.question-card-number').textContent = `Question ${index + 1}`;
        const radios = card.querySelectorAll('input[type="radio"]');
        // Ensure radio button names use the correct *new* index
        radios.forEach(radio => {
             radio.name = `correct_answer[${index}]`;
        });
        card.dataset.questionIndex = index; // Update the index stored on the element
    });
    // Update the global index to reflect the new count
    questionIndex = cards.length;
    updateTotalMarks();
}

function updateTotalMarks() {
    let total = 0;
    const marksInputs = document.querySelectorAll('.marks-input');
    marksInputs.forEach(input => {
        total += parseInt(input.value) || 0;
    });
    document.getElementById('total_marks').value = total;
}

document.getElementById('add-question-btn').addEventListener('click', addQuestion);

// Initial setup for existing questions
document.addEventListener('DOMContentLoaded', () => {
    updateTotalMarks();
});
</script>

<!-- Link to the same styles as create_quiz.html or copy relevant styles -->
<style>
/* Copy or link styles from create_quiz.html */
/* Improved styling for create quiz page */
body {
    background-color: #f4f7f6; /* Lighter page background */
}

/* Versioning warning styles */
.versioning-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-left: 4px solid #f39c12;
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: #856404;
}

.versioning-warning h3 {
    margin-top: 0;
    color: #e67e22;
    font-size: 1.2rem;
}

.versioning-warning ul {
    margin: 1rem 0 0 0;
    padding-left: 1.5rem;
}

.versioning-warning li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.create-quiz-container {
    max-width: 950px;
    margin: 2rem auto;
    background-color: transparent; /* Container is just for max-width */
    padding: 0; /* Remove padding from main container */
}

.create-quiz-container h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

/* Style for form sections (details, questions) */
.form-section {
    background-color: #fff;
    padding: 2rem 2.5rem;
    border-radius: 10px;
    margin-bottom: 2.5rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
}

.form-section h2 {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.8rem;
    margin-bottom: 2rem;
    color: #007bff; /* Theme color for headings */
    font-size: 1.6rem;
}

/* General form group styling */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.6rem;
    color: #495057;
    font-weight: 600; /* Slightly bolder labels */
    font-size: 0.95rem;
}

label.group-label {
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #343a40;
}

input[type="text"],
input[type="number"],
textarea,
select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 5px;
    box-sizing: border-box;
    font-size: 1rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea {
    resize: vertical;
    min-height: 70px;
}

select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem; /* Make space for arrow */
}

/* Layout helpers */
.form-row {
    display: flex;
    gap: 2rem;
    align-items: flex-end; /* Align items based on their bottom edge */
    margin-bottom: 1.5rem;
}

.form-group.half-width {
    flex: 1 1 50%; /* Allow shrinking but prefer 50% */
}

.grading-row {
    justify-content: space-around;
    gap: 1rem;
}

.grade-input {
     display: flex;
     flex-direction: column; /* Stack label and input */
     align-items: center;
     gap: 0.3rem;
     flex: 1;
     min-width: 60px; /* Prevent shrinking too much */
}
.grade-input label {
    margin-bottom: 0;
    white-space: nowrap;
    font-weight: normal;
    font-size: 0.9rem;
}
.grade-input input {
     width: 100%;
     text-align: center;
}

/* Question Card Styling */
.question-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    position: relative; 
    background-color: #fff;
    border-left: 4px solid #007bff; /* Accent border */
}

.question-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.question-card-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #0056b3;
}

.remove-question-btn {
    background: #f1f3f5;
    color: #dc3545;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 1.4rem;
    font-weight: bold;
    line-height: 26px; 
    text-align: center;
    cursor: pointer;
    padding: 0;
    transition: background-color 0.2s, color 0.2s;
}
.remove-question-btn:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.question-card-footer {
    justify-content: flex-end; /* Push marks to the right */
}

.question-meta.marks-only {
   flex: 0 1 150px; /* Give marks input a reasonable max width */
   max-width: 150px; /* Adjust as needed */
}

/* MCQ Options Styling */
.mcq-fields {
    margin-top: 1.5rem;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
    padding-left: 10px; /* Indent options slightly */
}

.option-marker {
    font-weight: bold;
    color: #6c757d;
    width: 20px; /* Fixed width for alignment */
    text-align: right;
}

.option-group input[type="text"] {
    flex-grow: 1;
}

.option-group input[type="radio"] {
    width: auto;
    margin-left: 10px;
    cursor: pointer;
    transform: scale(1.2); /* Make radio slightly larger */
}

/* Button Styling */
.btn {
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    border: 1px solid transparent;
}

#add-question-btn {
    display: block;
    margin: 1.5rem auto 0 auto; /* Center add button */
}

.btn-primary {
    background-color: #007bff; /* Blue for update */
    color: white;
    border-color: #007bff;
}
.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-outline {
    background-color: transparent;
    color: #007bff;
    border-color: #007bff;
}
.btn-outline:hover {
    background-color: #007bff;
    color: white;
}

.btn-submit-quiz {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    margin-top: 2rem;
}

/* Checkbox styling for randomization */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-top: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    margin-top: 0.25rem;
    transform: scale(1.2);
}

.checkbox-label {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    cursor: pointer;
    margin: 0;
}

.checkbox-text {
    font-weight: 500;
    color: #333;
}

.checkbox-help {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Math Preview Styles */
.math-preview-container {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.math-preview-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.math-preview {
    min-height: 2rem;
    padding: 0.5rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 1rem;
    line-height: 1.5;
}

.math-preview em {
    color: #6c757d;
    font-style: italic;
}

.math-input {
    font-family: 'Courier New', monospace;
}

/* Math rendering improvements */
.tex2jax_process {
    font-size: 1.1em;
}

.math-preview .MathJax {
    font-size: 1.2em !important;
}

</style>

<script>
// Math Preview Functionality (same as create_quiz.html)
function convertToLatex(text) {
    if (!text) return text;

    let result = text;

    // Convert common mathematical expressions to LaTeX
    const conversions = [
        // Exponents
        [/([a-zA-Z0-9]+)\^([a-zA-Z0-9]+)/g, '$1^{$2}'],

        // Square roots
        [/sqrt\(([^)]+)\)/g, '\\sqrt{$1}'],

        // Fractions (simple form like a/b)
        [/([a-zA-Z0-9]+)\/([a-zA-Z0-9]+)/g, '\\frac{$1}{$2}'],

        // Trigonometric functions
        [/sin\(([^)]+)\)/g, '\\sin($1)'],
        [/cos\(([^)]+)\)/g, '\\cos($1)'],
        [/tan\(([^)]+)\)/g, '\\tan($1)'],

        // Logarithmic functions
        [/log\(([^)]+)\)/g, '\\log($1)'],
        [/ln\(([^)]+)\)/g, '\\ln($1)'],

        // Greek letters
        [/\balpha\b/g, '\\alpha'],
        [/\bbeta\b/g, '\\beta'],
        [/\bgamma\b/g, '\\gamma'],
        [/\bdelta\b/g, '\\delta'],
        [/\btheta\b/g, '\\theta'],
        [/\bpi\b/g, '\\pi'],
        [/\bsigma\b/g, '\\sigma'],

        // Special symbols
        [/infinity|inf\b/g, '\\infty'],
        [/\+\/-/g, '\\pm'],
        [/\*/g, '\\cdot'],
        [/<=/g, '\\leq'],
        [/>=/g, '\\geq'],
        [/!=/g, '\\neq'],
    ];

    for (const [pattern, replacement] of conversions) {
        result = result.replace(pattern, replacement);
    }

    return result;
}

function updateMathPreview(textarea) {
    const questionIndex = textarea.dataset.questionIndex;
    const previewDiv = document.getElementById(`math-preview-${questionIndex}`);
    const text = textarea.value.trim();

    if (!text) {
        previewDiv.innerHTML = '<em>Type math expressions to see preview...</em>';
        return;
    }

    // Check if text contains math expressions
    const mathPatterns = [
        /[a-zA-Z]\^[0-9]+/,
        /[0-9]+\^[0-9]+/,
        /sqrt\([^)]+\)/,
        /sin\([^)]+\)/,
        /cos\([^)]+\)/,
        /tan\([^)]+\)/,
        /log\([^)]+\)/,
        /ln\([^)]+\)/,
        /[a-zA-Z]\s*=\s*[^,\n]+/,
        /[0-9]+\s*[+\-*/]\s*[0-9]+/,
        /[a-zA-Z]\s*[+\-*/]\s*[a-zA-Z]/,
        /\([^)]*[+\-*/][^)]*\)/,
    ];

    const hasMath = mathPatterns.some(pattern => pattern.test(text));

    if (hasMath) {
        const latexText = convertToLatex(text);
        previewDiv.innerHTML = `\\(${latexText}\\)`;

        // Re-render MathJax
        if (window.MathJax) {
            MathJax.typesetPromise([previewDiv]).catch(function (err) {
                console.log('MathJax error:', err.message);
                previewDiv.innerHTML = `<em>Math preview: ${text}</em>`;
            });
        }
    } else {
        previewDiv.innerHTML = `<em>Regular text: ${text}</em>`;
    }
}

// Add event listeners for math preview
function addMathPreviewListeners() {
    const mathInputs = document.querySelectorAll('.math-input');
    mathInputs.forEach(textarea => {
        textarea.addEventListener('input', function() {
            updateMathPreview(this);
        });

        // Initial preview
        updateMathPreview(textarea);
    });
}

// Initial setup
document.addEventListener('DOMContentLoaded', () => {
    updateTotalMarks();
    addMathPreviewListeners();
});

// Image Upload Functionality
function setupImageUploadListeners() {
    const imageInputs = document.querySelectorAll('.image-upload-input');
    imageInputs.forEach(input => {
        input.addEventListener('change', function() {
            handleImagePreview(this);
        });
    });
}

function handleImagePreview(input) {
    const questionIndex = input.id.split('_').pop();
    const previewContainer = document.getElementById(`image-preview-${questionIndex}`);
    const previewImage = previewContainer.querySelector('.preview-image');

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            input.value = '';
            return;
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, JPEG, PNG)');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            previewContainer.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        previewContainer.style.display = 'none';
    }
}

function removeImagePreview(questionIndex) {
    const input = document.getElementById(`question_image_${questionIndex}`);
    const previewContainer = document.getElementById(`image-preview-${questionIndex}`);

    input.value = '';
    previewContainer.style.display = 'none';
}

// Override addQuestion to include math preview and image upload setup
const originalAddQuestion = addQuestion;
addQuestion = function() {
    originalAddQuestion();
    // Add math preview and image upload listeners to new question
    setTimeout(() => {
        addMathPreviewListeners();
        setupImageUploadListeners();
    }, 100);
};

// Expression Evaluator Functionality (same as create_quiz.html)
async function verifyExpression(questionIndex) {
    const textarea = document.querySelector(`textarea[data-question-index="${questionIndex}"]`);
    const expression = textarea.value.trim();
    const resultContainer = document.getElementById(`expression-result-${questionIndex}`);
    const resultContent = document.getElementById(`expression-result-content-${questionIndex}`);
    const operationSelect = document.getElementById(`operation-select-${questionIndex}`);
    const variableInput = document.getElementById(`variable-input-${questionIndex}`);
    const verifyBtn = document.querySelector(`button[onclick="verifyExpression(${questionIndex})"]`);

    if (!expression) {
        alert('Please enter a mathematical expression first.');
        return;
    }

    // Show loading state
    verifyBtn.disabled = true;
    verifyBtn.textContent = '🔄 Evaluating...';
    resultContainer.style.display = 'block';
    resultContent.innerHTML = '<em>Evaluating expression...</em>';
    resultContent.className = 'expression-result';

    try {
        const operation = operationSelect.value;
        const variable = variableInput.value || 'x';

        const response = await fetch('/api/evaluate_expression', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expression: expression,
                operation: operation,
                variable: variable
            })
        });

        const data = await response.json();

        if (data.success) {
            displayExpressionResult(questionIndex, data);
        } else {
            displayExpressionError(questionIndex, data.error, data.suggestion);
        }

    } catch (error) {
        displayExpressionError(questionIndex, 'Network error: Could not connect to server', 'Please check your internet connection and try again.');
    } finally {
        // Reset button state
        verifyBtn.disabled = false;
        verifyBtn.textContent = '🧠 Verify Expression';
    }
}

function displayExpressionResult(questionIndex, data) {
    const resultContent = document.getElementById(`expression-result-content-${questionIndex}`);

    let html = `
        <div class="result-original">
            <strong>Original:</strong> ${data.original_expression}
        </div>
        <div class="result-simplified">
            <strong>${data.operation.charAt(0).toUpperCase() + data.operation.slice(1)}:</strong>
            <span class="tex2jax_process">\\(${data.result_latex}\\)</span>
        </div>
    `;

    if (data.variables && data.variables.length > 0) {
        html += `<div class="result-info">Variables: ${data.variables.join(', ')}</div>`;
    }

    if (data.is_constant && data.numerical_value !== null) {
        html += `<div class="result-info">Numerical value: ${data.numerical_value}</div>`;
    }

    if (Array.isArray(data.result_latex)) {
        html = `
            <div class="result-original">
                <strong>Original:</strong> ${data.original_expression}
            </div>
            <div class="result-simplified">
                <strong>Solutions:</strong><br>
        `;
        data.result_latex.forEach((solution, index) => {
            html += `<span class="tex2jax_process">\\(x_{${index+1}} = ${solution}\\)</span><br>`;
        });
        html += '</div>';
    }

    resultContent.innerHTML = html;
    resultContent.className = 'expression-result success';

    // Re-render MathJax
    if (window.MathJax) {
        MathJax.typesetPromise([resultContent]).catch(function (err) {
            console.log('MathJax error:', err.message);
        });
    }
}

function displayExpressionError(questionIndex, error, suggestion) {
    const resultContent = document.getElementById(`expression-result-content-${questionIndex}`);

    let html = `<div><strong>Error:</strong> ${error}</div>`;
    if (suggestion) {
        html += `<div class="result-info"><strong>Suggestion:</strong> ${suggestion}</div>`;
    }

    resultContent.innerHTML = html;
    resultContent.className = 'expression-result error';
}

// Handle operation change to show/hide variable input
function setupExpressionEvaluatorListeners() {
    const operationSelects = document.querySelectorAll('.operation-select');
    operationSelects.forEach(select => {
        select.addEventListener('change', function() {
            const questionIndex = this.id.split('-').pop();
            const variableInput = document.getElementById(`variable-input-${questionIndex}`);
            const operation = this.value;

            if (operation === 'differentiate' || operation === 'integrate' || operation === 'solve') {
                variableInput.style.display = 'inline-block';
            } else {
                variableInput.style.display = 'none';
            }
        });
    });
}

// Initial setup
document.addEventListener('DOMContentLoaded', () => {
    updateTotalMarks();
    addMathPreviewListeners();
    setupImageUploadListeners();
    setupExpressionEvaluatorListeners();
});

// Override addQuestion to include all listeners
const originalAddQuestion = addQuestion;
addQuestion = function() {
    originalAddQuestion();
    // Add all listeners to new question
    setTimeout(() => {
        addMathPreviewListeners();
        setupImageUploadListeners();
        setupExpressionEvaluatorListeners();
    }, 100);
};
</script>

<style>
/* Image Upload Styles (same as create_quiz.html) */
.image-upload-container {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.image-upload-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.image-upload-input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
    font-size: 0.875rem;
}

.image-upload-input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.file-upload-help {
    margin-top: 0.25rem;
}

.file-upload-help small {
    color: #6c757d;
    font-size: 0.75rem;
}

.image-preview {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-align: center;
}

.preview-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.remove-image-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
}

.remove-image-btn:hover {
    background-color: #c82333;
}

/* Existing Image Styles */
.existing-image-container {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.existing-image-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.existing-image-preview {
    text-align: center;
}

.existing-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.existing-image-actions {
    margin-top: 0.5rem;
}

.checkbox-container {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: #dc3545;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Expression Evaluator Styles (same as create_quiz.html) */
.question-input-container {
    display: flex;
    gap: 0.5rem;
    align-items: flex-start;
}

.question-input-container textarea {
    flex: 1;
}

.verify-expression-btn {
    padding: 0.5rem 1rem;
    background-color: #6f42c1;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    white-space: nowrap;
    height: fit-content;
    margin-top: 0.25rem;
    transition: background-color 0.2s ease;
}

.verify-expression-btn:hover {
    background-color: #5a2d91;
}

.verify-expression-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.expression-result-container {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    border-left: 4px solid #6f42c1;
}

.expression-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.expression-result-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
}

.expression-operations {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.operation-select, .variable-input {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.875rem;
    background-color: white;
}

.operation-select:focus, .variable-input:focus {
    border-color: #6f42c1;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
}

.expression-result {
    min-height: 2rem;
    padding: 0.5rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 1rem;
    line-height: 1.5;
}

.expression-result em {
    color: #6c757d;
    font-style: italic;
}

.expression-result.success {
    border-left: 4px solid #28a745;
    background-color: #f8fff9;
}

.expression-result.error {
    border-left: 4px solid #dc3545;
    background-color: #fff8f8;
    color: #721c24;
}

.expression-result .result-original {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.expression-result .result-simplified {
    font-size: 1.1rem;
    color: #28a745;
    font-weight: 500;
}

.expression-result .result-info {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .question-input-container {
        flex-direction: column;
    }

    .verify-expression-btn {
        align-self: flex-start;
        margin-top: 0.5rem;
    }

    .expression-result-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .expression-operations {
        width: 100%;
        justify-content: flex-start;
    }
}
</style>

{% endblock %}