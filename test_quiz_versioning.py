#!/usr/bin/env python3
"""
Test script for Quiz Versioning functionality
Tests the complete versioning workflow including:
1. Creating a quiz
2. Student attempting the quiz
3. Teacher editing the quiz (should create new version)
4. Verifying version integrity
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def login_user(email, password):
    """Login and return session cookies"""
    session = requests.Session()

    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print(f"Failed to get login page: {response.status_code}")
        return None

    # Login
    login_data = {
        'email': email,
        'password': password
    }
    response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200 and "dashboard" in response.url:
        print(f"✅ Successfully logged in as {email}")
        return session
    else:
        print(f"❌ Failed to login as {email}")
        return None

def create_test_quiz(session):
    """Create a test quiz for versioning"""
    import time
    unique_title = f'Versioning Test Quiz {int(time.time())}'
    quiz_data = {
        'quiz_title': unique_title,
        'quiz_description': 'A quiz to test the versioning functionality',
        'time_limit': '30',
        'total_marks': '10',
        'grade_a': '90',
        'grade_b': '80',
        'grade_c': '70',
        'grade_d': '60',
        'difficulty': 'Medium',
        'num_questions': '2',
        'question_type_1': 'mcq',
        'question_text_1': 'What is 2 + 2?',
        'question_marks_1': '5',
        'mcq_option1_1': '3',
        'mcq_option2_1': '4',
        'mcq_option3_1': '5',
        'mcq_option4_1': '6',
        'correct_answer_1': 'option2',
        'question_type_2': 'mcq',
        'question_text_2': 'What is the capital of France?',
        'question_marks_2': '5',
        'mcq_option1_2': 'London',
        'mcq_option2_2': 'Berlin',
        'mcq_option3_2': 'Paris',
        'mcq_option4_2': 'Madrid',
        'correct_answer_2': 'option3'
    }
    
    response = session.post(f"{BASE_URL}/teacher/create-quiz", data=quiz_data)
    if response.status_code == 200:
        print("✅ Test quiz created successfully")
        return unique_title
    else:
        print(f"❌ Failed to create test quiz: {response.status_code}")
        return None

def get_quiz_id_by_title(session, title):
    """Get quiz ID by title from my-quizzes page"""
    response = session.get(f"{BASE_URL}/my-quizzes")
    if response.status_code == 200:
        content = response.text
        # Look for the specific title and extract the quiz ID from the edit link
        import re
        # Find all quiz cards and their edit links
        pattern = r'<h3>([^<]+)</h3>.*?/teacher/edit-quiz/(\d+)'
        matches = re.findall(pattern, content, re.DOTALL)
        for quiz_title, quiz_id in matches:
            if title.strip() in quiz_title.strip():
                return int(quiz_id)
    return None

def attempt_quiz_as_student(session, quiz_id):
    """Attempt the quiz as a student"""
    # Get quiz attempt page
    response = session.get(f"{BASE_URL}/quiz/{quiz_id}/attempt")
    if response.status_code != 200:
        print(f"❌ Failed to access quiz attempt page: {response.status_code}")
        return False
    
    # Submit quiz answers
    attempt_data = {
        'answer_1': 'option2',  # Correct answer for 2+2=4
        'answer_2': 'option3'   # Correct answer for Paris
    }
    
    response = session.post(f"{BASE_URL}/quiz/{quiz_id}/submit", data=attempt_data)
    if response.status_code == 200:
        print("✅ Quiz attempted successfully by student")
        return True
    else:
        print(f"❌ Failed to submit quiz attempt: {response.status_code}")
        return False

def edit_quiz_with_attempts(session, quiz_id, original_title):
    """Edit a quiz that has attempts (should trigger versioning)"""
    edit_data = {
        'quiz_title': f'{original_title} - Updated',
        'quiz_description': 'Updated description after student attempts',
        'time_limit': '45',
        'total_marks': '15',
        'grade_a': '85',
        'grade_b': '75',
        'grade_c': '65',
        'grade_d': '55',
        'difficulty': 'Hard',
        'num_questions': '3',
        'question_type_1': 'mcq',
        'question_text_1': 'What is 3 + 3?',
        'question_marks_1': '5',
        'mcq_option1_1': '5',
        'mcq_option2_1': '6',
        'mcq_option3_1': '7',
        'mcq_option4_1': '8',
        'correct_answer_1': 'option2',
        'question_type_2': 'mcq',
        'question_text_2': 'What is the capital of Germany?',
        'question_marks_2': '5',
        'mcq_option1_2': 'London',
        'mcq_option2_2': 'Berlin',
        'mcq_option3_2': 'Paris',
        'mcq_option4_2': 'Madrid',
        'correct_answer_2': 'option2',
        'question_type_3': 'mcq',
        'question_text_3': 'What is 5 + 5?',
        'question_marks_3': '5',
        'mcq_option1_3': '8',
        'mcq_option2_3': '9',
        'mcq_option3_3': '10',
        'mcq_option4_3': '11',
        'correct_answer_3': 'option3'
    }
    
    response = session.post(f"{BASE_URL}/teacher/edit-quiz/{quiz_id}", data=edit_data)
    if response.status_code == 200:
        print("✅ Quiz edited successfully (versioning should have occurred)")
        return True
    else:
        print(f"❌ Failed to edit quiz: {response.status_code}")
        return False

def verify_versioning(session, original_title):
    """Verify that versioning worked correctly"""
    # Check my-quizzes page for both active and archived versions
    response = session.get(f"{BASE_URL}/my-quizzes")
    if response.status_code == 200:
        content = response.text
        if "Updated" in content:
            print("✅ New version found in active quizzes")
        else:
            print("❌ New version not found in active quizzes")

    # Check archived quizzes
    response = session.get(f"{BASE_URL}/my-quizzes?view=archived")
    if response.status_code == 200:
        content = response.text
        if original_title in content or "Archived" in content:
            print("✅ Original version found in archived quizzes")
        else:
            print("❌ Original version not found in archived quizzes")

def main():
    print("🧪 Starting Quiz Versioning Test")
    print("=" * 50)
    
    # Test as teacher
    print("\n1. Testing as Teacher...")
    teacher_session = login_user("<EMAIL>", "Teacher123!")
    if not teacher_session:
        return
    
    # Create test quiz
    print("\n2. Creating test quiz...")
    quiz_title = create_test_quiz(teacher_session)
    if not quiz_title:
        return

    # Get quiz ID
    quiz_id = get_quiz_id_by_title(teacher_session, quiz_title)
    if not quiz_id:
        print("❌ Could not find created quiz ID")
        return
    print(f"✅ Found quiz ID: {quiz_id}")
    
    # Test as student
    print("\n3. Testing as Student...")
    student_session = login_user("<EMAIL>", "TestPass123!")
    if not student_session:
        return
    
    # Attempt the quiz
    print("\n4. Student attempting quiz...")
    if not attempt_quiz_as_student(student_session, quiz_id):
        return
    
    # Back to teacher - edit the quiz
    print("\n5. Teacher editing quiz with attempts...")
    if not edit_quiz_with_attempts(teacher_session, quiz_id, quiz_title):
        return
    
    # Verify versioning
    print("\n6. Verifying versioning...")
    verify_versioning(teacher_session, quiz_title)
    
    print("\n" + "=" * 50)
    print("🎉 Quiz Versioning Test Complete!")
    print("\nPlease check the web interface to verify:")
    print("- Active quizzes show the updated version")
    print("- Archived quizzes show the original version")
    print("- Version numbers are displayed correctly")
    print("- Student attempts are preserved with original quiz")

if __name__ == "__main__":
    main()
