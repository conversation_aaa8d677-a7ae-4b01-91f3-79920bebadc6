# Quiz Management System

## Overview
The Quiz Management System is a web application built using Flask that allows users to create, manage, and take quizzes. It provides a user-friendly interface for both quiz creators and participants.

## Project Structure
```
quiz-management-system
├── src
│   ├── app.py
│   ├── controllers
│   │   └── __init__.py
│   ├── models
│   │   └── __init__.py
│   ├── routes
│   │   └── __init__.py
│   └── utils
│       └── __init__.py
├── requirements.txt
└── README.md
```

## Installation
To set up the project, follow these steps:

1. Clone the repository:
   ```
   git clone <repository-url>
   cd quiz-management-system
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows:
     ```
     venv\Scripts\activate
     ```
   - On macOS/Linux:
     ```
     source venv/bin/activate
     ```

4. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage
To run the application, execute the following command:
```
python src/app.py
```

The application will start on `http://127.0.0.1:5000/`.

## Testing
To run the tests, use:
```
pytest
```

## Contributing
Contributions are welcome! Please open an issue or submit a pull request for any improvements or bug fixes.

## License
This project is licensed under the MIT License.