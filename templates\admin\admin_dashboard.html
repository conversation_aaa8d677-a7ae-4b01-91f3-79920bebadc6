{% extends "base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="container admin-container">
    <div class="admin-header">
        <h1>Admin Dashboard</h1>
        <div class="admin-actions">
            <a href="{{ url_for('logout') }}" class="btn btn-danger">Logout</a>
        </div>
    </div>

    <div class="admin-content">
        <div class="admin-grid">
            <!-- User Management -->
            <div class="admin-card">
                <div class="card-icon">👥</div>
                <h2>User Management</h2>
                <p>Manage users, roles, and permissions</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_manage_users') }}" class="btn btn-primary">Manage Users</a>
                </div>
            </div>

            <!-- Pending Users -->
            <div class="admin-card">
                <div class="card-icon">🔔</div>
                <h2>Pending Users</h2>
                <p>Verify or reject new user registrations</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_pending_users') }}" class="btn btn-primary">Manage Pending Users</a>
                </div>
            </div>

            <!-- Create Quiz -->
            <div class="admin-card">
                <div class="card-icon">✏️</div>
                <h2>Create Quiz</h2>
                <p>Design and publish a new quiz for students</p>
                <div class="card-actions">
                    <a href="{{ url_for('create_quiz') }}" class="btn btn-primary">Create New Quiz</a>
                </div>
            </div>

            <!-- My Quizzes -->
            <div class="admin-card">
                <div class="card-icon">📚</div>
                <h2>My Quizzes</h2>
                <p>View, edit, or delete your created quizzes</p>
                <div class="card-actions">
                    <a href="{{ url_for('my_quizzes') }}" class="btn btn-primary">Manage My Quizzes</a>
                </div>
            </div>

            <!-- Quiz Management -->
            <div class="admin-card">
                <div class="card-icon">📝</div>
                <h2>All Quizzes</h2>
                <p>View and manage all quizzes in the system</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_manage_quizzes') }}" class="btn btn-primary">View All Quizzes</a>
                </div>
            </div>

            <!-- Quiz Attempts -->
            <div class="admin-card">
                <div class="card-icon">📋</div>
                <h2>Quiz Attempts</h2>
                <p>View and filter all quiz attempts by students</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_quiz_attempts') }}" class="btn btn-primary">View Attempts</a>
                </div>
            </div>

            <!-- Statistics -->
            <div class="admin-card">
                <div class="card-icon">📊</div>
                <h2>Statistics</h2>
                <p>View system statistics and activity</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_statistics') }}" class="btn btn-primary">View Statistics</a>
                </div>
            </div>

            <!-- Messages -->
            <div class="admin-card">
                <div class="card-icon">💬</div>
                <h2>Messages</h2>
                <p>Communicate with students and parents</p>
                <div class="card-actions">
                    <a href="{{ url_for('inbox') }}" class="btn btn-primary">Inbox</a>
                    <a href="{{ url_for('compose_to_student_parent') }}" class="btn btn-success">Send to Student & Parent</a>
                </div>
            </div>

            <!-- System Settings -->
            <div class="admin-card">
                <div class="card-icon">⚙️</div>
                <h2>System Settings</h2>
                <p>Configure system-wide settings</p>
                <div class="card-actions">
                    <a href="{{ url_for('admin_settings') }}" class="btn btn-primary">Manage Settings</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin: 0;
    color: #2c3e50;
}

.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.admin-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 2rem;
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.admin-card h2 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.5rem;
}

.admin-card p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.card-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    flex: 1;
    text-align: center;
    min-width: 120px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}
</style>
{% endblock %}