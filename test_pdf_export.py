#!/usr/bin/env python3
"""
PDF Report Card Export Test Suite
=================================

This script tests the PDF export functionality for student report cards,
accessible to both teachers and parents.
"""

import requests
import os
import sys
import time
from urllib.parse import urljoin

# Configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_DELAY = 1  # seconds between tests

# Test user credentials
test_users = {
    'teacher': {
        'email': '<EMAIL>',
        'password': 'Teacher123!'
    },
    'parent': {
        'email': '<EMAIL>',
        'password': 'Pass@123'
    },
    'student': {
        'email': '<EMAIL>',
        'password': 'student123'
    }
}

class TestSession:
    def __init__(self):
        self.session = requests.Session()

def print_step(message):
    print(f"✓ {message}")

def print_error(message):
    print(f"✗ {message}")

def print_test_header(title):
    print(f"\n{'='*50}")
    print(f"=== {title} ===")
    print('='*50)

def login_user(session, email, password, expected_role):
    """Login a user and verify successful authentication"""
    try:
        # Get login page first
        response = session.get(f"{BASE_URL}/login")
        if response.status_code != 200:
            print_error(f"Failed to access login page: {response.status_code}")
            return False
        
        # Submit login form
        login_data = {
            'email': email,
            'password': password
        }
        
        response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=True)
        
        if response.status_code == 200:
            # Check if we're redirected to dashboard (successful login)
            if f"{expected_role}_dashboard" in response.url or "dashboard" in response.url:
                print_step(f"Successfully logged in as {expected_role}: {email}")
                return True
            else:
                print_error(f"Login failed - unexpected redirect: {response.url}")
                return False
        else:
            print_error(f"Login failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Login error: {e}")
        return False

def test_database_structure():
    """Test if database has the required data for PDF export testing"""
    print_step("Testing database structure and data availability")
    
    try:
        import sqlite3
        
        # Check if database exists
        db_path = 'instance/quiz.db'
        if not os.path.exists(db_path):
            print_error("Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check for students
        cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'student'")
        student_count = cursor.fetchone()[0]
        print_step(f"Found {student_count} students")
        
        # Check for quiz attempts
        cursor.execute("SELECT COUNT(*) FROM quiz_attempt")
        attempt_count = cursor.fetchone()[0]
        print_step(f"Found {attempt_count} quiz attempts")
        
        # Check for teachers
        cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'teacher'")
        teacher_count = cursor.fetchone()[0]
        print_step(f"Found {teacher_count} teachers")
        
        # Check for parents
        cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'parent'")
        parent_count = cursor.fetchone()[0]
        print_step(f"Found {parent_count} parents")
        
        # Get sample student with attempts for testing
        cursor.execute("""
            SELECT DISTINCT u.id, u.name, u.email 
            FROM user u 
            JOIN quiz_attempt qa ON u.id = qa.student_id 
            WHERE u.role = 'student' 
            LIMIT 1
        """)
        sample_student = cursor.fetchone()
        
        if sample_student:
            print_step(f"Sample student for testing: {sample_student[1]} (ID: {sample_student[0]})")
            conn.close()
            return sample_student[0]  # Return student ID
        else:
            print_error("No students with quiz attempts found")
            conn.close()
            return False
            
    except Exception as e:
        print_error(f"Database check failed: {e}")
        return False

def test_teacher_pdf_export(session, student_id):
    """Test teacher PDF export functionality"""
    print_step("Testing teacher PDF export access")
    
    try:
        # Access student reports management page
        response = session.get(f"{BASE_URL}/teacher/student-reports")
        if response.status_code != 200:
            print_error(f"Failed to access student reports page: {response.status_code}")
            return False
        
        print_step("Successfully accessed student reports management page")
        
        # Check for PDF export button in the page
        if "Export PDF" in response.text:
            print_step("Found PDF export button on student reports page")
        else:
            print_error("PDF export button not found on student reports page")
            return False
        
        # Test PDF export download
        export_url = f"{BASE_URL}/teacher/export-student-report/{student_id}"
        response = session.get(export_url)
        
        if response.status_code == 200:
            # Check if response is a PDF
            content_type = response.headers.get('Content-Type', '')
            if 'application/pdf' in content_type:
                print_step(f"Successfully downloaded PDF report (Size: {len(response.content)} bytes)")
                
                # Check Content-Disposition header
                content_disposition = response.headers.get('Content-Disposition', '')
                if 'attachment' in content_disposition and '.pdf' in content_disposition:
                    print_step("PDF has correct download headers")
                    return True
                else:
                    print_error("PDF missing proper download headers")
                    return False
            else:
                print_error(f"Response is not a PDF: {content_type}")
                return False
        else:
            print_error(f"PDF export failed: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Teacher PDF export test failed: {e}")
        return False

def test_parent_pdf_export(session, student_id):
    """Test parent PDF export functionality"""
    print_step("Testing parent PDF export access")
    
    try:
        # Access parent dashboard first
        response = session.get(f"{BASE_URL}/parent/dashboard")
        if response.status_code != 200:
            print_error(f"Failed to access parent dashboard: {response.status_code}")
            return False
        
        print_step("Successfully accessed parent dashboard")
        
        # Access child's report card
        response = session.get(f"{BASE_URL}/parent/report-card/{student_id}")
        if response.status_code != 200:
            print_error(f"Failed to access child's report card: {response.status_code}")
            return False
        
        print_step("Successfully accessed child's report card")
        
        # Check for PDF export button
        if "Download PDF Report" in response.text:
            print_step("Found PDF export button on report card page")
        else:
            print_error("PDF export button not found on report card page")
            return False
        
        # Test PDF export download
        export_url = f"{BASE_URL}/parent/export-report-card/{student_id}"
        response = session.get(export_url)
        
        if response.status_code == 200:
            # Check if response is a PDF
            content_type = response.headers.get('Content-Type', '')
            if 'application/pdf' in content_type:
                print_step(f"Successfully downloaded PDF report (Size: {len(response.content)} bytes)")
                
                # Check Content-Disposition header
                content_disposition = response.headers.get('Content-Disposition', '')
                if 'attachment' in content_disposition and '.pdf' in content_disposition:
                    print_step("PDF has correct download headers")
                    return True
                else:
                    print_error("PDF missing proper download headers")
                    return False
            else:
                print_error(f"Response is not a PDF: {content_type}")
                return False
        else:
            print_error(f"PDF export failed: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Parent PDF export test failed: {e}")
        return False

def main():
    """Main test execution"""
    print("PDF Report Card Export Test Suite")
    print("=" * 50)
    print("🧪 Starting PDF Export Tests")
    
    all_tests_passed = True
    
    # Test 1: Database structure
    print_test_header("Database Structure Tests")
    student_id = test_database_structure()
    if not student_id:
        print_error("Database structure test failed - cannot continue")
        return False
    
    # Test 2: Teacher functionality
    print_test_header("Teacher PDF Export Tests")
    session = TestSession()
    
    if login_user(session.session, test_users['teacher']['email'], 
                  test_users['teacher']['password'], 'teacher'):
        
        if not test_teacher_pdf_export(session.session, student_id):
            all_tests_passed = False
        time.sleep(TEST_DELAY)
    else:
        all_tests_passed = False
    
    # Test 3: Parent functionality
    print_test_header("Parent PDF Export Tests")
    session = TestSession()
    
    if login_user(session.session, test_users['parent']['email'], 
                  test_users['parent']['password'], 'parent'):
        
        if not test_parent_pdf_export(session.session, student_id):
            all_tests_passed = False
        time.sleep(TEST_DELAY)
    else:
        all_tests_passed = False
    
    # Final results
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All PDF export tests passed!")
        print("✅ PDF report card export functionality is working correctly.")
    else:
        print("❌ Some tests failed!")
        print("Please check the error messages above.")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
