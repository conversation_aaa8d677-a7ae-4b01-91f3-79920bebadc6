{% extends "base.html" %}

{% block title %}View Quiz: {{ quiz.title }}{% endblock %}

{% block content %}
<div class="container view-quiz-container">
    <div class="quiz-header">
        <h1>{{ quiz.get_display_title() }}</h1>
        <div class="header-actions">
            {% if not quiz.is_active %}
                <span class="status-badge archived">Archived Version</span>
            {% endif %}
            <a href="{{ url_for('my_quizzes') }}" class="btn btn-secondary">Back to My Quizzes</a>
        </div>
    </div>

    {% if not quiz.is_active %}
    <div class="alert alert-info archived-notice">
        <h3>📚 Archived Quiz Version</h3>
        <p>This is an archived version of the quiz that is no longer active. It was preserved to maintain the integrity of past student attempts.</p>
        <p><strong>Students cannot attempt this version.</strong> Only the latest active version is available for new attempts.</p>
    </div>
    {% endif %}

    <div class="quiz-meta">
        <span class="meta-item"><strong>Version:</strong> {{ quiz.version }}</span>
        <span class="meta-item"><strong>Status:</strong>
            {% if quiz.is_active %}
                <span class="status-active">Active</span>
            {% else %}
                <span class="status-inactive">Archived</span>
            {% endif %}
        </span>
        <span class="meta-item"><strong>Difficulty:</strong> <span class="difficulty-badge {{ quiz.difficulty }}">{{ quiz.difficulty }}</span></span>
        <span class="meta-item"><strong>Time Limit:</strong> {{ quiz.time_limit }} minutes</span>
        <span class="meta-item"><strong>Total Marks:</strong> {{ quiz.total_marks }}</span>
    </div>
    {% if quiz.description %}
        <div class="quiz-description-view">
            <strong>Description:</strong>
            <p>{{ quiz.description }}</p>
        </div>
    {% endif %}

    <div class="questions-view-section">
        <h2>Questions</h2>
        {% for question in questions %}
            <div class="question-view-card">
                <div class="question-view-header">
                    <span class="question-view-number">Question {{ loop.index }}</span>
                    <span class="question-view-marks">({{ question.marks }} Marks)</span>
                </div>
                <p class="question-view-text tex2jax_process">{{ question.question_text | process_math | safe }}</p>

                <!-- Question Image -->
                {% if question.image_filename %}
                <div class="question-image-container">
                    <img src="{{ url_for('static', filename='uploads/' + question.image_filename) }}"
                         alt="Question Image"
                         class="question-image"
                         onclick="openImageModal(this.src)">
                </div>
                {% endif %}

                <!-- Assuming MCQ type -->
                <div class="options-view">
                    <p><strong>Options:</strong></p>
                    <ul>
                        <li {% if question.correct_answer == '1' %}class="correct-answer"{% endif %} class="tex2jax_process">A. {{ question.option1 | process_math | safe }}</li>
                        <li {% if question.correct_answer == '2' %}class="correct-answer"{% endif %} class="tex2jax_process">B. {{ question.option2 | process_math | safe }}</li>
                        {% if question.option3 %}
                            <li {% if question.correct_answer == '3' %}class="correct-answer"{% endif %} class="tex2jax_process">C. {{ question.option3 | process_math | safe }}</li>
                        {% endif %}
                        {% if question.option4 %}
                            <li {% if question.correct_answer == '4' %}class="correct-answer"{% endif %} class="tex2jax_process">D. {{ question.option4 | process_math | safe }}</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        {% else %}
            <p>No questions found for this quiz.</p>
        {% endfor %}
    </div>
</div>

<style>
.view-quiz-container {
    max-width: 900px;
    margin: 2rem auto;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.quiz-header h1 {
    margin: 0;
    color: #343a40;
}

.quiz-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    font-size: 0.95rem;
}

.meta-item strong {
    color: #495057;
}

.difficulty-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 10px;
    font-weight: bold;
    font-size: 0.85rem;
    text-transform: capitalize;
}
.difficulty-badge.easy { background-color: #d4edda; color: #155724; }
.difficulty-badge.medium { background-color: #fff3cd; color: #856404; }
.difficulty-badge.hard { background-color: #f8d7da; color: #721c24; }

.quiz-description-view {
    margin-bottom: 2rem;
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.quiz-description-view p {
    margin-top: 0.5rem;
    color: #6c757d;
    line-height: 1.6;
}

.questions-view-section h2 {
    margin-bottom: 1.5rem;
    color: #007bff;
}

.question-view-card {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-left: 4px solid #17a2b8; /* Different accent color */
    border-radius: 8px;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
}

.question-view-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 0.8rem;
}

.question-view-number {
    font-weight: bold;
    font-size: 1.1em;
    color: #17a2b8;
}

.question-view-marks {
    font-size: 0.9em;
    color: #6c757d;
}

.question-view-text {
    margin-bottom: 1.2rem;
    font-size: 1.1em;
    color: #212529;
}

.options-view ul {
    list-style: none;
    padding-left: 1.5rem;
    margin-top: 0.5rem;
}

.options-view li {
    padding: 0.3rem 0;
    color: #495057;
}

.options-view li.correct-answer {
    font-weight: bold;
    color: #28a745; /* Green for correct answer */
}
.options-view li.correct-answer::before {
    content: "✓ "; /* Checkmark for correct answer */
    color: #28a745;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}
.btn-secondary:hover {
    background-color: #5a6268;
}

/* Versioning styles */
.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.status-badge.archived {
    background-color: #6c757d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
}

.archived-notice {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-left: 4px solid #17a2b8;
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: #0c5460;
}

.archived-notice h3 {
    margin-top: 0;
    color: #17a2b8;
    font-size: 1.2rem;
}

.status-active {
    color: #28a745;
    font-weight: 600;
}

.status-inactive {
    color: #6c757d;
    font-weight: 600;
}

/* Question Image Styles */
.question-image-container {
    margin: 1rem 0;
    text-align: center;
}

.question-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.question-image:hover {
    transform: scale(1.02);
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    cursor: pointer;
}

.image-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
    border-radius: 8px;
}

.image-modal-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.image-modal-close:hover {
    opacity: 0.7;
}
</style>

<!-- Image Modal -->
<div id="image-modal" class="image-modal" onclick="closeImageModal()">
    <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
    <img class="image-modal-content" id="modal-image" src="" alt="Question Image">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize MathJax rendering for math expressions
    if (window.MathJax) {
        MathJax.typesetPromise().then(() => {
            console.log('MathJax rendering complete for quiz view');
        }).catch((err) => {
            console.log('MathJax error:', err.message);
        });
    }
});

// Image Modal Functions
function openImageModal(imageSrc) {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    modal.style.display = 'block';
    modalImage.src = imageSrc;
}

function closeImageModal() {
    const modal = document.getElementById('image-modal');
    modal.style.display = 'none';
}

// Close modal when clicking outside the image or pressing Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeImageModal();
    }
});
</script>

{% endblock %}