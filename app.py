from flask import Flask, render_template, request, redirect, url_for, session, jsonify, flash, make_response, send_file
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from flask_mail import Mail, Message as MailMessage
import os
import uuid
from datetime import datetime, timedelta
import re # Import regex module
import traceback # Import traceback module
from sqlalchemy import or_, and_
from functools import wraps
import random
import smtplib
import string
import sympy as sp
from sympy import symbols, sympify, simplify, expand, factor, diff, integrate, solve, latex
import json

# Try to import WeasyPrint, fall back to alternative if not available
try:
    import weasyprint
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError) as e:
    WEASYPRINT_AVAILABLE = False
    print(f"WeasyPrint not available: {e}. PDF generation will use alternative method.")
import json
import csv
import io
import zipfile
import sqlite3
from itsdangerous import URLSafeTimedSerializer
# PDF generation imports
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

app = Flask(__name__)
# Use a fixed secret key for session security
app.secret_key = 'your-secret-key-here'  # Replace with a secure secret key
# SQLite database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///quiz.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)  # Session lasts for 7 days

# Flask-Mail configuration
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'  # Replace with your Gmail address
app.config['MAIL_PASSWORD'] = 'rgxydmdmisnurhay'  # Replace with your 16-character app password
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'  # Replace with your Gmail address

# File upload configuration
app.config['UPLOAD_FOLDER'] = os.path.join('static', 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 5 * 1024 * 1024  # 5 MB max file size
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

# Create uploads directory if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

mail = Mail(app)
db = SQLAlchemy(app)

# Add custom Jinja2 filters for math processing
@app.template_filter('process_math')
def process_math_filter(text):
    """Jinja2 filter to process text for math expressions"""
    return process_text_for_math(text)

@app.template_filter('wrap_math')
def wrap_math_filter(text, inline=True):
    """Jinja2 filter to wrap text with math delimiters"""
    return wrap_math_expressions(text, inline)

# Add this after app configuration
app.config['SECURITY_PASSWORD_SALT'] = 'your-security-password-salt'  # Change this to a random string
serializer = URLSafeTimedSerializer(app.config['SECRET_KEY'])

# Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100))
    email = db.Column(db.String(100), unique=True)
    password = db.Column(db.String(255))  # Hashed password
    unhashed_password = db.Column(db.String(255))  # Store unhashed password
    role = db.Column(db.String(10))  # admin, teacher, student, parent
    parent_email = db.Column(db.String(100), nullable=True)
    report_comment = db.Column(db.Text, nullable=True) # New field for teacher comments
    is_verified = db.Column(db.Boolean, default=False) # New field for admin verification
    verification_token = db.Column(db.String(100), nullable=True) # Token for verification

    def get_parent(self):
        """Get the parent user for this student (if role is student and parent_email is set)"""
        if self.role == 'student' and self.parent_email:
            return User.query.filter_by(email=self.parent_email, role='parent').first()
        return None

    def get_children(self):
        """Get all children for this parent (if role is parent)"""
        if self.role == 'parent':
            return User.query.filter_by(parent_email=self.email, role='student').all()
        return []

class Quiz(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    time_limit = db.Column(db.Integer, nullable=False)
    total_marks = db.Column(db.Integer, nullable=False)
    grade_a_threshold = db.Column(db.Integer, nullable=False)
    grade_b_threshold = db.Column(db.Integer, nullable=False)
    grade_c_threshold = db.Column(db.Integer, nullable=False)
    grade_d_threshold = db.Column(db.Integer, nullable=False)
    difficulty = db.Column(db.String(20), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    teacher_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Versioning fields
    version = db.Column(db.Integer, default=1, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    original_quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'), nullable=True)

    # Randomization field
    randomize_questions = db.Column(db.Boolean, default=True, nullable=False)

    # Calculator field
    allow_calculator = db.Column(db.Boolean, default=False, nullable=False)

    # Relationships
    questions = db.relationship('Question', backref='quiz', lazy=True)
    teacher = db.relationship('User', backref='quizzes', lazy=True)

    # Self-referential relationship for versioning
    versions = db.relationship('Quiz',
                              backref=db.backref('original_quiz', remote_side=[id]),
                              foreign_keys=[original_quiz_id])

    def get_latest_version(self):
        """Get the latest active version of this quiz"""
        if self.original_quiz_id is None:
            # This is the original quiz, find its latest version
            latest = Quiz.query.filter_by(
                original_quiz_id=self.id,
                is_active=True
            ).order_by(Quiz.version.desc()).first()
            return latest if latest else self
        else:
            # This is a version, find the latest version of the original
            return self.original_quiz.get_latest_version()

    def get_all_versions(self):
        """Get all versions of this quiz (including original)"""
        if self.original_quiz_id is None:
            # This is the original quiz
            versions = Quiz.query.filter_by(original_quiz_id=self.id).order_by(Quiz.version.asc()).all()
            return [self] + versions
        else:
            # This is a version, get all versions of the original
            return self.original_quiz.get_all_versions()

    def get_display_title(self):
        """Get title with version number for display"""
        if self.version > 1:
            return f"{self.title} - v{self.version}"
        return self.title

class Question(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'))
    question_text = db.Column(db.String(500))
    question_type = db.Column(db.String(20))  # mcq, true_false
    option1 = db.Column(db.String(200), nullable=True)
    option2 = db.Column(db.String(200), nullable=True)
    option3 = db.Column(db.String(200), nullable=True)
    option4 = db.Column(db.String(200), nullable=True)
    correct_answer = db.Column(db.String(500))  # For MCQ: option number, for true/false: true/false
    marks = db.Column(db.Integer)
    image_filename = db.Column(db.String(255), nullable=True)  # Store uploaded image filename

class QuizAttempt(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'), nullable=False)
    score = db.Column(db.Float, nullable=False)
    submitted_at = db.Column(db.DateTime, default=datetime.utcnow)
    question_order = db.Column(db.Text, nullable=True)  # JSON string storing randomized question IDs order
    answers = db.relationship('QuizAnswer', backref='attempt', lazy=True)
    quiz = db.relationship('Quiz', backref='attempts', lazy=True)
    student = db.relationship('User', backref='quiz_attempts', lazy=True)

class QuizAnswer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    attempt_id = db.Column(db.Integer, db.ForeignKey('quiz_attempt.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('question.id'), nullable=False)
    selected_answer = db.Column(db.String(255), nullable=False)
    is_correct = db.Column(db.Boolean, nullable=False)

class ParentComment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'), nullable=False)
    attempt_id = db.Column(db.Integer, db.ForeignKey('quiz_attempt.id'), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    comment_text = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    student = db.relationship('User', foreign_keys=[student_id], backref='parent_comments_received', lazy=True)
    parent = db.relationship('User', foreign_keys=[parent_id], backref='parent_comments_made', lazy=True)
    quiz = db.relationship('Quiz', backref='parent_comments', lazy=True)
    attempt = db.relationship('QuizAttempt', backref='parent_comments', lazy=True)

class StudentComment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'), nullable=False)
    attempt_id = db.Column(db.Integer, db.ForeignKey('quiz_attempt.id'), nullable=True)  # Optional link to specific attempt
    comment_text = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    student = db.relationship('User', foreign_keys=[student_id], backref='student_comments_made', lazy=True)
    quiz = db.relationship('Quiz', backref='student_comments', lazy=True)
    attempt = db.relationship('QuizAttempt', backref='student_comments', lazy=True)

class TeacherReply(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'), nullable=False)
    parent_comment_id = db.Column(db.Integer, db.ForeignKey('parent_comment.id'), nullable=True)
    student_comment_id = db.Column(db.Integer, db.ForeignKey('student_comment.id'), nullable=True)
    reply_text = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    teacher = db.relationship('User', foreign_keys=[teacher_id], backref='teacher_replies_made', lazy=True)
    quiz = db.relationship('Quiz', backref='teacher_replies', lazy=True)
    parent_comment = db.relationship('ParentComment', backref='teacher_replies', lazy=True)
    student_comment = db.relationship('StudentComment', backref='teacher_replies', lazy=True)

# --- NEW Email-Style Message Model ---
class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subject = db.Column(db.String(200), nullable=True) # Optional subject
    body = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    is_read = db.Column(db.Boolean, default=False, nullable=False)
    thread_id = db.Column(db.String(50), nullable=True, index=True)  # For grouping related messages
    message_type = db.Column(db.String(20), default='normal', nullable=False)  # normal, student_parent_broadcast

    # Relationships to easily get sender/receiver user objects
    # Use backref to define User.sent_messages and User.received_messages
    sender = db.relationship('User', foreign_keys=[sender_id], backref=db.backref('sent_messages', lazy='dynamic'))
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref=db.backref('received_messages', lazy='dynamic'))

    def get_thread_messages(self):
        """Get all messages in the same thread"""
        if not self.thread_id:
            return [self]
        return Message.query.filter_by(thread_id=self.thread_id).order_by(Message.timestamp.asc()).all()

    def mark_as_read(self):
        """Mark this message as read"""
        if not self.is_read:
            self.is_read = True
            try:
                db.session.commit()
                return True
            except Exception as e:
                db.session.rollback()
                print(f"Error marking message as read: {e}")
                return False
        return True

# === WORKSHEET GENERATION MODELS ===

class Topic(db.Model):
    """Model for organizing questions by topic/chapter"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    worksheet_topics = db.relationship('WorksheetTopic', backref='topic', lazy=True)

class WorksheetTemplate(db.Model):
    """Model for saving worksheet configurations"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    difficulty_level = db.Column(db.Integer, nullable=False)  # 1=Easy, 2=Medium, 3=Hard
    question_count = db.Column(db.Integer, nullable=False)
    include_answers = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    teacher = db.relationship('User', backref='worksheet_templates', lazy=True)
    topics = db.relationship('WorksheetTopic', backref='worksheet_template', lazy=True, cascade='all, delete-orphan')
    generated_worksheets = db.relationship('GeneratedWorksheet', backref='template', lazy=True)

class WorksheetTopic(db.Model):
    """Many-to-many relationship between worksheets and topics"""
    id = db.Column(db.Integer, primary_key=True)
    worksheet_template_id = db.Column(db.Integer, db.ForeignKey('worksheet_template.id'), nullable=False)
    topic_id = db.Column(db.Integer, db.ForeignKey('topic.id'), nullable=False)

class GeneratedWorksheet(db.Model):
    """Model for tracking generated worksheets"""
    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('worksheet_template.id'), nullable=True)
    teacher_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    question_ids = db.Column(db.Text, nullable=False)  # JSON string of question IDs
    difficulty_level = db.Column(db.Integer, nullable=False)
    include_answers = db.Column(db.Boolean, default=False)
    generated_at = db.Column(db.DateTime, default=datetime.utcnow)
    download_count = db.Column(db.Integer, default=0)

    # Relationships
    teacher = db.relationship('User', backref='generated_worksheets', lazy=True)

# === MATH EXPRESSION HELPER FUNCTIONS ===

def detect_math_expressions(text):
    """
    Detect potential math expressions in text and return positions
    """
    if not text:
        return []

    # Patterns that suggest mathematical content
    math_patterns = [
        r'[a-zA-Z]\^[0-9]+',  # Variables with exponents like x^2
        r'[0-9]+\^[0-9]+',    # Numbers with exponents like 2^3
        r'[a-zA-Z]\^[a-zA-Z]', # Variables with variable exponents like x^n
        r'sqrt\([^)]+\)',     # Square root functions
        r'sin\([^)]+\)',      # Trigonometric functions
        r'cos\([^)]+\)',
        r'tan\([^)]+\)',
        r'log\([^)]+\)',      # Logarithmic functions
        r'ln\([^)]+\)',
        r'[a-zA-Z]\s*=\s*[^,\n]+',  # Equations like x = 5
        r'[0-9]+\s*[+\-*/]\s*[0-9]+', # Basic arithmetic
        r'[a-zA-Z]\s*[+\-*/]\s*[a-zA-Z]', # Variable arithmetic
        r'\([^)]*[+\-*/][^)]*\)',  # Expressions in parentheses
        r'[a-zA-Z][0-9]*\s*[+\-*/]\s*[a-zA-Z][0-9]*', # Terms like 2x + 3y
        r'[0-9]*[a-zA-Z]\^?[0-9]*\s*[+\-*/]', # Polynomial terms
        r'\\frac\{[^}]+\}\{[^}]+\}', # LaTeX fractions
        r'\\sqrt\{[^}]+\}',   # LaTeX square roots
    ]

    matches = []
    for pattern in math_patterns:
        for match in re.finditer(pattern, text, re.IGNORECASE):
            matches.append({
                'start': match.start(),
                'end': match.end(),
                'text': match.group(),
                'pattern': pattern
            })

    return matches

def convert_to_latex(text):
    """
    Convert plain text math expressions to LaTeX format
    """
    if not text:
        return text

    # Create a copy to work with
    result = text

    # Convert common mathematical expressions to LaTeX
    conversions = [
        # Exponents
        (r'([a-zA-Z0-9]+)\^([a-zA-Z0-9]+)', r'\1^{\2}'),
        (r'([a-zA-Z0-9]+)\^([a-zA-Z0-9]+)', r'\1^{\2}'),

        # Square roots
        (r'sqrt\(([^)]+)\)', r'\\sqrt{\1}'),

        # Fractions (simple form like a/b)
        (r'([a-zA-Z0-9]+)/([a-zA-Z0-9]+)', r'\\frac{\1}{\2}'),

        # Trigonometric functions
        (r'sin\(([^)]+)\)', r'\\sin(\1)'),
        (r'cos\(([^)]+)\)', r'\\cos(\1)'),
        (r'tan\(([^)]+)\)', r'\\tan(\1)'),

        # Logarithmic functions
        (r'log\(([^)]+)\)', r'\\log(\1)'),
        (r'ln\(([^)]+)\)', r'\\ln(\1)'),

        # Greek letters (common ones)
        (r'\balpha\b', r'\\alpha'),
        (r'\bbeta\b', r'\\beta'),
        (r'\bgamma\b', r'\\gamma'),
        (r'\bdelta\b', r'\\delta'),
        (r'\btheta\b', r'\\theta'),
        (r'\bpi\b', r'\\pi'),
        (r'\bsigma\b', r'\\sigma'),

        # Infinity
        (r'\binfinity\b', r'\\infty'),
        (r'\binf\b', r'\\infty'),

        # Plus/minus
        (r'\+/-', r'\\pm'),
        (r'plus-minus', r'\\pm'),

        # Multiplication
        (r'\*', r'\\cdot'),

        # Less than or equal, greater than or equal
        (r'<=', r'\\leq'),
        (r'>=', r'\\geq'),
        (r'!=', r'\\neq'),
    ]

    for pattern, replacement in conversions:
        result = re.sub(pattern, replacement, result)

    return result

def wrap_math_expressions(text, inline=True):
    """
    Wrap detected math expressions with appropriate LaTeX delimiters
    """
    if not text:
        return text

    # Check if text already has LaTeX delimiters (more comprehensive check)
    if (text.startswith('$$') and text.endswith('$$') or
        text.startswith('\\(') and text.endswith('\\)') or
        text.startswith('\\[') and text.endswith('\\]') or
        '$$' in text or '\\(' in text or '\\[' in text):
        return text

    # Detect if this looks like a math expression
    math_matches = detect_math_expressions(text)

    if math_matches:
        # Convert to LaTeX
        latex_text = convert_to_latex(text)

        # Wrap with appropriate delimiters
        if inline:
            return f'\\({latex_text}\\)'
        else:
            return f'$${latex_text}$$'

    return text

def process_text_for_math(text):
    """
    Process text to automatically detect and format math expressions
    """
    if not text:
        return text

    # Split text into lines to handle each separately
    lines = text.split('\n')
    processed_lines = []

    for line in lines:
        # Skip if line already has LaTeX delimiters
        if '$$' in line or '\\(' in line or '\\[' in line:
            processed_lines.append(line)
            continue

        # Check if entire line looks like a math expression
        math_matches = detect_math_expressions(line.strip())

        if math_matches and len(line.strip()) < 100:  # Reasonable length for math expression
            # If most of the line is math, treat as display math
            math_content = sum(match['end'] - match['start'] for match in math_matches)
            if math_content > len(line.strip()) * 0.5:  # More than 50% is math
                processed_lines.append(wrap_math_expressions(line.strip(), inline=False))
            else:
                processed_lines.append(wrap_math_expressions(line.strip(), inline=True))
        else:
            # Look for inline math expressions within the line
            processed_line = line
            # For now, keep as is - we can enhance this later for mixed content
            processed_lines.append(processed_line)

    return '\n'.join(processed_lines)

# === FILE UPLOAD HELPER FUNCTIONS ===

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(filename):
    """Generate a unique filename using UUID4"""
    if filename == '':
        return None

    # Get file extension
    file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    # Generate unique filename with UUID4
    unique_filename = f"{uuid.uuid4().hex}.{file_ext}"
    return unique_filename

def save_uploaded_file(file):
    """
    Save uploaded file with validation and return the filename
    Returns: (success: bool, filename: str, error_message: str)
    """
    if not file:
        return False, None, "No file provided"

    if file.filename == '':
        return False, None, "No file selected"

    # Validate file type
    if not allowed_file(file.filename):
        return False, None, f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"

    try:
        # Generate secure filename
        original_filename = secure_filename(file.filename)
        unique_filename = generate_unique_filename(original_filename)

        if not unique_filename:
            return False, None, "Invalid filename"

        # Save file
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        return True, unique_filename, None

    except Exception as e:
        return False, None, f"Error saving file: {str(e)}"

def delete_uploaded_file(filename):
    """Delete an uploaded file"""
    if not filename:
        return

    try:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        print(f"Error deleting file {filename}: {e}")

def get_image_url(filename):
    """Get the URL for an uploaded image"""
    if not filename:
        return None
    return url_for('static', filename=f'uploads/{filename}')

# Initialize database
def init_db():
    with app.app_context():
        # Create tables if they don't exist
        db.create_all()

        # Create admin user if it doesn't exist
        if not User.query.filter_by(email='<EMAIL>').first():
            admin = User(
                name='Admin',
                email='<EMAIL>',
                password=generate_password_hash('admin123'),
                unhashed_password='admin123',  # Store unhashed version
                role='admin',
                is_verified=True  # Admin is verified by default
            )
            db.session.add(admin)
            db.session.commit()
            print("Admin user created successfully.")
        else:
            print("Admin user already exists.")

        # Add preloaded quizzes to all existing verified teachers
        add_preloaded_quizzes_to_all_teachers()

def create_preloaded_hard_quiz(teacher_id):
    """Create a preloaded hard quiz for 6th grade with 15 questions"""
    # Check if this quiz already exists for this teacher
    existing_quiz = Quiz.query.filter_by(
        teacher_id=teacher_id,
        title="6th Grade Advanced Mathematics Challenge"
    ).first()

    if existing_quiz:
        print(f"Math quiz already exists for teacher ID {teacher_id}")
        return existing_quiz

    # Create the quiz
    hard_quiz = Quiz(
        title="6th Grade Advanced Mathematics Challenge",
        description="A challenging quiz covering advanced 6th grade math concepts including algebra, geometry, and problem-solving.",
        teacher_id=teacher_id,
        time_limit=45,  # 45 minutes
        total_marks=75,  # 5 marks per question
        grade_a_threshold=85,
        grade_b_threshold=75,
        grade_c_threshold=65,
        grade_d_threshold=50,
        difficulty="hard"
    )
    db.session.add(hard_quiz)
    db.session.flush()  # Get the quiz ID

    # Add 15 questions
    questions = [
        {
            "question_text": "If 2x + 3y = 12 and 3x - y = 4, what is the value of x + y?",
            "option1": "3",
            "option2": "4",
            "option3": "5",
            "option4": "6",
            "correct_answer": "2",  # option2 = 4
            "marks": 5
        },
        {
            "question_text": "A rectangular prism has a length of 8 cm, width of 5 cm, and height of 3 cm. What is its volume?",
            "option1": "40 cm³",
            "option2": "120 cm³",
            "option3": "80 cm³",
            "option4": "16 cm³",
            "correct_answer": "2",  # option2 = 120 cm³
            "marks": 5
        },
        {
            "question_text": "If a number is increased by 20% and then decreased by 25%, the final result is what percent of the original number?",
            "option1": "95%",
            "option2": "90%",
            "option3": "85%",
            "option4": "80%",
            "correct_answer": "2",  # option2 = 90%
            "marks": 5
        },
        {
            "question_text": "What is the next number in the sequence: 1, 4, 9, 16, 25, ...?",
            "option1": "30",
            "option2": "36",
            "option3": "49",
            "option4": "64",
            "correct_answer": "2",  # option2 = 36
            "marks": 5
        },
        {
            "question_text": "If the perimeter of a square is 36 units, what is its area?",
            "option1": "36 square units",
            "option2": "81 square units",
            "option3": "64 square units",
            "option4": "9 square units",
            "correct_answer": "2",  # option2 = 81 square units
            "marks": 5
        },
        {
            "question_text": "A train travels at 60 km/h for 2 hours and then at 80 km/h for 3 hours. What is the average speed for the entire journey?",
            "option1": "70 km/h",
            "option2": "72 km/h",
            "option3": "75 km/h",
            "option4": "68 km/h",
            "correct_answer": "2",  # option2 = 72 km/h
            "marks": 5
        },
        {
            "question_text": "If 3/4 of a number is 18, what is the number?",
            "option1": "13.5",
            "option2": "24",
            "option3": "27",
            "option4": "32",
            "correct_answer": "2",  # option2 = 24
            "marks": 5
        },
        {
            "question_text": "What is the value of x in the equation 2(x + 3) - 5 = 3x - 8?",
            "option1": "2",
            "option2": "3",
            "option3": "4",
            "option4": "5",
            "correct_answer": "2",  # option2 = 3
            "marks": 5
        },
        {
            "question_text": "A box contains 5 red marbles, 3 blue marbles, and 2 green marbles. If a marble is drawn at random, what is the probability of drawing a blue marble?",
            "option1": "1/10",
            "option2": "3/10",
            "option3": "1/5",
            "option4": "1/2",
            "correct_answer": "2",  # option2 = 3/10
            "marks": 5
        },
        {
            "question_text": "If the sum of interior angles of a polygon is 1080°, how many sides does the polygon have?",
            "option1": "6",
            "option2": "8",
            "option3": "10",
            "option4": "12",
            "correct_answer": "2",  # option2 = 8
            "marks": 5
        },
        {
            "question_text": "What is the least common multiple (LCM) of 12, 18, and 24?",
            "option1": "36",
            "option2": "72",
            "option3": "48",
            "option4": "144",
            "correct_answer": "2",  # option2 = 72
            "marks": 5
        },
        {
            "question_text": "If a circle has a diameter of 14 cm, what is its circumference? (Use π = 22/7)",
            "option1": "22 cm",
            "option2": "44 cm",
            "option3": "77 cm",
            "option4": "154 cm",
            "correct_answer": "2",  # option2 = 44 cm
            "marks": 5
        },
        {
            "question_text": "A car travels 240 miles on 10 gallons of gas. How many miles can it travel on 16 gallons of gas?",
            "option1": "256 miles",
            "option2": "384 miles",
            "option3": "400 miles",
            "option4": "480 miles",
            "correct_answer": "2",  # option2 = 384 miles
            "marks": 5
        },
        {
            "question_text": "If the ratio of boys to girls in a class is 3:5 and there are 24 boys, how many students are in the class?",
            "option1": "40",
            "option2": "64",
            "option3": "56",
            "option4": "80",
            "correct_answer": "2",  # option2 = 64
            "marks": 5
        },
        {
            "question_text": "What is the value of y in the equation 3y - 2(y + 4) = 5y - 14?",
            "option1": "-2",
            "option2": "-6",
            "option3": "6",
            "option4": "2",
            "correct_answer": "2",  # option2 = -6
            "marks": 5
        }
    ]

    for q_data in questions:
        question = Question(
            quiz_id=hard_quiz.id,
            question_text=q_data["question_text"],
            question_type="mcq",
            option1=q_data["option1"],
            option2=q_data["option2"],
            option3=q_data["option3"],
            option4=q_data["option4"],
            correct_answer=q_data["correct_answer"],
            marks=q_data["marks"]
        )
        db.session.add(question)

    db.session.commit()
    print("Hard math quiz created successfully with 15 questions.")

    # Create a second hard quiz focused on geometry
    create_preloaded_geometry_quiz(teacher_id)

def create_preloaded_geometry_quiz(teacher_id):
    """Create a preloaded hard geometry quiz for 6th grade with 15 questions"""
    # Check if this quiz already exists for this teacher
    existing_quiz = Quiz.query.filter_by(
        teacher_id=teacher_id,
        title="6th Grade Advanced Geometry Challenge"
    ).first()

    if existing_quiz:
        print(f"Geometry quiz already exists for teacher ID {teacher_id}")
        # Create the fractions quiz after checking geometry quiz
        create_preloaded_fractions_quiz(teacher_id)
        return existing_quiz

    # Create the quiz
    geometry_quiz = Quiz(
        title="6th Grade Advanced Geometry Challenge",
        description="A challenging quiz focused on geometry concepts for 6th grade students, covering shapes, angles, area, volume, and spatial reasoning.",
        teacher_id=teacher_id,
        time_limit=45,  # 45 minutes
        total_marks=75,  # 5 marks per question
        grade_a_threshold=85,
        grade_b_threshold=75,
        grade_c_threshold=65,
        grade_d_threshold=50,
        difficulty="hard"
    )
    db.session.add(geometry_quiz)
    db.session.flush()  # Get the quiz ID

    # Add 15 geometry questions
    questions = [
        {
            "question_text": "What is the area of a circle with radius 7 cm? (Use π = 22/7)",
            "option1": "44 cm²",
            "option2": "154 cm²",
            "option3": "22 cm²",
            "option4": "77 cm²",
            "correct_answer": "2",  # option2 = 154 cm²
            "marks": 5
        },
        {
            "question_text": "A rectangular prism has a volume of 360 cubic cm. If its length is 10 cm and its width is 6 cm, what is its height?",
            "option1": "6 cm",
            "option2": "36 cm",
            "option3": "60 cm",
            "option4": "216 cm",
            "correct_answer": "1",  # option1 = 6 cm
            "marks": 5
        },
        {
            "question_text": "What is the sum of the interior angles of a hexagon?",
            "option1": "360°",
            "option2": "540°",
            "option3": "720°",
            "option4": "1080°",
            "correct_answer": "3",  # option3 = 720°
            "marks": 5
        },
        {
            "question_text": "If a triangle has angles measuring 30° and 60°, what is the measure of the third angle?",
            "option1": "30°",
            "option2": "60°",
            "option3": "90°",
            "option4": "120°",
            "correct_answer": "3",  # option3 = 90°
            "marks": 5
        },
        {
            "question_text": "What is the perimeter of a regular octagon with sides of length 5 cm?",
            "option1": "35 cm",
            "option2": "40 cm",
            "option3": "45 cm",
            "option4": "50 cm",
            "correct_answer": "2",  # option2 = 40 cm
            "marks": 5
        },
        {
            "question_text": "A cube has a surface area of 96 square cm. What is the length of each edge?",
            "option1": "4 cm",
            "option2": "6 cm",
            "option3": "8 cm",
            "option4": "16 cm",
            "correct_answer": "1",  # option1 = 4 cm
            "marks": 5
        },
        {
            "question_text": "What is the area of a trapezoid with bases of 8 cm and 12 cm, and a height of 5 cm?",
            "option1": "40 cm²",
            "option2": "50 cm²",
            "option3": "60 cm²",
            "option4": "100 cm²",
            "correct_answer": "2",  # option2 = 50 cm²
            "marks": 5
        },
        {
            "question_text": "If a rectangle has a perimeter of 30 cm and a width of 5 cm, what is its length?",
            "option1": "5 cm",
            "option2": "10 cm",
            "option3": "15 cm",
            "option4": "20 cm",
            "correct_answer": "2",  # option2 = 10 cm
            "marks": 5
        },
        {
            "question_text": "What is the volume of a sphere with radius 3 cm? (Use π = 22/7 and V = 4/3πr³)",
            "option1": "36π cm³",
            "option2": "9π cm³",
            "option3": "12π cm³",
            "option4": "113.1 cm³",
            "correct_answer": "1",  # option1 = 36π cm³
            "marks": 5
        },
        {
            "question_text": "Two angles are complementary. If one angle is 37°, what is the measure of the other angle?",
            "option1": "43°",
            "option2": "53°",
            "option3": "63°",
            "option4": "143°",
            "correct_answer": "2",  # option2 = 53°
            "marks": 5
        },
        {
            "question_text": "What is the area of a rhombus with diagonals of 10 cm and 12 cm?",
            "option1": "60 cm²",
            "option2": "120 cm²",
            "option3": "22 cm²",
            "option4": "48 cm²",
            "correct_answer": "1",  # option1 = 60 cm²
            "marks": 5
        },
        {
            "question_text": "A cylinder has a radius of 5 cm and a height of 10 cm. What is its volume? (Use π = 3.14)",
            "option1": "157 cm³",
            "option2": "250 cm³",
            "option3": "785 cm³",
            "option4": "1570 cm³",
            "correct_answer": "3",  # option3 = 785 cm³
            "marks": 5
        },
        {
            "question_text": "What is the measure of each interior angle in a regular pentagon?",
            "option1": "72°",
            "option2": "108°",
            "option3": "120°",
            "option4": "135°",
            "correct_answer": "2",  # option2 = 108°
            "marks": 5
        },
        {
            "question_text": "If a triangle has sides of lengths 5 cm, 12 cm, and 13 cm, what type of triangle is it?",
            "option1": "Equilateral",
            "option2": "Isosceles",
            "option3": "Scalene",
            "option4": "Right-angled",
            "correct_answer": "4",  # option4 = Right-angled
            "marks": 5
        },
        {
            "question_text": "What is the total surface area of a rectangular prism with length 8 cm, width 4 cm, and height 3 cm?",
            "option1": "96 cm²",
            "option2": "108 cm²",
            "option3": "112 cm²",
            "option4": "136 cm²",
            "correct_answer": "3",  # option3 = 112 cm²
            "marks": 5
        }
    ]

    for q_data in questions:
        question = Question(
            quiz_id=geometry_quiz.id,
            question_text=q_data["question_text"],
            question_type="mcq",
            option1=q_data["option1"],
            option2=q_data["option2"],
            option3=q_data["option3"],
            option4=q_data["option4"],
            correct_answer=q_data["correct_answer"],
            marks=q_data["marks"]
        )
        db.session.add(question)

    db.session.commit()
    print("Hard geometry quiz created successfully with 15 questions.")

    # Create the fractions quiz after creating geometry quiz
    create_preloaded_fractions_quiz(teacher_id)

def create_preloaded_fractions_quiz(teacher_id):
    """Create a preloaded medium difficulty quiz on fractions and decimals for 6th grade with 15 questions"""
    # Check if this quiz already exists for this teacher
    existing_quiz = Quiz.query.filter_by(
        teacher_id=teacher_id,
        title="6th Grade Fractions and Decimals"
    ).first()

    if existing_quiz:
        print(f"Fractions quiz already exists for teacher ID {teacher_id}")
        return existing_quiz

    # Create the quiz
    fractions_quiz = Quiz(
        title="6th Grade Fractions and Decimals",
        description="A medium difficulty quiz covering fractions, decimals, percentages, and related operations for 6th grade students.",
        teacher_id=teacher_id,
        time_limit=40,  # 40 minutes
        total_marks=75,  # 5 marks per question
        grade_a_threshold=80,
        grade_b_threshold=70,
        grade_c_threshold=60,
        grade_d_threshold=50,
        difficulty="medium"
    )
    db.session.add(fractions_quiz)
    db.session.flush()  # Get the quiz ID

    # Add 15 fractions and decimals questions
    questions = [
        {
            "question_text": "What is 2/5 + 3/10 in simplified form?",
            "option1": "1/2",
            "option2": "5/10",
            "option3": "7/10",
            "option4": "7/15",
            "correct_answer": "3",  # option3 = 7/10
            "marks": 5
        },
        {
            "question_text": "Convert 0.375 to a fraction in simplest form.",
            "option1": "3/8",
            "option2": "37.5/100",
            "option3": "3/80",
            "option4": "375/1000",
            "correct_answer": "1",  # option1 = 3/8
            "marks": 5
        },
        {
            "question_text": "What is 2.45 × 0.2?",
            "option1": "0.49",
            "option2": "0.245",
            "option3": "0.490",
            "option4": "4.9",
            "correct_answer": "3",  # option3 = 0.490
            "marks": 5
        },
        {
            "question_text": "Express 75% as a fraction in simplest form.",
            "option1": "75/100",
            "option2": "3/4",
            "option3": "7.5/10",
            "option4": "0.75/1",
            "correct_answer": "2",  # option2 = 3/4
            "marks": 5
        },
        {
            "question_text": "What is 3/4 ÷ 1/2?",
            "option1": "3/2",
            "option2": "6/4",
            "option3": "3/8",
            "option4": "1.5",
            "correct_answer": "1",  # option1 = 3/2
            "marks": 5
        },
        {
            "question_text": "Which decimal is equivalent to 5/8?",
            "option1": "0.58",
            "option2": "0.625",
            "option3": "0.825",
            "option4": "0.125",
            "correct_answer": "2",  # option2 = 0.625
            "marks": 5
        },
        {
            "question_text": "What is 1.75 - 0.8?",
            "option1": "0.95",
            "option2": "0.85",
            "option3": "1.05",
            "option4": "0.75",
            "correct_answer": "1",  # option1 = 0.95
            "marks": 5
        },
        {
            "question_text": "If 40% of a number is 60, what is the number?",
            "option1": "24",
            "option2": "150",
            "option3": "100",
            "option4": "240",
            "correct_answer": "2",  # option2 = 150
            "marks": 5
        },
        {
            "question_text": "What is 2/3 of 27?",
            "option1": "9",
            "option2": "18",
            "option3": "54",
            "option4": "81",
            "correct_answer": "2",  # option2 = 18
            "marks": 5
        },
        {
            "question_text": "Which fraction is greater: 5/8 or 7/12?",
            "option1": "5/8",
            "option2": "7/12",
            "option3": "They are equal",
            "option4": "Cannot be determined",
            "correct_answer": "1",  # option1 = 5/8
            "marks": 5
        },
        {
            "question_text": "What is 0.35 written as a percentage?",
            "option1": "3.5%",
            "option2": "35%",
            "option3": "0.35%",
            "option4": "350%",
            "correct_answer": "2",  # option2 = 35%
            "marks": 5
        },
        {
            "question_text": "What is the decimal equivalent of 7/20?",
            "option1": "0.35",
            "option2": "0.375",
            "option3": "0.7",
            "option4": "0.07",
            "correct_answer": "1",  # option1 = 0.35
            "marks": 5
        },
        {
            "question_text": "If a recipe calls for 3/4 cup of flour and you want to make 1/3 of the recipe, how much flour do you need?",
            "option1": "1/4 cup",
            "option2": "1/12 cup",
            "option3": "1/3 cup",
            "option4": "1/4 cup",
            "correct_answer": "1",  # option1 = 1/4 cup
            "marks": 5
        },
        {
            "question_text": "What is 3.75 rounded to the nearest tenth?",
            "option1": "3.7",
            "option2": "3.8",
            "option3": "4.0",
            "option4": "3.75",
            "correct_answer": "2",  # option2 = 3.8
            "marks": 5
        },
        {
            "question_text": "Which of the following is equivalent to 0.125?",
            "option1": "1/8",
            "option2": "1/4",
            "option3": "1/80",
            "option4": "12.5%",
            "correct_answer": "1",  # option1 = 1/8
            "marks": 5
        }
    ]

    for q_data in questions:
        question = Question(
            quiz_id=fractions_quiz.id,
            question_text=q_data["question_text"],
            question_type="mcq",
            option1=q_data["option1"],
            option2=q_data["option2"],
            option3=q_data["option3"],
            option4=q_data["option4"],
            correct_answer=q_data["correct_answer"],
            marks=q_data["marks"]
        )
        db.session.add(question)

    db.session.commit()
    print("Medium fractions quiz created successfully with 15 questions.")

def add_preloaded_quizzes_to_all_teachers():
    """Add preloaded quizzes to all existing verified teachers"""
    # Get all verified teachers
    teachers = User.query.filter_by(role='teacher', is_verified=True).all()

    if not teachers:
        print("No verified teachers found.")
        return

    print(f"Adding preloaded quizzes to {len(teachers)} verified teachers...")

    # Add quizzes to each teacher
    for teacher in teachers:
        # Check if the teacher already has quizzes
        existing_quizzes = Quiz.query.filter_by(teacher_id=teacher.id).count()

        if existing_quizzes == 0:
            # Teacher has no quizzes, add all preloaded quizzes
            create_preloaded_hard_quiz(teacher.id)
            print(f"Added preloaded quizzes to teacher {teacher.name} (ID: {teacher.id})")
        else:
            # Teacher has some quizzes, check which ones they're missing
            math_quiz = Quiz.query.filter_by(
                teacher_id=teacher.id,
                title="6th Grade Advanced Mathematics Challenge"
            ).first()

            geometry_quiz = Quiz.query.filter_by(
                teacher_id=teacher.id,
                title="6th Grade Advanced Geometry Challenge"
            ).first()

            fractions_quiz = Quiz.query.filter_by(
                teacher_id=teacher.id,
                title="6th Grade Fractions and Decimals"
            ).first()

            if not math_quiz:
                create_preloaded_hard_quiz(teacher.id)
                print(f"Added math quiz to teacher {teacher.name} (ID: {teacher.id})")
            elif not geometry_quiz:
                create_preloaded_geometry_quiz(teacher.id)
                print(f"Added geometry quiz to teacher {teacher.name} (ID: {teacher.id})")
            elif not fractions_quiz:
                create_preloaded_fractions_quiz(teacher.id)
                print(f"Added fractions quiz to teacher {teacher.name} (ID: {teacher.id})")
            else:
                print(f"Teacher {teacher.name} (ID: {teacher.id}) already has all preloaded quizzes")

# Call init_db when the application starts
init_db()

# Helper function for email validation
def is_valid_email(email):
    # Basic regex for email format
    return re.match(r"[^@]+@[^@]+\.[^@]+", email)

# Password validation helper
def is_strong_password(password):
    if len(password) < 8:
        return False
    if not any(c.islower() for c in password):
        return False
    if not any(c.isupper() for c in password):
        return False
    if not any(c.isdigit() for c in password):
        return False
    if not any(c in string.punctuation for c in password):
        return False
    return True

# Routes
@app.route('/')
def home():
    return render_template('home.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    # Redirect already logged-in users to their dashboard
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        user = User.query.filter_by(email=email).first()

        if user and check_password_hash(user.password, password):
            # Check if user is verified (except for admin users who are always verified)
            if not user.is_verified and user.role != 'admin':
                flash('Your account is pending verification by an administrator. Please wait for approval.', 'error')
                return redirect(url_for('login'))

            session['user_id'] = user.id
            session['user_role'] = user.role
            session['user_name'] = user.name

            # If this is a teacher, check if they have the preloaded quizzes
            if user.role == 'teacher':
                # Check if the teacher has any quizzes
                if not Quiz.query.filter_by(teacher_id=user.id).first():
                    # Add preloaded quizzes to this teacher
                    create_preloaded_hard_quiz(user.id)
                    print(f"Added preloaded quiz to teacher {user.name} on login")

            flash('Login successful!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid email or password. Please try again.', 'error')
            return redirect(url_for('login'))

    return render_template('login.html')

def generate_otp():
    """Generate a 6-digit OTP"""
    return str(random.randint(100000, 999999))

def send_otp_email(email, otp):
    msg = MailMessage(
        subject='Your Verification Code',
        recipients=[email]
    )
    msg.body = f'Your verification code is: {otp}\n\nThis code will expire in 10 minutes.'
    mail.send(msg)

def send_admin_verification_email(user):
    """Send an email to the admin when a new user registers"""
    admin_email = '<EMAIL>'  # Admin email address
    verification_url = url_for('verify_user_by_token', user_id=user.id, token=user.verification_token, _external=True)

    msg = MailMessage(
        subject='New User Registration Requires Verification',
        recipients=[admin_email]
    )

    msg.body = f'''
A new user has registered and requires verification:

Name: {user.name}
Email: {user.email}
Role: {user.role}
Parent Email: {user.parent_email if user.parent_email else 'N/A'}

To verify this user, please click the following link:
{verification_url}

To reject this user, visit the admin dashboard and manage pending users.
'''

    mail.send(msg)

def send_parent_comment_notification(comment):
    """Send email notification to teacher when a parent leaves a comment"""
    try:
        # Get teacher information
        teacher = User.query.get(comment.quiz.teacher_id)
        if not teacher:
            print(f"Teacher not found for quiz {comment.quiz_id}")
            return

        # Get parent and student information
        parent = User.query.get(comment.parent_id)
        student = User.query.get(comment.student_id)

        if not parent or not student:
            print(f"Parent or student not found for comment {comment.id}")
            return

        # Create email message
        msg = MailMessage(
            subject=f'New Parent Comment - {comment.quiz.title}',
            recipients=[teacher.email]
        )

        # Create view comment URL
        comment_url = url_for('view_parent_comments', quiz_id=comment.quiz_id, _external=True)

        msg.body = f'''
Dear {teacher.name},

A parent has left a comment about their child's quiz performance:

Quiz: {comment.quiz.title}
Student: {student.name}
Parent: {parent.name} ({parent.email})
Date: {comment.timestamp.strftime('%B %d, %Y at %I:%M %p')}

Comment:
{comment.comment_text}

To view all parent comments and respond, visit:
{comment_url}

Best regards,
Quiz Management System
'''

        msg.html = f'''
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #007bff;">New Parent Comment</h2>

    <p>Dear {teacher.name},</p>

    <p>A parent has left a comment about their child's quiz performance:</p>

    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
        <strong>Quiz:</strong> {comment.quiz.title}<br>
        <strong>Student:</strong> {student.name}<br>
        <strong>Parent:</strong> {parent.name} ({parent.email})<br>
        <strong>Date:</strong> {comment.timestamp.strftime('%B %d, %Y at %I:%M %p')}
    </div>

    <div style="background: #fff; border-left: 4px solid #007bff; padding: 15px; margin: 15px 0;">
        <strong>Comment:</strong><br>
        <p style="margin-top: 10px; line-height: 1.5;">{comment.comment_text}</p>
    </div>

    <p>
        <a href="{comment_url}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            View All Parent Comments
        </a>
    </p>

    <p style="color: #666; font-size: 0.9em;">
        Best regards,<br>
        Quiz Management System
    </p>
</div>
'''

        mail.send(msg)
        print(f"Parent comment notification sent to {teacher.email}")

    except Exception as e:
        print(f"Error sending parent comment notification: {e}")
        # Don't raise the exception to avoid breaking the comment submission

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    # Redirect already logged-in users to their dashboard
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        name = request.form['name'].strip()
        email = request.form['email'].strip().lower()
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        role = request.form['role']
        parent_email = request.form.get('parent_email', '').strip().lower()

        # --- Input Validation ---
        if password != confirm_password:
            flash('Passwords do not match. Please try again.', 'error')
            return redirect(url_for('signup'))

        # --- Password Strength Validation ---
        if not is_strong_password(password):
            flash('Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a digit, and a special character.', 'error')
            return redirect(url_for('signup'))

        if not all([name, email, password, role]):
            flash('All fields except Parent Email (for non-students) are required.', 'error')
            return redirect(url_for('signup'))

        if not is_valid_email(email):
             flash('Invalid format for your email.', 'error')
             return redirect(url_for('signup'))

        # Role-specific email validation
        if role == 'student' and not email.endswith('@jpischool.com'):
            flash('Student email must end with @jpischool.com.', 'error')
            return redirect(url_for('signup'))
        elif role == 'teacher' and not email.endswith('@jpischool.com'):
            flash('Teacher email must end with @jpischool.com.', 'error')
            return redirect(url_for('signup'))
        elif role == 'parent' and not (email.endswith('@gmail.com') or email.endswith('@yahoo.com')):
            flash(f'Parent email must end with @gmail.com or @yahoo.com.', 'error')
            return redirect(url_for('signup'))

        # Parent email validation (mandatory for students)
        if role == 'student':
            if not parent_email:
                 flash('Parent email is required for student registration.', 'error')
                 return redirect(url_for('signup'))
            if not is_valid_email(parent_email):
                flash('Invalid format for parent email.', 'error')
                return redirect(url_for('signup'))
            if not (parent_email.endswith('@gmail.com') or parent_email.endswith('@yahoo.com')):
                flash('Parent email must end with @gmail.com or @yahoo.com.', 'error')
                return redirect(url_for('signup'))

            # Check if the provided parent email exists as a parent user
            parent_user = User.query.filter_by(email=parent_email, role='parent').first()
            if not parent_user:
                flash('The provided parent email is not registered as a parent account. Please ask your parent to sign up first.', 'error')
                return redirect(url_for('signup'))
        else:
             parent_email = None

        # --- Check for Existing User ---
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            flash('Email already exists. Please login or use a different email.', 'error')
            return redirect(url_for('signup'))

        # Generate OTP and store user data in session
        otp = generate_otp()
        session['signup_data'] = {
            'name': name,
            'email': email,
            'password': generate_password_hash(password),
            'unhashed_password': password,  # Store unhashed version
            'role': role,
            'parent_email': parent_email,
            'otp': otp,
            'otp_timestamp': datetime.now().timestamp()
        }

        # Send OTP email
        try:
            send_otp_email(email, otp)
            flash('Verification code has been sent to your email.', 'success')
            return redirect(url_for('verify_otp'))
        except Exception as e:
            print("Error sending OTP email:", e)
            import traceback; traceback.print_exc()
            flash('Failed to send verification code. Please try again.', 'error')
            return redirect(url_for('signup'))

    return render_template('signup.html')

@app.route('/verify-otp', methods=['GET', 'POST'])
def verify_otp():
    if 'signup_data' not in session:
        flash('Please complete the signup process first.', 'error')
        return redirect(url_for('signup'))

    if request.method == 'POST':
        entered_otp = request.form['otp']
        stored_data = session['signup_data']

        # Check if OTP is expired (10 minutes)
        if datetime.now().timestamp() - stored_data['otp_timestamp'] > 600:
            flash('Verification code has expired. Please request a new one.', 'error')
            return redirect(url_for('verify_otp'))

        if entered_otp == stored_data['otp']:
            # Generate a verification token
            verification_token = generate_reset_token(stored_data['email'])

            # Create new user with verification token and unverified status
            new_user = User(
                name=stored_data['name'],
                email=stored_data['email'],
                password=stored_data['password'],
                unhashed_password=stored_data['unhashed_password'],
                role=stored_data['role'],
                parent_email=stored_data['parent_email'],
                is_verified=False,
                verification_token=verification_token
            )

            try:
                db.session.add(new_user)
                db.session.commit()

                # If this is a teacher account, add preloaded quizzes
                if new_user.role == 'teacher':
                    create_preloaded_hard_quiz(new_user.id)
                    print(f"Added preloaded quiz to new teacher {new_user.name}")

                # Send verification email to admin
                try:
                    send_admin_verification_email(new_user)
                except Exception as mail_error:
                    print(f"Error sending admin verification email: {mail_error}")
                    # Continue even if email fails, as user is already created

                # Clear signup data from session
                session.pop('signup_data', None)
                flash('Account created successfully! Your account is pending verification by an administrator. You will be notified when your account is approved.', 'success')
                return redirect(url_for('login'))
            except Exception as e:
                db.session.rollback()
                print(f"Error creating user account: {e}")
                flash('An error occurred while creating your account. Please try again.', 'error')
                return redirect(url_for('signup'))
        else:
            flash('Invalid verification code. Please try again.', 'error')
            return redirect(url_for('verify_otp'))

    return render_template('verify_otp.html')

@app.route('/resend-otp')
def resend_otp():
    if 'signup_data' not in session:
        flash('Please complete the signup process first.', 'error')
        return redirect(url_for('signup'))

    # Generate new OTP
    otp = generate_otp()
    session['signup_data']['otp'] = otp
    session['signup_data']['otp_timestamp'] = datetime.now().timestamp()

    # Send new OTP email
    try:
        send_otp_email(session['signup_data']['email'], otp)
        flash('A new verification code has been sent to your email.', 'success')
    except Exception as e:
        flash('Failed to send verification code. Please try again.', 'error')

    return redirect(url_for('verify_otp'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        flash('Please login to access the dashboard.', 'error')
        return redirect(url_for('login'))

    role = session['user_role']

    if role == 'admin':
        # Redirect admin users to the admin dashboard
        return redirect(url_for('admin_dashboard'))
    elif role == 'teacher':
        return redirect(url_for('teacher_dashboard'))
    elif role == 'student':
        return redirect(url_for('student_dashboard'))
    elif role == 'parent':
        return redirect(url_for('parent_dashboard'))
    else:
        flash('Unknown user role.', 'error')
        return redirect(url_for('login'))

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully.', 'success')
    return redirect(url_for('login'))

@app.route('/teacher/dashboard')
def teacher_dashboard():
    if 'user_id' not in session or session['user_role'] != 'teacher':
        return redirect(url_for('login'))
    return render_template('teacher_dashboard.html')

@app.route('/teacher/create-quiz', methods=['GET', 'POST'])
def create_quiz():
    if 'user_id' not in session or session['user_role'] not in ['teacher', 'admin']:
        return redirect(url_for('login'))

    if request.method == 'POST':
        print("--- Received POST request for /teacher/create-quiz ---")
        print(f"Form data: {request.form.to_dict(flat=False)}")
        try:
            # Create new quiz
            # Handle checkbox settings (checkbox sends 'on' when checked, nothing when unchecked)
            randomize_questions = 'randomize_questions' in request.form
            allow_calculator = 'allow_calculator' in request.form

            quiz = Quiz(
                title=request.form['quiz_title'],
                description=request.form.get('quiz_description', ''),
                teacher_id=session['user_id'],
                time_limit=request.form['time_limit'],
                total_marks=int(request.form['total_marks']),
                grade_a_threshold=int(request.form['grade_a']),
                grade_b_threshold=int(request.form['grade_b']),
                grade_c_threshold=int(request.form['grade_c']),
                grade_d_threshold=int(request.form['grade_d']),
                difficulty=request.form['difficulty'],
                randomize_questions=randomize_questions,
                allow_calculator=allow_calculator
            )
            db.session.add(quiz)
            db.session.flush() # Use flush to get the quiz ID before adding questions

            # Add questions (Only MCQ is expected now)
            question_texts = request.form.getlist('question[]')
            question_types = request.form.getlist('question_type[]') # Sent via hidden input, should be 'mcq'
            question_marks = request.form.getlist('question_marks[]')
            mcq_options1 = request.form.getlist('option1[]')
            mcq_options2 = request.form.getlist('option2[]')
            mcq_options3 = request.form.getlist('option3[]')
            mcq_options4 = request.form.getlist('option4[]')

            # Handle image uploads
            question_images = request.files.getlist('question_image[]')
            # Note: Correct answers are now indexed like correct_answer[0], correct_answer[1], etc.
            # We need to extract these carefully.
            correct_answer_map = {}
            for key, value in request.form.items():
                if key.startswith('correct_answer['):
                    index = int(key.split('[')[1].split(']')[0])
                    correct_answer_map[index] = value

            # Check if form data lengths match for mandatory fields
            num_questions = len(question_texts)
            if not (num_questions == len(question_types) == len(question_marks) == \
                      len(mcq_options1) == len(mcq_options2)):
                flash('Error: Form data length mismatch for core question fields.', 'error')
                db.session.rollback()
                return redirect(url_for('create_quiz'))

            if num_questions != len(correct_answer_map):
                 flash(f'Error: Mismatch between number of questions ({num_questions}) and selected correct answers ({len(correct_answer_map)}). Please ensure one answer is marked correct for each question.', 'error')
                 db.session.rollback()
                 return redirect(url_for('create_quiz'))


            for i in range(num_questions):
                # Check if the type is indeed MCQ (as expected from hidden input)
                if question_types[i] != 'mcq':
                    flash(f'Error: Internal error - Unexpected question type "{question_types[i]}" for question {i+1}.', 'error')
                    db.session.rollback()
                    return redirect(url_for('create_quiz'))

                # Get correct answer for this specific question index
                correct_ans_value = correct_answer_map.get(i)
                if correct_ans_value is None:
                    flash(f'Error: No correct answer selected for question {i+1}.', 'error')
                    db.session.rollback()
                    return redirect(url_for('create_quiz'))

                # Handle image upload for this question
                image_filename = None
                if i < len(question_images) and question_images[i] and question_images[i].filename:
                    success, filename, error_msg = save_uploaded_file(question_images[i])
                    if success:
                        image_filename = filename
                    else:
                        flash(f'Error uploading image for question {i+1}: {error_msg}', 'error')
                        db.session.rollback()
                        return redirect(url_for('create_quiz'))

                question = Question(
                    quiz_id=quiz.id,
                    question_text=question_texts[i],
                    question_type='mcq',
                    marks=int(question_marks[i]),
                    option1=mcq_options1[i],
                    option2=mcq_options2[i],
                    option3=mcq_options3[i] if i < len(mcq_options3) else '', # Use empty string if not provided
                    option4=mcq_options4[i] if i < len(mcq_options4) else '',
                    correct_answer=correct_ans_value, # Use the mapped value
                    image_filename=image_filename
                )
                db.session.add(question)

            db.session.commit()
            flash('Quiz created successfully!', 'success')
            # Redirect based on user role
            if session['user_role'] == 'admin':
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('teacher_dashboard'))

        except Exception as e:
             db.session.rollback()
             error_details = traceback.format_exc()
             print(f"Error in create_quiz: {e}\n{error_details}")
             flash(f'An error occurred while creating the quiz: {e}', 'error') # Simplified user message
             return redirect(url_for('create_quiz'))

    return render_template('create_quiz.html')

@app.route('/my-quizzes')
def my_quizzes():
    if 'user_id' not in session or session['user_role'] not in ['teacher', 'admin']:
        return redirect(url_for('login'))

    # Get view type from query parameter
    view_type = request.args.get('view', 'active')

    if view_type == 'archived':
        # Get archived quizzes (inactive)
        quizzes = Quiz.query.filter_by(teacher_id=session['user_id'], is_active=False)\
                           .order_by(Quiz.version.desc()).all()
    else:
        # Get active quizzes (default)
        quizzes = Quiz.query.filter_by(teacher_id=session['user_id'], is_active=True)\
                           .order_by(Quiz.created_at.desc()).all()

    return render_template('my_quizzes.html', quizzes=quizzes, view_type=view_type)

@app.route('/teacher/delete-quiz/<int:quiz_id>', methods=['DELETE'])
def delete_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] not in ['teacher', 'admin']:
        return jsonify({'error': 'Unauthorized'}), 401

    quiz = Quiz.query.get_or_404(quiz_id)

    # Check if the quiz belongs to the current user (teacher or admin)
    if quiz.teacher_id != session['user_id']:
        return jsonify({'error': 'Unauthorized'}), 403

    # Delete all questions associated with the quiz
    Question.query.filter_by(quiz_id=quiz_id).delete()

    # Delete the quiz
    db.session.delete(quiz)
    db.session.commit()

    return jsonify({'message': 'Quiz deleted successfully'})

# === QUIZ EXPORT FUNCTIONALITY ===

def generate_quiz_csv_data(quiz):
    """Generate CSV data for a single quiz"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write quiz header information
    writer.writerow(['Quiz Export'])
    writer.writerow(['Generated on:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    writer.writerow([])  # Empty row for spacing

    # Quiz basic information
    writer.writerow(['Quiz Information'])
    writer.writerow(['Title:', quiz.title])
    writer.writerow(['Description:', quiz.description or 'No description'])
    writer.writerow(['Total Marks:', quiz.total_marks])
    writer.writerow(['Time Limit (minutes):', quiz.time_limit])
    writer.writerow(['Difficulty:', quiz.difficulty.title()])
    writer.writerow(['Created by:', quiz.teacher.name])
    writer.writerow(['Created on:', quiz.created_at.strftime('%Y-%m-%d %H:%M:%S')])
    writer.writerow(['Version:', quiz.version])
    writer.writerow(['Randomize Questions:', 'Yes' if quiz.randomize_questions else 'No'])
    writer.writerow(['Allow Calculator:', 'Yes' if quiz.allow_calculator else 'No'])
    writer.writerow([])  # Empty row for spacing

    # Grade thresholds
    writer.writerow(['Grade Thresholds'])
    writer.writerow(['Grade A (%):', quiz.grade_a_threshold])
    writer.writerow(['Grade B (%):', quiz.grade_b_threshold])
    writer.writerow(['Grade C (%):', quiz.grade_c_threshold])
    writer.writerow(['Grade D (%):', quiz.grade_d_threshold])
    writer.writerow([])  # Empty row for spacing

    # Questions header
    writer.writerow(['Questions'])
    writer.writerow(['Question #', 'Question Text', 'Type', 'Marks', 'Option 1', 'Option 2', 'Option 3', 'Option 4', 'Correct Answer', 'Correct Answer Text'])

    # Get all questions for this quiz
    questions = Question.query.filter_by(quiz_id=quiz.id).all()

    for i, question in enumerate(questions, 1):
        # Determine correct answer text
        correct_answer_text = ''
        if question.question_type == 'mcq':
            if question.correct_answer == '1':
                correct_answer_text = question.option1 or ''
            elif question.correct_answer == '2':
                correct_answer_text = question.option2 or ''
            elif question.correct_answer == '3':
                correct_answer_text = question.option3 or ''
            elif question.correct_answer == '4':
                correct_answer_text = question.option4 or ''
        elif question.question_type == 'true_false':
            correct_answer_text = question.correct_answer

        writer.writerow([
            i,  # Question number
            question.question_text,
            question.question_type.upper(),
            question.marks,
            question.option1 or '',
            question.option2 or '',
            question.option3 or '',
            question.option4 or '',
            question.correct_answer,
            correct_answer_text
        ])

    # Add summary statistics
    writer.writerow([])  # Empty row for spacing
    writer.writerow(['Summary Statistics'])
    writer.writerow(['Total Questions:', len(questions)])
    writer.writerow(['Total Possible Marks:', sum(q.marks for q in questions)])
    writer.writerow(['Average Marks per Question:', round(sum(q.marks for q in questions) / len(questions), 2) if questions else 0])

    # Question type breakdown
    mcq_count = sum(1 for q in questions if q.question_type == 'mcq')
    tf_count = sum(1 for q in questions if q.question_type == 'true_false')
    writer.writerow(['MCQ Questions:', mcq_count])
    writer.writerow(['True/False Questions:', tf_count])

    return output.getvalue()

def create_safe_filename(title, quiz_id):
    """Create a safe filename from quiz title"""
    # Remove or replace unsafe characters
    safe_title = re.sub(r'[^\w\s-]', '', title)
    safe_title = re.sub(r'[-\s]+', '_', safe_title)
    safe_title = safe_title.strip('_')

    # Limit length and add quiz ID
    if len(safe_title) > 50:
        safe_title = safe_title[:50]

    return f"quiz_{quiz_id}_{safe_title}.csv"

@app.route('/teacher/view-quiz/<int:quiz_id>')
def view_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] not in ['teacher', 'admin']:
        return redirect(url_for('login'))

    quiz = Quiz.query.get_or_404(quiz_id)

    # Check if the quiz belongs to the current user (teacher or admin)
    if quiz.teacher_id != session['user_id']:
        return redirect(url_for('my_quizzes'))

    # Get all questions for this quiz
    questions = Question.query.filter_by(quiz_id=quiz_id).all()

    return render_template('view_quiz.html', quiz=quiz, questions=questions)

@app.route('/teacher/edit-quiz/<int:quiz_id>', methods=['GET', 'POST'])
def edit_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] not in ['teacher', 'admin']:
        return redirect(url_for('login'))

    quiz = Quiz.query.get_or_404(quiz_id)

    if quiz.teacher_id != session['user_id']:
        flash('You are not authorized to edit this quiz.', 'error')
        return redirect(url_for('my_quizzes'))

    # Check if quiz has attempts - if so, we need to create a new version
    has_attempts = quiz_has_attempts(quiz_id)

    if request.method == 'POST':
        # If quiz has attempts, create a new version instead of editing in place
        if has_attempts:
            try:
                # Create new version
                new_quiz = clone_quiz_for_versioning(quiz)

                # Update the new version with form data
                target_quiz = new_quiz
                flash_message = f'Quiz has existing attempts. Created new version {new_quiz.version} with your changes.'
            except Exception as e:
                db.session.rollback()
                flash(f'Error creating new quiz version: {e}', 'error')
                questions = Question.query.filter_by(quiz_id=quiz_id).all()
                return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)
        else:
            # No attempts, safe to edit in place
            target_quiz = quiz
            flash_message = 'Quiz updated successfully!'
        try:
            # Update quiz details on target quiz (either original or new version)
            target_quiz.title = request.form['quiz_title']
            target_quiz.description = request.form.get('quiz_description', '')
            target_quiz.time_limit = request.form['time_limit']
            target_quiz.total_marks = int(request.form['total_marks'])
            target_quiz.grade_a_threshold = int(request.form['grade_a'])
            target_quiz.grade_b_threshold = int(request.form['grade_b'])
            target_quiz.grade_c_threshold = int(request.form['grade_c'])
            target_quiz.grade_d_threshold = int(request.form['grade_d'])
            target_quiz.difficulty = request.form['difficulty']

            # Handle checkbox settings
            target_quiz.randomize_questions = 'randomize_questions' in request.form
            target_quiz.allow_calculator = 'allow_calculator' in request.form

            # Update questions (assuming only MCQs)
            question_texts = request.form.getlist('question[]')
            question_types = request.form.getlist('question_type[]') # Should be 'mcq' from hidden input
            question_marks = request.form.getlist('question_marks[]')
            mcq_options1 = request.form.getlist('option1[]')
            mcq_options2 = request.form.getlist('option2[]')
            mcq_options3 = request.form.getlist('option3[]')
            mcq_options4 = request.form.getlist('option4[]')

            # Handle image uploads and removals
            question_images = request.files.getlist('question_image[]')
            existing_images = request.form.getlist('existing_image[]')
            remove_images = request.form.getlist('remove_image[]')

            # Parse correct answers from indexed radio buttons
            correct_answer_map = {}
            for key, value in request.form.items():
                if key.startswith('correct_answer['):
                    try:
                        index = int(key.split('[')[1].split(']')[0])
                        correct_answer_map[index] = value
                    except (IndexError, ValueError):
                        # Handle potential malformed key, though unlikely
                        flash(f'Warning: Malformed correct answer key received: {key}', 'warning')
                        continue # Skip this potentially problematic key

            # --- Validation ---
            num_questions = len(question_texts)
            if not (num_questions == len(question_types) == len(question_marks) == \
                    len(mcq_options1) == len(mcq_options2)):
                flash('Error: Form data length mismatch for core question fields.', 'error')
                # Don't commit, return to edit page with current values
                questions = Question.query.filter_by(quiz_id=quiz_id).all() # Reload for template
                return render_template('edit_quiz.html', quiz=quiz, questions=questions)

            if num_questions != len(correct_answer_map):
                flash(f'Error: Mismatch between questions ({num_questions}) and selected correct answers ({len(correct_answer_map)}). Mark one answer per question.', 'error')
                questions = Question.query.filter_by(quiz_id=quiz_id).all()
                return render_template('edit_quiz.html', quiz=quiz, questions=questions)

            # --- Update Database ---
            if has_attempts:
                # For versioned quiz, questions are already cloned, just update them
                target_questions = Question.query.filter_by(quiz_id=target_quiz.id).all()

                # Clear existing questions from new version and recreate them
                for q in target_questions:
                    db.session.delete(q)

                # Create new questions with form data
                for i in range(num_questions):
                    correct_ans_value = correct_answer_map.get(i)
                    if correct_ans_value is None:
                        flash(f'Error: No correct answer selected for question {i+1}.', 'error')
                        db.session.rollback()
                        questions = Question.query.filter_by(quiz_id=quiz_id).all()
                        return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)

                    # Handle image for this question
                    image_filename = None

                    # Check if there's a new image upload
                    if i < len(question_images) and question_images[i] and question_images[i].filename:
                        success, filename, error_msg = save_uploaded_file(question_images[i])
                        if success:
                            image_filename = filename
                        else:
                            flash(f'Error uploading image for question {i+1}: {error_msg}', 'error')
                            db.session.rollback()
                            questions = Question.query.filter_by(quiz_id=quiz_id).all()
                            return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)
                    else:
                        # No new image, check if we should keep existing image
                        if i < len(existing_images) and existing_images[i] and str(i) not in remove_images:
                            image_filename = existing_images[i]

                    new_question = Question(
                        quiz_id=target_quiz.id,
                        question_text=question_texts[i],
                        question_type='mcq',
                        marks=int(question_marks[i]),
                        option1=mcq_options1[i],
                        option2=mcq_options2[i],
                        option3=mcq_options3[i] if i < len(mcq_options3) else '',
                        option4=mcq_options4[i] if i < len(mcq_options4) else '',
                        correct_answer=correct_ans_value,
                        image_filename=image_filename
                    )
                    db.session.add(new_question)
            else:
                # No attempts, safe to edit in place
                existing_questions = Question.query.filter_by(quiz_id=quiz_id).all()

                # Update existing questions
                for i in range(min(len(existing_questions), num_questions)):
                    correct_ans_value = correct_answer_map.get(i)
                    if correct_ans_value is None:
                        flash(f'Error: No correct answer selected for question {i+1}.', 'error')
                        db.session.rollback()
                        questions = Question.query.filter_by(quiz_id=quiz_id).all()
                        return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)

                    # Update existing question
                    existing_questions[i].question_text = question_texts[i]
                    existing_questions[i].question_type = 'mcq'
                    existing_questions[i].marks = int(question_marks[i])
                    existing_questions[i].option1 = mcq_options1[i]
                    existing_questions[i].option2 = mcq_options2[i]
                    existing_questions[i].option3 = mcq_options3[i] if i < len(mcq_options3) else ''
                    existing_questions[i].option4 = mcq_options4[i] if i < len(mcq_options4) else ''
                    existing_questions[i].correct_answer = correct_ans_value

                    # Handle image for existing question
                    # Check if user wants to remove existing image
                    if str(i) in remove_images and existing_questions[i].image_filename:
                        delete_uploaded_file(existing_questions[i].image_filename)
                        existing_questions[i].image_filename = None

                    # Check if there's a new image upload
                    if i < len(question_images) and question_images[i] and question_images[i].filename:
                        # Delete old image if exists
                        if existing_questions[i].image_filename:
                            delete_uploaded_file(existing_questions[i].image_filename)

                        success, filename, error_msg = save_uploaded_file(question_images[i])
                        if success:
                            existing_questions[i].image_filename = filename
                        else:
                            flash(f'Error uploading image for question {i+1}: {error_msg}', 'error')
                            db.session.rollback()
                            questions = Question.query.filter_by(quiz_id=quiz_id).all()
                            return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)

                # Handle adding new questions for in-place editing
                if num_questions > len(existing_questions):
                    for i in range(len(existing_questions), num_questions):
                        correct_ans_value = correct_answer_map.get(i)
                        if correct_ans_value is None:
                            flash(f'Error: No correct answer selected for question {i+1}.', 'error')
                            db.session.rollback()
                            questions = Question.query.filter_by(quiz_id=quiz_id).all()
                            return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)

                        # Handle image for new question
                        image_filename = None
                        if i < len(question_images) and question_images[i] and question_images[i].filename:
                            success, filename, error_msg = save_uploaded_file(question_images[i])
                            if success:
                                image_filename = filename
                            else:
                                flash(f'Error uploading image for question {i+1}: {error_msg}', 'error')
                                db.session.rollback()
                                questions = Question.query.filter_by(quiz_id=quiz_id).all()
                                return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)

                        # Add new question
                        question = Question(
                            quiz_id=target_quiz.id,
                            question_text=question_texts[i],
                            question_type='mcq',
                            marks=int(question_marks[i]),
                            option1=mcq_options1[i],
                            option2=mcq_options2[i],
                            option3=mcq_options3[i] if i < len(mcq_options3) else '',
                            option4=mcq_options4[i] if i < len(mcq_options4) else '',
                            correct_answer=correct_ans_value,
                            image_filename=image_filename
                        )
                        db.session.add(question)

                # Handle deleting extra questions for in-place editing
                if len(existing_questions) > num_questions:
                        for i in range(num_questions, len(existing_questions)):
                            # Delete associated image file if exists
                            if existing_questions[i].image_filename:
                                delete_uploaded_file(existing_questions[i].image_filename)
                            db.session.delete(existing_questions[i])

            db.session.commit()
            flash(flash_message, 'success')

            # Redirect to the new quiz if versioned, otherwise stay with original
            if has_attempts:
                return redirect(url_for('edit_quiz', quiz_id=target_quiz.id))
            else:
                return redirect(url_for('my_quizzes'))

        except Exception as e:
            db.session.rollback()
            error_details = traceback.format_exc()
            print(f"Error in edit_quiz: {e}\n{error_details}")
            flash(f'An error occurred while updating the quiz: {e}', 'error')
            # Return to edit page with original data on error
            questions = Question.query.filter_by(quiz_id=quiz_id).all()
            return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)

    # For GET request, show the edit form with existing data
    questions = Question.query.filter_by(quiz_id=quiz_id).all()
    return render_template('edit_quiz.html', quiz=quiz, questions=questions, has_attempts=has_attempts)

@app.route('/quiz/<int:quiz_id>/attempt', methods=['GET'])
def attempt_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Only students can attempt quizzes.', 'error')
        return redirect(url_for('dashboard'))

    quiz = Quiz.query.get_or_404(quiz_id)
    questions = Question.query.filter_by(quiz_id=quiz_id).all()

    # Check if student has already attempted this quiz
    existing_attempt = QuizAttempt.query.filter_by(
        student_id=session['user_id'],
        quiz_id=quiz_id
    ).first()

    if existing_attempt:
        flash('You have already attempted this quiz.', 'error')
        return redirect(url_for('student_dashboard'))

    # Handle question randomization
    if quiz.randomize_questions and len(questions) > 2:
        # Create a session key for this specific quiz attempt
        session_key = f'quiz_{quiz_id}_question_order'

        # Check if we already have a randomized order for this student and quiz
        if session_key not in session:
            # Create randomized order
            question_ids = [q.id for q in questions]
            random.shuffle(question_ids)
            session[session_key] = question_ids

        # Get the randomized order
        randomized_order = session[session_key]

        # Reorder questions according to the randomized order
        question_dict = {q.id: q for q in questions}
        questions = [question_dict[qid] for qid in randomized_order if qid in question_dict]

    return render_template('attempt_quiz.html', quiz=quiz, questions=questions)

@app.route('/quiz/<int:quiz_id>/submit', methods=['POST'])
def submit_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Only students can submit quizzes.', 'error')
        return redirect(url_for('dashboard'))

    quiz = Quiz.query.get_or_404(quiz_id)
    questions = Question.query.filter_by(quiz_id=quiz_id).all()

    # Check if student has already attempted this quiz
    existing_attempt = QuizAttempt.query.filter_by(
        student_id=session['user_id'],
        quiz_id=quiz_id
    ).first()

    if existing_attempt:
        flash('You have already attempted this quiz.', 'error')
        return redirect(url_for('student_dashboard'))

    # Get the question order from session (if randomized)
    session_key = f'quiz_{quiz_id}_question_order'
    question_order = session.get(session_key, None)

    # Calculate score
    total_marks = 0
    obtained_marks = 0

    # Create quiz attempt with question order
    attempt = QuizAttempt(
        student_id=session['user_id'],
        quiz_id=quiz_id,
        score=0,
        question_order=json.dumps(question_order) if question_order else None
    )
    db.session.add(attempt)
    db.session.flush()  # Get the attempt ID

    for question in questions:
        total_marks += question.marks
        selected_answer = request.form.get(f'question_{question.id}')

        if selected_answer:
            is_correct = False
            if question.question_type == 'mcq':
                # Convert both to strings for comparison
                is_correct = str(selected_answer).strip() == str(question.correct_answer).strip()
            elif question.question_type == 'true_false':
                # Convert both to lowercase strings for comparison
                is_correct = str(selected_answer).lower().strip() == str(question.correct_answer).lower().strip()

            if is_correct:
                obtained_marks += question.marks

            answer = QuizAnswer(
                attempt_id=attempt.id,
                question_id=question.id,
                selected_answer=selected_answer,
                is_correct=is_correct
            )
            db.session.add(answer)

    # Calculate final score
    score = (obtained_marks / total_marks) * 100 if total_marks > 0 else 0
    attempt.score = score

    db.session.commit()

    # Clean up session data for this quiz
    if session_key in session:
        del session[session_key]

    flash(f'Quiz submitted successfully! Your score: {score:.2f}%', 'success')
    return redirect(url_for('student_dashboard'))

def calculate_grade(score, quiz=None):
    """
    Calculate the grade based on the score and quiz thresholds.
    If quiz is provided, use its thresholds, otherwise use default thresholds.
    """
    if quiz:
        if score >= quiz.grade_a_threshold:
            return 'A'
        elif score >= quiz.grade_b_threshold:
            return 'B'
        elif score >= quiz.grade_c_threshold:
            return 'C'
        elif score >= quiz.grade_d_threshold:
            return 'D'
        else:
            return 'F'
    else:
        # Fallback to default thresholds if no quiz is provided
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'

def clone_quiz_for_versioning(original_quiz):
    """Create a new version of a quiz when editing an attempted quiz"""
    print(f"🔄 Creating new version of quiz: {original_quiz.title}")

    # Determine the new version number
    if original_quiz.original_quiz_id is None:
        # This is the original quiz, find the highest version
        latest_version = Quiz.query.filter_by(original_quiz_id=original_quiz.id).order_by(Quiz.version.desc()).first()
        new_version = (latest_version.version + 1) if latest_version else 2
        original_quiz_id = original_quiz.id
    else:
        # This is already a version, find the highest version of the original
        latest_version = Quiz.query.filter_by(original_quiz_id=original_quiz.original_quiz_id).order_by(Quiz.version.desc()).first()
        new_version = latest_version.version + 1
        original_quiz_id = original_quiz.original_quiz_id

    # Create new quiz version
    new_quiz = Quiz(
        title=original_quiz.title,
        description=original_quiz.description,
        time_limit=original_quiz.time_limit,
        total_marks=original_quiz.total_marks,
        grade_a_threshold=original_quiz.grade_a_threshold,
        grade_b_threshold=original_quiz.grade_b_threshold,
        grade_c_threshold=original_quiz.grade_c_threshold,
        grade_d_threshold=original_quiz.grade_d_threshold,
        difficulty=original_quiz.difficulty,
        teacher_id=original_quiz.teacher_id,
        version=new_version,
        is_active=True,
        original_quiz_id=original_quiz_id,
        randomize_questions=original_quiz.randomize_questions
    )

    db.session.add(new_quiz)
    db.session.flush()  # Get the new quiz ID

    # Clone all questions
    original_questions = Question.query.filter_by(quiz_id=original_quiz.id).all()
    for original_question in original_questions:
        new_question = Question(
            quiz_id=new_quiz.id,
            question_text=original_question.question_text,
            question_type=original_question.question_type,
            option1=original_question.option1,
            option2=original_question.option2,
            option3=original_question.option3,
            option4=original_question.option4,
            correct_answer=original_question.correct_answer,
            marks=original_question.marks
        )
        db.session.add(new_question)

    # Mark original quiz as inactive (archived)
    original_quiz.is_active = False

    print(f"✅ Created quiz version {new_version} with ID {new_quiz.id}")
    return new_quiz

def quiz_has_attempts(quiz_id):
    """Check if a quiz has any attempts"""
    return QuizAttempt.query.filter_by(quiz_id=quiz_id).count() > 0

@app.route('/student/dashboard')
def student_dashboard():
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Please login as a student to access the dashboard.', 'error')
        return redirect(url_for('login'))

    # Get difficulty filter from query parameters
    difficulty_filter = request.args.get('difficulty', 'All')

    # Validate difficulty filter
    valid_difficulties = ['All', 'Easy', 'Medium', 'Hard']
    if difficulty_filter not in valid_difficulties:
        difficulty_filter = 'All'

    # Get quizzes based on difficulty filter (only active quizzes)
    if difficulty_filter == 'All':
        quizzes = Quiz.query.filter_by(is_active=True).all()
    else:
        quizzes = Quiz.query.filter_by(difficulty=difficulty_filter, is_active=True).all()

    # Get past attempts with quiz data in chronological order (oldest first)
    past_attempts = QuizAttempt.query.filter_by(student_id=session['user_id'])\
        .join(Quiz)\
        .order_by(QuizAttempt.submitted_at.asc())\
        .all()

    # Calculate progress statistics
    total_attempts = len(past_attempts)
    average_score = sum(attempt.score for attempt in past_attempts) / total_attempts if total_attempts > 0 else 0

    # Prepare data for progress graph (now in chronological order)
    quiz_progress = {
        'labels': [f'Quiz {i+1}' for i in range(total_attempts)],
        'scores': [attempt.score for attempt in past_attempts]  # Will now be in chronological order
    }

    # Add grades to attempts
    for attempt in past_attempts:
        attempt.grade = calculate_grade(attempt.score, attempt.quiz)

    return render_template('student_dashboard.html',
                         quizzes=quizzes,
                         past_attempts=past_attempts,
                         total_attempts=total_attempts,
                         average_score=average_score,
                         quiz_progress=quiz_progress,
                         difficulty_filter=difficulty_filter,
                         valid_difficulties=valid_difficulties)

@app.route('/quiz/result/<int:attempt_id>')
def view_past_result(attempt_id):
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Please login as a student to view quiz results.', 'error')
        return redirect(url_for('login'))

    # Get the attempt with its associated quiz
    attempt = QuizAttempt.query\
        .join(Quiz)\
        .filter(QuizAttempt.id == attempt_id)\
        .first_or_404()

    # Verify the attempt belongs to the current student
    if attempt.student_id != session['user_id']:
        flash('You are not authorized to view this result.', 'error')
        return redirect(url_for('student_dashboard'))

    # Get all questions and answers for this attempt
    questions = Question.query.filter_by(quiz_id=attempt.quiz_id).all()
    answers = QuizAnswer.query.filter_by(attempt_id=attempt_id).all()

    # Create a dictionary of question_id to answer for easy lookup
    answer_dict = {answer.question_id: answer for answer in answers}

    # Get a list of all question IDs in the database
    current_question_ids = [q.id for q in questions]

    # Pair questions with their answers, including unanswered questions
    questions_with_answers = []

    # First, add all current questions with their answers
    for question in questions:
        answer = answer_dict.get(question.id)
        if answer:
            questions_with_answers.append((question, answer))
        else:
            # Create a dummy answer object for unanswered questions
            dummy_answer = QuizAnswer(
                attempt_id=attempt_id,
                question_id=question.id,
                selected_answer="",
                is_correct=False
            )
            dummy_answer.is_omitted = True  # Add a flag to identify omitted questions
            questions_with_answers.append((question, dummy_answer))

    # Now check for answers to questions that no longer exist in the quiz
    # (these would be from previous versions of the quiz)
    for answer in answers:
        if answer.question_id not in current_question_ids:
            # Try to get the question from the database (it might still exist)
            question = Question.query.get(answer.question_id)
            if question:
                # Question exists but is no longer part of this quiz
                questions_with_answers.append((question, answer))
            else:
                # Question no longer exists at all, create a placeholder
                placeholder_question = Question(
                    id=answer.question_id,
                    quiz_id=attempt.quiz_id,
                    question_text="[This question has been removed]",
                    question_type="mcq",
                    option1="[Option no longer available]",
                    option2="[Option no longer available]",
                    option3="[Option no longer available]",
                    option4="[Option no longer available]",
                    correct_answer="",
                    marks=0
                )
                questions_with_answers.append((placeholder_question, answer))

    # Calculate correct, incorrect, and omitted counts
    correct_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and answer.is_correct)
    incorrect_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and not answer.is_correct)
    omitted_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') and answer.is_omitted)

    # Calculate time taken (in minutes) - fix the calculation to use a more reasonable time
    # Instead of quiz.created_at (which is when the quiz was created by the teacher),
    # we should ideally track when the student started the quiz
    # For now, we'll estimate based on the time limit of the quiz
    time_taken = min((attempt.submitted_at - attempt.quiz.created_at).total_seconds() / 60,
                     float(attempt.quiz.time_limit)) if attempt.quiz.created_at else 0

    return render_template('past_quiz_result.html',
                         quiz=attempt.quiz,
                         attempt=attempt,
                         questions_with_answers=questions_with_answers,
                         correct_count=correct_count,
                         incorrect_count=incorrect_count,
                         omitted_count=omitted_count,
                         total_questions=len(questions_with_answers),
                         time_taken=f"{time_taken:.1f} minutes",
                         grade=calculate_grade(attempt.score, attempt.quiz))

@app.route('/parent/dashboard')
def parent_dashboard():
    if 'user_id' not in session or session['user_role'] != 'parent':
        flash('Please login as a parent to access the dashboard.', 'error')
        return redirect(url_for('login'))

    parent_email = User.query.get(session['user_id']).email

    # Find all children linked to this parent
    children = User.query.filter_by(role='student', parent_email=parent_email).all()

    children_data = []
    for child in children:
        # Get attempts for each child, ordered chronologically
        attempts = QuizAttempt.query.filter_by(student_id=child.id)\
            .join(Quiz)\
            .order_by(QuizAttempt.submitted_at.asc())\
            .all()

        # Add grades to attempts
        for attempt in attempts:
            attempt.grade = calculate_grade(attempt.score, attempt.quiz)

        children_data.append({
            'id': child.id,
            'name': child.name,
            'attempts': attempts
        })

    return render_template('parent_dashboard.html', children_data=children_data)

# Route for parents to view a specific child's quiz result
@app.route('/parent/result/<int:attempt_id>')
def view_child_result(attempt_id):
    if 'user_id' not in session or session['user_role'] != 'parent':
        flash('Please login as a parent to view results.', 'error')
        return redirect(url_for('login'))

    attempt = QuizAttempt.query.get_or_404(attempt_id)
    child = User.query.get(attempt.student_id)
    parent_email = User.query.get(session['user_id']).email

    # Verify this child is linked to the logged-in parent
    if child.parent_email != parent_email:
        flash('You are not authorized to view this result.', 'error')
        return redirect(url_for('parent_dashboard'))

    # Reuse the student's result view logic
    questions = Question.query.filter_by(quiz_id=attempt.quiz_id).all()
    answers = QuizAnswer.query.filter_by(attempt_id=attempt_id).all()
    answer_dict = {answer.question_id: answer for answer in answers}

    # Get a list of all question IDs in the database
    current_question_ids = [q.id for q in questions]

    # Pair questions with their answers, including unanswered questions
    questions_with_answers = []

    # First, add all current questions with their answers
    for question in questions:
        answer = answer_dict.get(question.id)
        if answer:
            questions_with_answers.append((question, answer))
        else:
            # Create a dummy answer object for unanswered questions
            dummy_answer = QuizAnswer(
                attempt_id=attempt_id,
                question_id=question.id,
                selected_answer="",
                is_correct=False
            )
            dummy_answer.is_omitted = True  # Add a flag to identify omitted questions
            questions_with_answers.append((question, dummy_answer))

    # Now check for answers to questions that no longer exist in the quiz
    # (these would be from previous versions of the quiz)
    for answer in answers:
        if answer.question_id not in current_question_ids:
            # Try to get the question from the database (it might still exist)
            question = Question.query.get(answer.question_id)
            if question:
                # Question exists but is no longer part of this quiz
                questions_with_answers.append((question, answer))
            else:
                # Question no longer exists at all, create a placeholder
                placeholder_question = Question(
                    id=answer.question_id,
                    quiz_id=attempt.quiz_id,
                    question_text="[This question has been removed]",
                    question_type="mcq",
                    option1="[Option no longer available]",
                    option2="[Option no longer available]",
                    option3="[Option no longer available]",
                    option4="[Option no longer available]",
                    correct_answer="",
                    marks=0
                )
                questions_with_answers.append((placeholder_question, answer))

    # Calculate correct, incorrect, and omitted counts
    correct_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and answer.is_correct)
    incorrect_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and not answer.is_correct)
    omitted_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') and answer.is_omitted)

    # Calculate time taken (in minutes) - fix the calculation to use a more reasonable time
    time_taken = min((attempt.submitted_at - attempt.quiz.created_at).total_seconds() / 60,
                     float(attempt.quiz.time_limit)) if attempt.quiz.created_at else 0

    # Get existing parent comments for this attempt
    existing_comments = ParentComment.query.filter_by(
        attempt_id=attempt_id,
        parent_id=session['user_id']
    ).order_by(ParentComment.timestamp.desc()).all()

    # Render the same past_quiz_result template with parent context
    return render_template('past_quiz_result.html',
                         quiz=attempt.quiz,
                         attempt=attempt,
                         questions_with_answers=questions_with_answers,
                         correct_count=correct_count,
                         incorrect_count=incorrect_count,
                         omitted_count=omitted_count,
                         total_questions=len(questions_with_answers),
                         time_taken=f"{time_taken:.1f} minutes",
                         grade=calculate_grade(attempt.score, attempt.quiz),
                         is_parent_view=True,
                         child=child,
                         existing_comments=existing_comments)

@app.route('/parent/comment/<int:attempt_id>', methods=['POST'])
def submit_parent_comment(attempt_id):
    if 'user_id' not in session or session['user_role'] != 'parent':
        flash('Please login as a parent to leave comments.', 'error')
        return redirect(url_for('login'))

    attempt = QuizAttempt.query.get_or_404(attempt_id)
    child = User.query.get(attempt.student_id)
    parent_email = User.query.get(session['user_id']).email

    # Verify this child is linked to the logged-in parent
    if child.parent_email != parent_email:
        flash('You are not authorized to comment on this result.', 'error')
        return redirect(url_for('parent_dashboard'))

    # Get and validate comment text
    comment_text = request.form.get('comment_text', '').strip()
    if not comment_text:
        flash('Please enter a comment before submitting.', 'error')
        return redirect(url_for('view_child_result', attempt_id=attempt_id))

    if len(comment_text) > 2000:  # Reasonable limit
        flash('Comment is too long. Please keep it under 2000 characters.', 'error')
        return redirect(url_for('view_child_result', attempt_id=attempt_id))

    try:
        # Create new parent comment
        comment = ParentComment(
            student_id=attempt.student_id,
            quiz_id=attempt.quiz_id,
            attempt_id=attempt_id,
            parent_id=session['user_id'],
            comment_text=comment_text
        )

        db.session.add(comment)
        db.session.commit()

        # Send email notification to teacher
        try:
            send_parent_comment_notification(comment)
        except Exception as e:
            print(f"Failed to send email notification: {e}")
            # Continue even if email fails

        flash('Your comment has been submitted successfully. The teacher will be able to see it and has been notified by email.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('An error occurred while submitting your comment. Please try again.', 'error')
        print(f"Error submitting parent comment: {e}")

    return redirect(url_for('view_child_result', attempt_id=attempt_id))

@app.route('/student/report-card')
def student_report_card():
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Please login as a student to view your report card.', 'error')
        return redirect(url_for('login'))

    student_id = session['user_id']

    # Get filter parameters
    quiz_filter = request.args.get('quiz_id', '')
    comments_only = request.args.get('comments_only', '') == 'true'

    # Get all quiz attempts for this student
    attempts_query = QuizAttempt.query.filter_by(student_id=student_id)\
        .join(Quiz)\
        .filter(Quiz.is_active == True)

    # Apply quiz filter if specified
    if quiz_filter:
        attempts_query = attempts_query.filter(Quiz.id == int(quiz_filter))

    # Get attempts ordered by most recent first
    attempts = attempts_query.order_by(QuizAttempt.submitted_at.desc()).all()

    # Calculate grades for each attempt
    for attempt in attempts:
        attempt.grade = calculate_grade(attempt.score, attempt.quiz)
        attempt.time_taken = "N/A"  # We don't currently track time taken

    # Get all quizzes the student has attempted (for filter dropdown)
    attempted_quiz_ids = [attempt.quiz_id for attempt in attempts]
    attempted_quizzes = Quiz.query.filter(Quiz.id.in_(attempted_quiz_ids)).order_by(Quiz.title).all() if attempted_quiz_ids else []

    # Get student comments for all attempts
    student_comments = StudentComment.query.filter_by(student_id=student_id)\
        .order_by(StudentComment.timestamp.desc()).all()

    # Get parent comments for all attempts
    parent_comments = ParentComment.query.filter_by(student_id=student_id)\
        .order_by(ParentComment.timestamp.desc()).all()

    # Create dictionaries for quick lookup
    student_comments_by_quiz = {}
    parent_comments_by_quiz = {}

    for comment in student_comments:
        if comment.quiz_id not in student_comments_by_quiz:
            student_comments_by_quiz[comment.quiz_id] = []
        student_comments_by_quiz[comment.quiz_id].append(comment)

    for comment in parent_comments:
        if comment.quiz_id not in parent_comments_by_quiz:
            parent_comments_by_quiz[comment.quiz_id] = []
        parent_comments_by_quiz[comment.quiz_id].append(comment)

    # If comments_only filter is active, filter attempts to only those with comments
    if comments_only:
        quiz_ids_with_comments = set(student_comments_by_quiz.keys()) | set(parent_comments_by_quiz.keys())
        attempts = [attempt for attempt in attempts if attempt.quiz_id in quiz_ids_with_comments]

    # Calculate summary statistics
    if attempts:
        scores = [attempt.score for attempt in attempts]
        summary_stats = {
            'total_attempts': len(attempts),
            'average_score': round(sum(scores) / len(scores), 1),
            'highest_score': max(scores),
            'lowest_score': min(scores),
            'total_quizzes': len(set(attempt.quiz_id for attempt in attempts))
        }

        # Calculate grade distribution
        grades = [calculate_grade(score, attempt.quiz) for score, attempt in zip(scores, attempts)]
        grade_counts = {'A': 0, 'B': 0, 'C': 0, 'D': 0, 'F': 0}
        for grade in grades:
            if grade in grade_counts:
                grade_counts[grade] += 1

        summary_stats['grade_distribution'] = grade_counts
    else:
        summary_stats = {
            'total_attempts': 0,
            'average_score': 0,
            'highest_score': 0,
            'lowest_score': 0,
            'total_quizzes': 0,
            'grade_distribution': {'A': 0, 'B': 0, 'C': 0, 'D': 0, 'F': 0}
        }

    return render_template('student_report_card.html',
                         attempts=attempts,
                         attempted_quizzes=attempted_quizzes,
                         student_comments_by_quiz=student_comments_by_quiz,
                         parent_comments_by_quiz=parent_comments_by_quiz,
                         summary_stats=summary_stats,
                         quiz_filter=quiz_filter,
                         comments_only=comments_only)

@app.route('/student/comment/<int:quiz_id>', methods=['POST'])
def submit_student_comment(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Please login as a student to leave comments.', 'error')
        return redirect(url_for('login'))

    student_id = session['user_id']
    quiz = Quiz.query.get_or_404(quiz_id)

    # Verify student has attempted this quiz
    attempt = QuizAttempt.query.filter_by(student_id=student_id, quiz_id=quiz_id).first()
    if not attempt:
        flash('You can only comment on quizzes you have attempted.', 'error')
        return redirect(url_for('student_report_card'))

    # Get comment text from form
    comment_text = request.form.get('comment_text', '').strip()

    if not comment_text:
        flash('Please enter a comment before submitting.', 'error')
        return redirect(url_for('student_report_card'))

    if len(comment_text) > 2000:
        flash('Comment is too long. Please limit to 2000 characters.', 'error')
        return redirect(url_for('student_report_card'))

    try:
        # Create new student comment
        comment = StudentComment(
            student_id=student_id,
            quiz_id=quiz_id,
            attempt_id=attempt.id,
            comment_text=comment_text
        )

        db.session.add(comment)
        db.session.commit()

        flash('Your reflection has been saved successfully.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('An error occurred while saving your reflection. Please try again.', 'error')
        print(f"Error submitting student comment: {e}")

    return redirect(url_for('student_report_card'))

@app.route('/teacher/parent-comments')
def view_parent_comments():
    if 'user_id' not in session or session['user_role'] not in ['teacher', 'admin']:
        flash('Please login as a teacher to view parent comments.', 'error')
        return redirect(url_for('login'))

    # Get filter parameters
    quiz_filter = request.args.get('quiz_id', '')
    student_filter = request.args.get('student_id', '')
    comments_only = request.args.get('comments_only', '') == 'true'

    # Base query for teacher's quizzes
    teacher_id = session['user_id']

    # Get all quizzes by this teacher for the filter dropdown
    teacher_quizzes = Quiz.query.filter_by(teacher_id=teacher_id, is_active=True)\
        .order_by(Quiz.title).all()

    # Build the comments query - get comments for teacher's quizzes
    comments_query = ParentComment.query\
        .join(Quiz, ParentComment.quiz_id == Quiz.id)\
        .filter(Quiz.teacher_id == teacher_id)

    # Apply filters
    if quiz_filter:
        comments_query = comments_query.filter(ParentComment.quiz_id == int(quiz_filter))

    if student_filter:
        comments_query = comments_query.filter(ParentComment.student_id == int(student_filter))

    # Get comments ordered by most recent first
    comments = comments_query\
        .order_by(ParentComment.timestamp.desc())\
        .all()

    # Get students who have taken quizzes by this teacher (for filter dropdown)
    students_with_attempts = db.session.query(User)\
        .join(QuizAttempt, User.id == QuizAttempt.student_id)\
        .join(Quiz, QuizAttempt.quiz_id == Quiz.id)\
        .filter(Quiz.teacher_id == teacher_id)\
        .distinct()\
        .order_by(User.name)\
        .all()

    # If comments_only filter is active, get only quizzes that have comments
    if comments_only:
        quiz_ids_with_comments = [c.quiz_id for c in comments]
        teacher_quizzes = [q for q in teacher_quizzes if q.id in quiz_ids_with_comments]

    return render_template('teacher_parent_comments.html',
                         comments=comments,
                         teacher_quizzes=teacher_quizzes,
                         students_with_attempts=students_with_attempts,
                         quiz_filter=quiz_filter,
                         student_filter=student_filter,
                         comments_only=comments_only)

@app.route('/teacher/reply', methods=['POST'])
def submit_teacher_reply():
    if 'user_id' not in session or session['user_role'] not in ['teacher', 'admin']:
        flash('Please login as a teacher to reply to comments.', 'error')
        return redirect(url_for('login'))

    teacher_id = session['user_id']

    # Get form data
    reply_text = request.form.get('reply_text', '').strip()
    parent_comment_id = request.form.get('parent_comment_id')
    student_comment_id = request.form.get('student_comment_id')

    if not reply_text:
        flash('Please enter a reply before submitting.', 'error')
        return redirect(request.referrer or url_for('view_parent_comments'))

    if len(reply_text) > 2000:
        flash('Reply is too long. Please limit to 2000 characters.', 'error')
        return redirect(request.referrer or url_for('view_parent_comments'))

    # Validate that teacher owns the quiz for the comment being replied to
    quiz_id = None

    if parent_comment_id:
        parent_comment = ParentComment.query.get_or_404(parent_comment_id)
        quiz_id = parent_comment.quiz_id

        # Verify teacher owns this quiz
        if parent_comment.quiz.teacher_id != teacher_id:
            flash('You can only reply to comments on your own quizzes.', 'error')
            return redirect(url_for('view_parent_comments'))

    elif student_comment_id:
        student_comment = StudentComment.query.get_or_404(student_comment_id)
        quiz_id = student_comment.quiz_id

        # Verify teacher owns this quiz
        if student_comment.quiz.teacher_id != teacher_id:
            flash('You can only reply to comments on your own quizzes.', 'error')
            return redirect(url_for('view_parent_comments'))

    else:
        flash('Invalid comment reference.', 'error')
        return redirect(url_for('view_parent_comments'))

    try:
        # Create teacher reply
        reply = TeacherReply(
            teacher_id=teacher_id,
            quiz_id=quiz_id,
            parent_comment_id=int(parent_comment_id) if parent_comment_id else None,
            student_comment_id=int(student_comment_id) if student_comment_id else None,
            reply_text=reply_text
        )

        db.session.add(reply)
        db.session.commit()

        flash('Your reply has been submitted successfully.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('An error occurred while submitting your reply. Please try again.', 'error')
        print(f"Error submitting teacher reply: {e}")

    return redirect(request.referrer or url_for('view_parent_comments'))

# --- NEW Messaging Routes ---

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# === QUIZ EXPORT ROUTES ===

@app.route('/teacher/export-quiz/<int:quiz_id>')
@login_required
def export_quiz_csv(quiz_id):
    """Export a single quiz as CSV file"""
    if session['user_role'] not in ['teacher', 'admin']:
        flash('You do not have permission to export quizzes.', 'error')
        return redirect(url_for('login'))

    quiz = Quiz.query.get_or_404(quiz_id)

    # Check if user has permission to export this quiz
    if session['user_role'] == 'teacher' and quiz.teacher_id != session['user_id']:
        flash('You can only export quizzes you created.', 'error')
        return redirect(url_for('my_quizzes'))

    try:
        # Generate CSV data
        csv_data = generate_quiz_csv_data(quiz)

        # Create filename
        filename = create_safe_filename(quiz.title, quiz.id)

        # Create response
        output = io.BytesIO()
        output.write(csv_data.encode('utf-8'))
        output.seek(0)

        return send_file(
            output,
            mimetype='text/csv',
            as_attachment=True,
            download_name=filename,
            conditional=False
        )

    except Exception as e:
        print(f"Error exporting quiz {quiz_id}: {e}")
        flash('An error occurred while exporting the quiz. Please try again.', 'error')
        return redirect(url_for('my_quizzes'))

@app.route('/teacher/export-all-quizzes')
@login_required
def export_all_quizzes():
    """Export all user's quizzes as a ZIP file containing multiple CSV files"""
    if session['user_role'] not in ['teacher', 'admin']:
        flash('You do not have permission to export quizzes.', 'error')
        return redirect(url_for('login'))

    try:
        # Get user's quizzes
        if session['user_role'] == 'admin':
            # Admins can export all quizzes
            quizzes = Quiz.query.filter_by(is_active=True).all()
            zip_filename = f"all_quizzes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        else:
            # Teachers can only export their own quizzes
            quizzes = Quiz.query.filter_by(teacher_id=session['user_id'], is_active=True).all()
            user = User.query.get(session['user_id'])
            safe_name = re.sub(r'[^\w\s-]', '', user.name).replace(' ', '_')
            zip_filename = f"{safe_name}_quizzes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        if not quizzes:
            flash('No quizzes found to export.', 'error')
            return redirect(url_for('my_quizzes'))

        # Create ZIP file in memory
        zip_buffer = io.BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for quiz in quizzes:
                try:
                    # Generate CSV data for each quiz
                    csv_data = generate_quiz_csv_data(quiz)
                    csv_filename = create_safe_filename(quiz.title, quiz.id)

                    # Add CSV to ZIP
                    zip_file.writestr(csv_filename, csv_data.encode('utf-8'))

                except Exception as e:
                    print(f"Error processing quiz {quiz.id} for bulk export: {e}")
                    continue

            # Add a summary file
            summary_data = generate_export_summary(quizzes)
            zip_file.writestr('export_summary.txt', summary_data.encode('utf-8'))

        zip_buffer.seek(0)

        return send_file(
            zip_buffer,
            mimetype='application/zip',
            as_attachment=True,
            download_name=zip_filename,
            conditional=False
        )

    except Exception as e:
        print(f"Error in bulk export: {e}")
        flash('An error occurred while exporting quizzes. Please try again.', 'error')
        return redirect(url_for('my_quizzes'))

def generate_export_summary(quizzes):
    """Generate a summary text file for bulk export"""
    summary = []
    summary.append("Quiz Export Summary")
    summary.append("=" * 50)
    summary.append(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    summary.append(f"Total Quizzes Exported: {len(quizzes)}")
    summary.append("")

    # Group by difficulty
    difficulty_counts = {}
    total_questions = 0
    total_marks = 0

    for quiz in quizzes:
        difficulty = quiz.difficulty
        difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1

        # Count questions and marks
        questions = Question.query.filter_by(quiz_id=quiz.id).all()
        total_questions += len(questions)
        total_marks += sum(q.marks for q in questions)

    summary.append("Quiz Statistics:")
    summary.append(f"  Total Questions: {total_questions}")
    summary.append(f"  Total Possible Marks: {total_marks}")
    summary.append("")

    summary.append("Difficulty Distribution:")
    for difficulty, count in sorted(difficulty_counts.items()):
        summary.append(f"  {difficulty.title()}: {count} quiz(s)")
    summary.append("")

    summary.append("Exported Quizzes:")
    summary.append("-" * 30)

    for i, quiz in enumerate(quizzes, 1):
        questions = Question.query.filter_by(quiz_id=quiz.id).all()
        summary.append(f"{i}. {quiz.title}")
        summary.append(f"   ID: {quiz.id}")
        summary.append(f"   Difficulty: {quiz.difficulty.title()}")
        summary.append(f"   Questions: {len(questions)}")
        summary.append(f"   Total Marks: {quiz.total_marks}")
        summary.append(f"   Created: {quiz.created_at.strftime('%Y-%m-%d')}")
        summary.append(f"   Version: {quiz.version}")
        summary.append("")

    return "\n".join(summary)

# === PDF REPORT CARD GENERATION ===

def generate_student_report_pdf(student_id, requesting_user_role, requesting_user_id):
    """Generate a comprehensive PDF report card for a student"""

    # Get student information
    student = User.query.get_or_404(student_id)
    if student.role != 'student':
        raise ValueError("Invalid student ID")

    # Verify access permissions
    if requesting_user_role == 'parent':
        parent = User.query.get(requesting_user_id)
        if not parent or student.parent_email != parent.email:
            raise ValueError("Parent not authorized to view this student's report")
    elif requesting_user_role not in ['teacher', 'admin']:
        raise ValueError("Unauthorized access to student report")

    # Get all quiz attempts for this student
    attempts = QuizAttempt.query.filter_by(student_id=student_id)\
        .join(Quiz)\
        .filter(Quiz.is_active == True)\
        .order_by(QuizAttempt.submitted_at.desc())\
        .all()

    # Calculate grades for attempts
    for attempt in attempts:
        attempt.grade = calculate_grade(attempt.score, attempt.quiz)

    # Get comments
    student_comments = {}
    parent_comments = {}
    teacher_replies = {}

    for attempt in attempts:
        # Student comments
        student_comment = StudentComment.query.filter_by(
            student_id=student_id,
            quiz_id=attempt.quiz_id
        ).first()
        if student_comment:
            student_comments[attempt.quiz_id] = student_comment

        # Parent comments
        parent_comment = ParentComment.query.filter_by(
            student_id=student_id,
            quiz_id=attempt.quiz_id
        ).first()
        if parent_comment:
            parent_comments[attempt.quiz_id] = parent_comment

        # Teacher replies
        teacher_reply = TeacherReply.query.filter_by(
            quiz_id=attempt.quiz_id
        ).filter(
            or_(
                TeacherReply.parent_comment_id == parent_comment.id if parent_comment else False,
                TeacherReply.student_comment_id == student_comment.id if student_comment else False
            )
        ).first()
        if teacher_reply:
            teacher_replies[attempt.quiz_id] = teacher_reply

    # Calculate summary statistics
    if attempts:
        scores = [attempt.score for attempt in attempts]
        summary_stats = {
            'total_attempts': len(attempts),
            'total_quizzes': len(set(attempt.quiz_id for attempt in attempts)),
            'average_score': round(sum(scores) / len(scores), 1),
            'highest_score': max(scores),
            'lowest_score': min(scores),
            'grade_distribution': {}
        }

        # Calculate grade distribution
        grades = [attempt.grade for attempt in attempts]
        for grade in ['A', 'B', 'C', 'D', 'F']:
            summary_stats['grade_distribution'][grade] = grades.count(grade)
    else:
        summary_stats = {
            'total_attempts': 0,
            'average_score': 0,
            'highest_score': 0,
            'lowest_score': 0,
            'total_quizzes': 0,
            'grade_distribution': {'A': 0, 'B': 0, 'C': 0, 'D': 0, 'F': 0}
        }

    # Generate PDF
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)

    # Container for the 'Flowable' objects
    elements = []

    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.darkblue
    )

    subheading_style = ParagraphStyle(
        'CustomSubHeading',
        parent=styles['Heading3'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.black
    )

    normal_style = styles['Normal']

    # Title
    elements.append(Paragraph("📊 Student Report Card", title_style))
    elements.append(Spacer(1, 12))

    # Student Information
    elements.append(Paragraph("Student Information", heading_style))
    student_info_data = [
        ['Student Name:', student.name],
        ['Email:', student.email],
        ['Report Generated:', datetime.now().strftime('%B %d, %Y at %I:%M %p')],
        ['Generated by:', f"{requesting_user_role.title()} User"]
    ]

    student_info_table = Table(student_info_data, colWidths=[2*inch, 4*inch])
    student_info_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    elements.append(student_info_table)
    elements.append(Spacer(1, 20))

    # Summary Statistics
    elements.append(Paragraph("Performance Summary", heading_style))
    summary_data = [
        ['Total Quiz Attempts:', str(summary_stats['total_attempts'])],
        ['Unique Quizzes Taken:', str(summary_stats['total_quizzes'])],
        ['Average Score:', f"{summary_stats['average_score']}%"],
        ['Highest Score:', f"{summary_stats['highest_score']}%"],
        ['Lowest Score:', f"{summary_stats['lowest_score']}%"]
    ]

    summary_table = Table(summary_data, colWidths=[2.5*inch, 1.5*inch])
    summary_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey),
    ]))
    elements.append(summary_table)
    elements.append(Spacer(1, 12))

    # Grade Distribution
    elements.append(Paragraph("Grade Distribution", subheading_style))
    grade_data = [['Grade', 'Count']]
    for grade in ['A', 'B', 'C', 'D', 'F']:
        count = summary_stats['grade_distribution'][grade]
        grade_data.append([grade, str(count)])

    grade_table = Table(grade_data, colWidths=[1*inch, 1*inch])
    grade_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
    ]))
    elements.append(grade_table)
    elements.append(Spacer(1, 20))

    return buffer, elements, attempts, student_comments, parent_comments, teacher_replies, styles

def complete_student_report_pdf(buffer, elements, attempts, student_comments, parent_comments, teacher_replies, styles):
    """Complete the PDF generation with quiz details and comments"""

    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.darkblue
    )

    subheading_style = ParagraphStyle(
        'CustomSubHeading',
        parent=styles['Heading3'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.black
    )

    comment_style = ParagraphStyle(
        'CommentStyle',
        parent=styles['Normal'],
        fontSize=9,
        spaceAfter=6,
        leftIndent=20,
        textColor=colors.darkgreen
    )

    # Quiz Details Section
    if attempts:
        elements.append(Paragraph("Quiz Attempt Details", heading_style))

        # Create quiz details table
        quiz_data = [['Date', 'Quiz Title', 'Score', 'Grade', 'Time Taken', 'Difficulty']]

        for attempt in attempts:
            # Calculate time taken
            if attempt.time_taken:
                time_taken = f"{attempt.time_taken} min"
            else:
                time_taken = "N/A"

            quiz_data.append([
                attempt.submitted_at.strftime('%m/%d/%Y'),
                attempt.quiz.title[:30] + ('...' if len(attempt.quiz.title) > 30 else ''),
                f"{attempt.score}%",
                attempt.grade,
                time_taken,
                attempt.quiz.difficulty.title()
            ])

        quiz_table = Table(quiz_data, colWidths=[0.8*inch, 2.2*inch, 0.6*inch, 0.5*inch, 0.7*inch, 0.7*inch])
        quiz_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        elements.append(quiz_table)
        elements.append(Spacer(1, 20))

        # Comments Section
        elements.append(Paragraph("Comments and Feedback", heading_style))

        for attempt in attempts:
            quiz_id = attempt.quiz_id
            has_comments = (quiz_id in student_comments or
                          quiz_id in parent_comments or
                          quiz_id in teacher_replies)

            if has_comments:
                elements.append(Paragraph(f"Quiz: {attempt.quiz.title}", subheading_style))

                # Student comment
                if quiz_id in student_comments:
                    comment = student_comments[quiz_id]
                    elements.append(Paragraph(f"<b>Student Reflection:</b>", styles['Normal']))
                    elements.append(Paragraph(comment.comment_text, comment_style))
                    elements.append(Spacer(1, 6))

                # Parent comment
                if quiz_id in parent_comments:
                    comment = parent_comments[quiz_id]
                    elements.append(Paragraph(f"<b>Parent Comment:</b>", styles['Normal']))
                    elements.append(Paragraph(comment.comment_text, comment_style))
                    elements.append(Spacer(1, 6))

                # Teacher reply
                if quiz_id in teacher_replies:
                    reply = teacher_replies[quiz_id]
                    elements.append(Paragraph(f"<b>Teacher Response:</b>", styles['Normal']))
                    elements.append(Paragraph(reply.reply_text, comment_style))
                    elements.append(Spacer(1, 6))

                elements.append(Spacer(1, 12))

    else:
        elements.append(Paragraph("No quiz attempts found for this student.", styles['Normal']))

    # Build PDF
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)
    doc.build(elements)

    # Get the value of the BytesIO buffer and return it
    pdf_data = buffer.getvalue()
    buffer.close()

    return pdf_data

def create_safe_pdf_filename(student_name, student_id):
    """Create a safe filename for PDF report cards"""
    import re

    # Remove or replace unsafe characters
    safe_name = re.sub(r'[^\w\s-]', '', student_name)
    safe_name = re.sub(r'[-\s]+', '_', safe_name)
    safe_name = safe_name.strip('_')

    # Limit length
    if len(safe_name) > 30:
        safe_name = safe_name[:30]

    return f"ReportCard_{safe_name}_{student_id}.pdf"

# === PDF EXPORT ROUTES ===

@app.route('/teacher/export-student-report/<int:student_id>')
@login_required
def teacher_export_student_report(student_id):
    """Export a student's report card as PDF (Teacher access)"""
    if session['user_role'] not in ['teacher', 'admin']:
        flash('You do not have permission to export student reports.', 'error')
        return redirect(url_for('login'))

    try:
        # Generate PDF
        buffer, elements, attempts, student_comments, parent_comments, teacher_replies, styles = \
            generate_student_report_pdf(student_id, session['user_role'], session['user_id'])

        pdf_data = complete_student_report_pdf(buffer, elements, attempts,
                                             student_comments, parent_comments, teacher_replies, styles)

        # Get student info for filename
        student = User.query.get_or_404(student_id)
        filename = create_safe_pdf_filename(student.name, student_id)

        # Create response
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    except ValueError as e:
        flash(str(e), 'error')
        return redirect(url_for('manage_student_reports'))
    except Exception as e:
        print(f"Error exporting student report {student_id}: {e}")
        flash('An error occurred while generating the report. Please try again.', 'error')
        return redirect(url_for('manage_student_reports'))

@app.route('/parent/export-report-card/<int:child_id>')
@login_required
def parent_export_report_card(child_id):
    """Export a child's report card as PDF (Parent access)"""
    if session['user_role'] != 'parent':
        flash('You do not have permission to access this feature.', 'error')
        return redirect(url_for('login'))

    try:
        # Generate PDF
        buffer, elements, attempts, student_comments, parent_comments, teacher_replies, styles = \
            generate_student_report_pdf(child_id, session['user_role'], session['user_id'])

        pdf_data = complete_student_report_pdf(buffer, elements, attempts,
                                             student_comments, parent_comments, teacher_replies, styles)

        # Get student info for filename
        student = User.query.get_or_404(child_id)
        filename = create_safe_pdf_filename(student.name, child_id)

        # Create response
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    except ValueError as e:
        flash(str(e), 'error')
        return redirect(url_for('parent_dashboard'))
    except Exception as e:
        print(f"Error exporting child report {child_id}: {e}")
        flash('An error occurred while generating the report. Please try again.', 'error')
        return redirect(url_for('parent_dashboard'))

@app.route('/compose', methods=['GET', 'POST'])
@login_required
def compose_message():
    user_id = session['user_id']
    # Pre-fill recipient if ID is provided (e.g., from a reply button)
    recipient_id = request.args.get('to')
    recipient = User.query.get(recipient_id) if recipient_id else None
    subject = request.args.get('subject', '') # Pre-fill subject for replies

    if request.method == 'POST':
        receiver_username = request.form.get('receiver_username', '').strip()
        subject_form = request.form.get('subject', '').strip()
        body = request.form.get('body', '').strip()

        if not receiver_username or not body:
            flash('Receiver username and message body are required.', 'error')
            # Re-render form with entered data
            return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

        receiver = User.query.filter_by(name=receiver_username).first()
        if not receiver:
            flash(f'Recipient with username "{receiver_username}" not found.', 'error')
            return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

        if receiver.id == user_id:
             flash('You cannot send a message to yourself.', 'error')
             return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

        try:
            msg = Message(
                sender_id=user_id,
                receiver_id=receiver.id,
                subject=subject_form,
                body=body
            )
            db.session.add(msg)
            db.session.commit()
            flash('Message sent successfully!', 'success')
            return redirect(url_for('inbox'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error sending message: {e}', 'error')
            # Log the error
            print(f"Error sending message: {e}")
            return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

    # GET request
    return render_template('compose.html', recipient=recipient, subject=subject)

@app.route('/compose-to-student-parent', methods=['GET', 'POST'])
@login_required
def compose_to_student_parent():
    """Enhanced compose route for teachers/admins to send messages to both student and parent"""
    user_id = session['user_id']
    user_role = session['user_role']

    # Only allow teachers and admins to use this feature
    if user_role not in ['teacher', 'admin']:
        flash('You do not have permission to access this feature.', 'error')
        return redirect(url_for('inbox'))

    if request.method == 'POST':
        student_id = request.form.get('student_id')
        subject_form = request.form.get('subject', '').strip()
        body = request.form.get('body', '').strip()

        # Validation
        if not student_id:
            flash('Please select a student.', 'error')
            students = User.query.filter_by(role='student', is_verified=True).order_by(User.name).all()
            return render_template('compose_to_student_parent.html', students=students, subject=subject_form, body=body)

        if not body:
            flash('Message body cannot be empty.', 'error')
            students = User.query.filter_by(role='student', is_verified=True).order_by(User.name).all()
            return render_template('compose_to_student_parent.html', students=students, subject=subject_form, body=body)

        # Get the student
        student = User.query.get(student_id)
        if not student or student.role != 'student':
            flash('Invalid student selected.', 'error')
            students = User.query.filter_by(role='student', is_verified=True).order_by(User.name).all()
            return render_template('compose_to_student_parent.html', students=students, subject=subject_form, body=body)

        # Get the parent
        parent = student.get_parent()

        messages_sent = []
        errors = []

        try:
            # Generate a unique thread ID for this broadcast
            import uuid
            thread_id = f"broadcast_{uuid.uuid4().hex[:8]}_{int(datetime.utcnow().timestamp())}"

            # Send message to student
            student_msg = Message(
                sender_id=user_id,
                receiver_id=student.id,
                subject=subject_form,
                body=body,
                thread_id=thread_id,
                message_type='student_parent_broadcast'
            )
            db.session.add(student_msg)
            messages_sent.append(f"Student: {student.name}")

            # Send message to parent (if parent exists)
            if parent:
                parent_msg = Message(
                    sender_id=user_id,
                    receiver_id=parent.id,
                    subject=subject_form,
                    body=body,
                    thread_id=thread_id,
                    message_type='student_parent_broadcast'
                )
                db.session.add(parent_msg)
                messages_sent.append(f"Parent: {parent.name}")
            else:
                errors.append(f"No parent found for {student.name}")

            db.session.commit()

            # Create success message
            if messages_sent:
                success_msg = f"Message sent successfully to: {', '.join(messages_sent)}"
                if errors:
                    success_msg += f". Note: {', '.join(errors)}"
                flash(success_msg, 'success')
            else:
                flash('No messages were sent.', 'error')

            return redirect(url_for('inbox'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error sending message: {e}', 'error')
            print(f"Error sending message to student+parent: {e}")
            students = User.query.filter_by(role='student', is_verified=True).order_by(User.name).all()
            return render_template('compose_to_student_parent.html', students=students, subject=subject_form, body=body)

    # GET request - show the form
    students = User.query.filter_by(role='student', is_verified=True).order_by(User.name).all()
    return render_template('compose_to_student_parent.html', students=students)

@app.route('/api/student/<int:student_id>/parent')
@login_required
def get_student_parent_info(student_id):
    """API endpoint to get student and parent information"""
    user_role = session['user_role']

    # Only allow teachers and admins
    if user_role not in ['teacher', 'admin']:
        return jsonify({'error': 'Unauthorized'}), 403

    student = User.query.get(student_id)
    if not student or student.role != 'student':
        return jsonify({'error': 'Student not found'}), 404

    parent = student.get_parent()

    return jsonify({
        'student': {
            'id': student.id,
            'name': student.name,
            'email': student.email
        },
        'parent': {
            'id': parent.id,
            'name': parent.name,
            'email': parent.email
        } if parent else None
    })

@app.route('/inbox')
@login_required
def inbox():
    user_id = session['user_id']
    # Fetch messages where the current user is the receiver
    # Eager load sender info to avoid N+1 queries in the template
    received_messages = Message.query\
        .options(db.joinedload(Message.sender)) \
        .filter_by(receiver_id=user_id) \
        .order_by(Message.timestamp.desc()) \
        .all()
    return render_template('inbox.html', messages=received_messages)

@app.route('/sent')
@login_required
def sent_messages():
    user_id = session['user_id']
    # Fetch messages where the current user is the sender
    # Eager load receiver info
    sent_messages = Message.query \
        .options(db.joinedload(Message.receiver)) \
        .filter_by(sender_id=user_id) \
        .order_by(Message.timestamp.desc()) \
        .all()
    return render_template('sent.html', messages=sent_messages)

@app.route('/message/<int:message_id>')
@login_required
def view_message(message_id):
    user_id = session['user_id']
    message = Message.query.options(\
        db.joinedload(Message.sender), \
        db.joinedload(Message.receiver) \
    ).get_or_404(message_id)

    # Check if the user is either the sender or receiver
    if user_id != message.sender_id and user_id != message.receiver_id:
        flash('You do not have permission to view this message.', 'error')
        return redirect(url_for('inbox'))

    # Mark as read if the current user is the receiver and it's unread
    was_read = message.is_read # Store original status
    if user_id == message.receiver_id and not message.is_read:
        message.is_read = True
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"Error marking message as read: {e}") # Log error but proceed
            message.is_read = was_read # Revert optimistic update on error

    return render_template('message_detail.html', message=message)

# --- Report Card Routes ---

# Route for Teachers to view/edit student report comments
@app.route('/teacher/student-reports', methods=['GET', 'POST'])
@login_required
def manage_student_reports():
    if session['user_role'] != 'teacher':
        flash('Access denied.', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        student_id = request.form.get('student_id')
        comment = request.form.get('report_comment', '').strip()
        student = User.query.get(student_id)

        # Basic validation/authorization (e.g., check if teacher is allowed to comment on this student)
        if not student or student.role != 'student':
             flash('Invalid student selected.', 'error')
        else:
            # Add more specific auth checks if needed
            try:
                student.report_comment = comment
                db.session.commit()
                flash(f"Comment for {student.name} updated.", 'success')
            except Exception as e:
                db.session.rollback()
                flash(f"Error updating comment: {e}", 'error')
                print(f"Error updating comment: {e}")

        return redirect(url_for('manage_student_reports')) # Redirect back to the management page

    # GET Request: List students (potentially only those linked to the teacher)
    # Simplified: Show all students for now. Needs refinement based on teacher-student links.
    students = User.query.filter_by(role='student').order_by(User.name).all()
    return render_template('manage_student_reports.html', students=students)

# Route for Parents to view their child's report card
@app.route('/parent/report-card/<int:child_id>')
@login_required
def view_report_card(child_id):
    if session['user_role'] != 'parent':
        flash('Access denied.', 'error')
        return redirect(url_for('dashboard'))

    parent_email = User.query.get(session['user_id']).email
    child = User.query.get_or_404(child_id)

    # Verify this is the parent's child
    if child.role != 'student' or child.parent_email != parent_email:
        flash('You are not authorized to view this report card.', 'error')
        return redirect(url_for('parent_dashboard'))

    # Fetch quiz attempts
    attempts = QuizAttempt.query.filter_by(student_id=child_id)\
        .join(Quiz)\
        .order_by(QuizAttempt.submitted_at.asc())\
        .all()

    # Calculate cumulative average
    total_attempts = len(attempts)
    cumulative_score = sum(attempt.score for attempt in attempts)
    average_score = cumulative_score / total_attempts if total_attempts > 0 else 0

    # Add grades to attempts
    for attempt in attempts:
        attempt.grade = calculate_grade(attempt.score, attempt.quiz) # Use quiz-specific thresholds

    # Optional: PDF Export Logic
    if request.args.get('format') == 'pdf':
        # This requires a PDF generation library like WeasyPrint or pdfkit
        # html = render_template('report_card_pdf.html', child=child, attempts=attempts, average_score=average_score)
        # pdf = generate_pdf_from_html(html) # Placeholder for your PDF generation function
        # response = make_response(pdf)
        # response.headers['Content-Type'] = 'application/pdf'
        # response.headers['Content-Disposition'] = f'inline; filename=report_card_{child.name}.pdf'
        # return response
        flash('PDF export not yet implemented.', 'info')
        # Fall through to render HTML if PDF fails or isn't implemented

    return render_template('report_card.html',
                           child=child,
                           attempts=attempts,
                           average_score=average_score)

# Decorator for routes requiring admin privileges
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
        if session.get('user_role') != 'admin':
            flash('You do not have permission to access this page.', 'error')
            # Redirect non-admins away, perhaps to their own dashboard or home
            user_role = session.get('user_role', 'guest') # Default to 'guest' if role somehow missing
            if user_role == 'teacher':
                return redirect(url_for('teacher_dashboard'))
            elif user_role == 'student':
                return redirect(url_for('student_dashboard'))
            elif user_role == 'parent':
                return redirect(url_for('parent_dashboard'))
            else:
                return redirect(url_for('home')) # Fallback redirect
        return f(*args, **kwargs)
    return decorated_function

@app.route('/admin/dashboard')
@admin_required # Use the new decorator
def admin_dashboard():
    # You can add logic here later to fetch stats for the dashboard
    return render_template('admin/admin_dashboard.html')

@app.route('/admin/users')
@admin_required
def admin_manage_users():
    users = User.query.order_by(User.role, User.name).all()
    return render_template('admin/users.html', users=users)

@app.route('/admin/pending-users')
@admin_required
def admin_pending_users():
    pending_users = User.query.filter_by(is_verified=False).order_by(User.role, User.name).all()
    return render_template('admin/pending_users.html', users=pending_users)

@app.route('/verify-user/<int:user_id>/<token>', methods=['GET'])
def verify_user_by_token(user_id, token):
    """Route for verifying users via email link - doesn't require admin login"""
    user = User.query.get_or_404(user_id)

    # Verify the token matches
    if user.verification_token != token:
        flash('Invalid verification token.', 'error')
        if 'user_id' in session and session.get('user_role') == 'admin':
            return redirect(url_for('admin_pending_users'))
        else:
            return redirect(url_for('login'))

    # Verify the user
    user.is_verified = True
    user.verification_token = None  # Clear the token after verification

    try:
        db.session.commit()

        # If this is a teacher account, ensure they have the preloaded quizzes
        if user.role == 'teacher' and not Quiz.query.filter_by(teacher_id=user.id).first():
            create_preloaded_hard_quiz(user.id)
            print(f"Added preloaded quiz to teacher {user.name} on verification")

        # Send confirmation email to the user
        msg = MailMessage(
            subject='Your Account Has Been Verified',
            recipients=[user.email]
        )
        msg.body = f'''
Dear {user.name},

Your account has been verified by an administrator. You can now log in to the Quiz Management System.

Thank you,
Quiz Management System Team
'''
        mail.send(msg)

        flash(f'User {user.name} has been verified successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Error verifying user: {e}")
        flash(f'Error verifying user: {e}', 'error')

    # If admin is logged in, redirect to admin panel, otherwise to login page
    if 'user_id' in session and session.get('user_role') == 'admin':
        return redirect(url_for('admin_pending_users'))
    else:
        return redirect(url_for('login'))

@app.route('/admin/verify-user/<int:user_id>/<token>', methods=['GET'])
@admin_required
def admin_verify_user(user_id, token):
    """Route for verifying users from the admin panel - requires admin login"""
    user = User.query.get_or_404(user_id)

    # Verify the token matches
    if user.verification_token != token:
        flash('Invalid verification token.', 'error')
        return redirect(url_for('admin_pending_users'))

    # Verify the user
    user.is_verified = True
    user.verification_token = None  # Clear the token after verification

    try:
        db.session.commit()

        # If this is a teacher account, ensure they have the preloaded quizzes
        if user.role == 'teacher' and not Quiz.query.filter_by(teacher_id=user.id).first():
            create_preloaded_hard_quiz(user.id)
            print(f"Added preloaded quiz to teacher {user.name} on admin verification")

        # Send confirmation email to the user
        msg = MailMessage(
            subject='Your Account Has Been Verified',
            recipients=[user.email]
        )
        msg.body = f'''
Dear {user.name},

Your account has been verified by an administrator. You can now log in to the Quiz Management System.

Thank you,
Quiz Management System Team
'''
        mail.send(msg)

        flash(f'User {user.name} has been verified successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Error verifying user: {e}")
        flash(f'Error verifying user: {e}', 'error')

    return redirect(url_for('admin_pending_users'))

@app.route('/admin/reject-user/<int:user_id>', methods=['POST'])
@admin_required
def admin_reject_user(user_id):
    user = User.query.get_or_404(user_id)
    user_email = user.email
    user_name = user.name

    try:
        # Delete the user
        db.session.delete(user)
        db.session.commit()

        # Send rejection email
        msg = MailMessage(
            subject='Account Registration Rejected',
            recipients=[user_email]
        )
        msg.body = f'''
Dear {user_name},

We regret to inform you that your account registration for the Quiz Management System has been rejected by an administrator.

If you believe this is an error, please contact the administrator directly.

Thank you,
Quiz Management System Team
'''
        mail.send(msg)

        flash(f'User {user_name} has been rejected and removed from the system.', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Error rejecting user: {e}")
        flash(f'Error rejecting user: {e}', 'error')

    return redirect(url_for('admin_pending_users'))

@app.route('/admin/users/edit/<int:user_id>', methods=['GET', 'POST'])
@admin_required
def admin_edit_user(user_id):
    user_to_edit = User.query.get_or_404(user_id)

    # Prevent admin from editing their own role or deleting themselves easily
    if user_to_edit.id == session['user_id'] and request.form.get('role') != 'admin':
         flash('Admins cannot change their own role.', 'error')
         return redirect(url_for('admin_edit_user', user_id=user_id))

    if request.method == 'POST':
        try:
            new_name = request.form['name'].strip()
            new_email = request.form['email'].strip().lower()
            new_role = request.form['role']
            new_parent_email = request.form.get('parent_email', '').strip().lower() or None

            # Basic Validation
            if not all([new_name, new_email, new_role]):
                 flash('Name, Email, and Role are required.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

            if not is_valid_email(new_email):
                 flash('Invalid email format.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

            # Check if email is being changed to one that already exists (excluding the current user)
            existing_user = User.query.filter(User.email == new_email, User.id != user_id).first()
            if existing_user:
                 flash('Email already exists for another user.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

             # Validate role-specific email formats (similar to signup)
            if new_role == 'student' and not new_email.endswith('@jpischool.com'):
                flash('Student email must end with @jpischool.com.', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)
            elif new_role == 'teacher' and not new_email.endswith('@jpischool.com'):
                flash('Teacher email must end with @jpischool.com.', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)
            elif new_role == 'parent' and not (new_email.endswith('@gmail.com') or new_email.endswith('@yahoo.com')):
                 flash(f'Parent email must end with @gmail.com or @yahoo.com.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

            # Validate parent email if role is student
            if new_role == 'student':
                if not new_parent_email:
                    flash('Parent email is required for students.', 'error')
                    return render_template('admin/edit_user.html', user=user_to_edit)
                if not is_valid_email(new_parent_email) or not (new_parent_email.endswith('@gmail.com') or new_parent_email.endswith('@yahoo.com')):
                     flash('Invalid format or domain for parent email (must be @gmail.com or @yahoo.com).', 'error')
                     return render_template('admin/edit_user.html', user=user_to_edit)
                # Check if parent exists
                parent_user = User.query.filter_by(email=new_parent_email, role='parent').first()
                if not parent_user:
                     flash('The specified parent email does not belong to a registered parent.', 'error')
                     return render_template('admin/edit_user.html', user=user_to_edit)
            else:
                 new_parent_email = None # Clear parent email if role is not student

            # Update user
            user_to_edit.name = new_name
            user_to_edit.email = new_email
            user_to_edit.role = new_role
            user_to_edit.parent_email = new_parent_email

            db.session.commit()
            flash(f'User {user_to_edit.name} updated successfully.', 'success')
            return redirect(url_for('admin_manage_users'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error updating user: {e}', 'error')
            print(f"Error editing user {user_id}: {e}\n{traceback.format_exc()}") # Log detailed error

    # GET request: Render the edit form
    return render_template('admin/edit_user.html', user=user_to_edit)

@app.route('/admin/users/edit-password/<int:user_id>', methods=['POST'])
@admin_required
def admin_edit_user_password(user_id):
    user_to_edit = User.query.get_or_404(user_id)
    new_password = request.form.get('new_password', '').strip()
    confirm_password = request.form.get('confirm_password', '').strip()

    # Validate new password
    if not new_password:
        flash('New password is required.', 'error')
        return redirect(url_for('admin_edit_user', user_id=user_id))

    if new_password != confirm_password:
        flash('New passwords do not match.', 'error')
        return redirect(url_for('admin_edit_user', user_id=user_id))

    if not is_strong_password(new_password):
        flash('Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a digit, and a special character.', 'error')
        return redirect(url_for('admin_edit_user', user_id=user_id))

    try:
        user_to_edit.password = generate_password_hash(new_password)
        user_to_edit.unhashed_password = new_password  # Store unhashed version
        db.session.commit()
        flash(f'Password for {user_to_edit.name} updated successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error updating password: {e}', 'error')
        print(f"Error updating password for user {user_id}: {e}\n{traceback.format_exc()}")

    return redirect(url_for('admin_edit_user', user_id=user_id))

@app.route('/admin/users/delete/<int:user_id>', methods=['POST'])
@admin_required
def admin_delete_user(user_id):
    user_to_delete = User.query.get_or_404(user_id)

    # Prevent admin from deleting themselves
    if user_to_delete.id == session['user_id']:
        flash('You cannot delete your own admin account.', 'error')
        return redirect(url_for('admin_manage_users'))

    # Prevent deletion of the last remaining admin account
    admin_count = User.query.filter_by(role='admin').count()
    if user_to_delete.role == 'admin' and admin_count <= 1:
        flash('Cannot delete the last admin account.', 'error')
        return redirect(url_for('admin_manage_users'))

    try:
        # --- Handle related data before deleting user ---
        # Example: Reassign quizzes if teacher, nullify attempts if student, delete messages?
        # For now, we will just delete the user. Consider FK constraints or soft delete later.

        # Delete related messages (sent or received)
        Message.query.filter(or_(Message.sender_id == user_id, Message.receiver_id == user_id)).delete(synchronize_session=False)

        # Delete related quiz attempts (if student)
        if user_to_delete.role == 'student':
             QuizAttempt.query.filter_by(student_id=user_id).delete(synchronize_session=False)
             # Also delete associated answers implicitly due to attempt deletion or handle explicitly if needed
             # QuizAnswer.query.join(QuizAttempt).filter(QuizAttempt.student_id == user_id).delete(synchronize_session=False)

        # What to do with Quizzes if a Teacher is deleted?
        # Option 1: Delete them (cascading effect)
        # Option 2: Set teacher_id to NULL (if nullable)
        # Option 3: Reassign to a default admin/teacher (complex)
        # For now, let's delete the quizzes (assuming cascade delete isn't set up in model)
        if user_to_delete.role == 'teacher':
             quizzes_to_delete = Quiz.query.filter_by(teacher_id=user_id).all()
             for quiz in quizzes_to_delete:
                 Question.query.filter_by(quiz_id=quiz.id).delete(synchronize_session=False)
                 # Add deletion for QuizAttempts/Answers related to this quiz if necessary
                 QuizAttempt.query.filter_by(quiz_id=quiz.id).delete(synchronize_session=False)
                 # QuizAnswer ...
                 db.session.delete(quiz)

        # Now delete the user
        user_name = user_to_delete.name # Get name before deleting
        db.session.delete(user_to_delete)
        db.session.commit()
        flash(f'User {user_name} and their associated data (messages, attempts, quizzes) deleted successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting user: {e}', 'error')
        print(f"Error deleting user {user_id}: {e}\n{traceback.format_exc()}")

    return redirect(url_for('admin_manage_users'))

@app.route('/admin/quizzes')
@admin_required
def admin_manage_quizzes():
    # Eager load the teacher relationship to avoid N+1 queries
    quizzes = Quiz.query.options(db.joinedload(Quiz.teacher)).order_by(Quiz.created_at.desc()).all()
    return render_template('admin/quizzes.html', quizzes=quizzes)

@app.route('/admin/attempts')
@admin_required
def admin_quiz_attempts():
    from datetime import datetime, timedelta

    # Get time filter from query parameters
    time_filter = request.args.get('time_filter', 'All')

    # Validate time filter
    valid_filters = ['All', 'Last 7 days', 'Last 30 days', 'This Month']
    if time_filter not in valid_filters:
        time_filter = 'All'

    # Calculate date ranges based on filter
    now = datetime.utcnow()
    query = QuizAttempt.query.options(
        db.joinedload(QuizAttempt.student),
        db.joinedload(QuizAttempt.quiz)
    )

    if time_filter == 'Last 7 days':
        start_date = now - timedelta(days=7)
        query = query.filter(QuizAttempt.submitted_at >= start_date)
    elif time_filter == 'Last 30 days':
        start_date = now - timedelta(days=30)
        query = query.filter(QuizAttempt.submitted_at >= start_date)
    elif time_filter == 'This Month':
        start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        query = query.filter(QuizAttempt.submitted_at >= start_date)
    # For 'All', no additional filter is applied

    # Sort by date descending (most recent first)
    attempts = query.order_by(QuizAttempt.submitted_at.desc()).all()

    # Count of attempts shown
    attempts_count = len(attempts)

    return render_template('admin/attempts.html',
                         attempts=attempts,
                         time_filter=time_filter,
                         valid_filters=valid_filters,
                         attempts_count=attempts_count)

@app.route('/admin/statistics')
@admin_required
def admin_statistics():
    # Get user counts by role
    user_counts = {
        'admin': User.query.filter_by(role='admin').count(),
        'teacher': User.query.filter_by(role='teacher').count(),
        'student': User.query.filter_by(role='student').count(),
        'parent': User.query.filter_by(role='parent').count()
    }

    # Get quiz statistics
    total_quizzes = Quiz.query.count()
    total_questions = Question.query.count()
    total_attempts = QuizAttempt.query.count()

    # Calculate average scores
    avg_score = db.session.query(db.func.avg(QuizAttempt.score)).scalar() or 0

    # Get recent activity with eager loading
    recent_attempts = QuizAttempt.query\
        .options(
            db.joinedload(QuizAttempt.student),
            db.joinedload(QuizAttempt.quiz)
        )\
        .order_by(QuizAttempt.submitted_at.desc())\
        .limit(10)\
        .all()

    recent_messages = Message.query\
        .options(
            db.joinedload(Message.sender),
            db.joinedload(Message.receiver)
        )\
        .order_by(Message.timestamp.desc())\
        .limit(10)\
        .all()

    return render_template('admin/statistics.html',
                         user_counts=user_counts,
                         total_quizzes=total_quizzes,
                         total_questions=total_questions,
                         total_attempts=total_attempts,
                         avg_score=avg_score,
                         recent_attempts=recent_attempts,
                         recent_messages=recent_messages)

@app.route('/admin/settings', methods=['GET', 'POST'])
@admin_required
def admin_settings():
    if request.method == 'POST':
        # Handle settings updates
        try:
            # Example: Update session lifetime
            new_lifetime = int(request.form.get('session_lifetime', 7))
            app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=new_lifetime)

            # Add more settings as needed

            flash('Settings updated successfully!', 'success')
        except Exception as e:
            flash(f'Error updating settings: {e}', 'error')

    # Get current settings
    settings = {
        'session_lifetime': app.config['PERMANENT_SESSION_LIFETIME'].days,
        'max_quiz_questions': 20,  # Default value
        'min_quiz_questions': 5,   # Default value
        'max_quiz_time': 60,       # Default value in minutes
        'smtp_server': app.config.get('MAIL_SERVER', ''),
        'smtp_port': app.config.get('MAIL_PORT', ''),
        'smtp_username': app.config.get('MAIL_USERNAME', ''),
        'smtp_password': app.config.get('MAIL_PASSWORD', '')
    }

    return render_template('admin/settings.html', settings=settings)

def generate_reset_token(email):
    return serializer.dumps(email, salt=app.config['SECURITY_PASSWORD_SALT'])

def verify_reset_token(token, expiration=3600):
    try:
        email = serializer.loads(
            token,
            salt=app.config['SECURITY_PASSWORD_SALT'],
            max_age=expiration
        )
        return email
    except:
        return None

@app.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        user = User.query.filter_by(email=email).first()

        if user:
            # Generate token
            token = generate_reset_token(email)
            reset_url = url_for('reset_password', token=token, _external=True)

            # Send email
            try:
                msg = MailMessage(
                    subject='Password Reset Request',
                    recipients=[email],
                    body=f'''To reset your password, visit the following link:
{reset_url}

If you did not make this request then simply ignore this email.
''',
                    html=f'''<p>To reset your password, visit the following link:</p>
<p><a href="{reset_url}">{reset_url}</a></p>
<p>If you did not make this request then simply ignore this email.</p>
'''
                )
                mail.send(msg)
                flash('Password reset instructions have been sent to your email.', 'success')
            except Exception as e:
                print(f"Error sending reset email: {e}")
                flash('Error sending reset email. Please try again.', 'error')
        else:
            # Don't reveal if email exists or not
            flash('If your email is registered, you will receive password reset instructions.', 'info')

        return redirect(url_for('login'))

    return render_template('forgot_password.html')

@app.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    email = verify_reset_token(token)
    if not email:
        flash('The password reset link is invalid or has expired.', 'error')
        return redirect(url_for('forgot_password'))

    if request.method == 'POST':
        user = User.query.filter_by(email=email).first()
        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('forgot_password'))

        new_password = request.form.get('new_password', '').strip()
        confirm_password = request.form.get('confirm_password', '').strip()

        if not new_password:
            flash('New password is required.', 'error')
            return render_template('reset_password.html', token=token)

        if new_password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('reset_password.html', token=token)

        if not is_strong_password(new_password):
            flash('Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a digit, and a special character.', 'error')
            return render_template('reset_password.html', token=token)

        try:
            user.password = generate_password_hash(new_password)
            user.unhashed_password = new_password  # Update unhashed version too
            db.session.commit()
            flash('Your password has been updated successfully. Please login with your new password.', 'success')
            return redirect(url_for('login'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating password: {e}', 'error')
            return render_template('reset_password.html', token=token)

    return render_template('reset_password.html', token=token)

# === WORKSHEET GENERATION ROUTES ===

@app.route('/generate_worksheet', methods=['GET', 'POST'])
@login_required
def generate_worksheet():
    """Generate worksheets for teachers"""
    if session.get('user_role') not in ['teacher', 'admin']:
        flash('Access denied. Only teachers can generate worksheets.', 'error')
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            # Get form data
            selected_topics = request.form.getlist('topics')
            difficulty_level = int(request.form.get('difficulty_level', 1))
            question_count = int(request.form.get('question_count', 10))
            include_answers = 'include_answers' in request.form
            worksheet_title = request.form.get('worksheet_title', 'Math Worksheet')
            save_template = 'save_template' in request.form
            template_name = request.form.get('template_name', '')

            # Validate inputs
            if not selected_topics:
                flash('Please select at least one topic.', 'error')
                return redirect(url_for('generate_worksheet'))

            if question_count > 30:
                flash('Maximum 30 questions allowed per worksheet.', 'error')
                return redirect(url_for('generate_worksheet'))

            if question_count < 1:
                flash('Please select at least 1 question.', 'error')
                return redirect(url_for('generate_worksheet'))

            # Get questions based on criteria
            questions = get_filtered_questions(selected_topics, difficulty_level, question_count)

            if len(questions) < question_count:
                flash(f'Only {len(questions)} questions available for selected criteria. Showing all available questions.', 'warning')

            if not questions:
                flash('No questions found for the selected criteria.', 'error')
                return redirect(url_for('generate_worksheet'))

            # Save template if requested
            if save_template and template_name:
                save_worksheet_template(
                    template_name,
                    selected_topics,
                    difficulty_level,
                    question_count,
                    include_answers,
                    session['user_id']
                )
                flash(f'Worksheet template "{template_name}" saved successfully!', 'success')

            # Generate worksheet
            worksheet_data = {
                'title': worksheet_title,
                'questions': questions,
                'difficulty_level': difficulty_level,
                'include_answers': include_answers,
                'generated_at': datetime.now(),
                'teacher_name': session.get('user_name', 'Teacher')
            }

            # Save generated worksheet record
            generated_worksheet = GeneratedWorksheet(
                teacher_id=session['user_id'],
                title=worksheet_title,
                question_ids=json.dumps([q.id for q in questions]),
                difficulty_level=difficulty_level,
                include_answers=include_answers
            )
            db.session.add(generated_worksheet)
            db.session.commit()

            # Check if PDF export is requested
            if 'export_pdf' in request.form:
                return generate_worksheet_pdf(worksheet_data, generated_worksheet.id)
            else:
                return render_template('worksheet_display.html', worksheet=worksheet_data, worksheet_id=generated_worksheet.id)

        except Exception as e:
            flash(f'Error generating worksheet: {str(e)}', 'error')
            return redirect(url_for('generate_worksheet'))

    # GET request - show form
    topics = get_available_topics()
    saved_templates = WorksheetTemplate.query.filter_by(teacher_id=session['user_id']).order_by(WorksheetTemplate.updated_at.desc()).all()

    return render_template('generate_worksheet.html', topics=topics, saved_templates=saved_templates)

def get_filtered_questions(topic_names, difficulty_level, max_count):
    """Filter and randomly select questions based on criteria"""
    # Map difficulty level to quiz difficulty
    difficulty_map = {1: 'easy', 2: 'medium', 3: 'hard'}
    difficulty_str = difficulty_map.get(difficulty_level, 'medium')

    # For now, we'll use quiz titles as topics since we don't have a topic system yet
    # This is a simplified approach - in a real system, you'd have proper topic categorization
    topic_keywords = {
        'Algebra': ['algebra', 'equation', 'variable', 'expression'],
        'Geometry': ['geometry', 'area', 'volume', 'angle', 'triangle', 'circle', 'rectangle'],
        'Fractions': ['fraction', 'decimal', 'percentage', 'ratio'],
        'Arithmetic': ['addition', 'subtraction', 'multiplication', 'division', 'number'],
        'Statistics': ['average', 'mean', 'median', 'mode', 'data', 'graph'],
        'Measurement': ['length', 'weight', 'time', 'distance', 'unit']
    }

    # Build query to find questions
    query = db.session.query(Question).join(Quiz)

    # Filter by difficulty
    query = query.filter(Quiz.difficulty == difficulty_str)

    # Filter by topics (search in quiz titles and question text)
    topic_conditions = []
    for topic_name in topic_names:
        if topic_name in topic_keywords:
            keywords = topic_keywords[topic_name]
            for keyword in keywords:
                topic_conditions.append(Quiz.title.ilike(f'%{keyword}%'))
                topic_conditions.append(Question.question_text.ilike(f'%{keyword}%'))

    if topic_conditions:
        query = query.filter(or_(*topic_conditions))

    # Get all matching questions
    all_questions = query.all()

    # Randomly select questions
    import random
    if len(all_questions) <= max_count:
        return all_questions
    else:
        return random.sample(all_questions, max_count)

def get_available_topics():
    """Get list of available topics"""
    # For now, return predefined topics
    # In a real system, this would query the Topic model
    return [
        {'name': 'Algebra', 'description': 'Algebraic expressions, equations, and variables'},
        {'name': 'Geometry', 'description': 'Shapes, areas, volumes, and spatial reasoning'},
        {'name': 'Fractions', 'description': 'Fractions, decimals, and percentages'},
        {'name': 'Arithmetic', 'description': 'Basic mathematical operations'},
        {'name': 'Statistics', 'description': 'Data analysis and probability'},
        {'name': 'Measurement', 'description': 'Units, conversions, and measurements'}
    ]

def save_worksheet_template(name, topics, difficulty, question_count, include_answers, teacher_id):
    """Save a worksheet template for future use"""
    template = WorksheetTemplate(
        name=name,
        teacher_id=teacher_id,
        difficulty_level=difficulty,
        question_count=question_count,
        include_answers=include_answers
    )
    db.session.add(template)
    db.session.flush()  # Get the template ID

    # Add topics to the template
    for topic_name in topics:
        # For now, we'll store topic names directly
        # In a real system, you'd reference Topic model IDs
        topic_record = WorksheetTopic(
            worksheet_template_id=template.id,
            topic_id=hash(topic_name) % 1000  # Temporary solution
        )
        db.session.add(topic_record)

    db.session.commit()

@app.route('/worksheet_pdf/<int:worksheet_id>')
@login_required
def download_worksheet_pdf(worksheet_id):
    """Download worksheet as PDF"""
    if session.get('user_role') not in ['teacher', 'admin']:
        flash('Access denied.', 'error')
        return redirect(url_for('login'))

    try:
        # Get worksheet data
        generated_worksheet = GeneratedWorksheet.query.get_or_404(worksheet_id)

        # Check if user owns this worksheet
        if generated_worksheet.teacher_id != session['user_id'] and session.get('user_role') != 'admin':
            flash('Access denied.', 'error')
            return redirect(url_for('generate_worksheet'))

        # Get questions
        question_ids = json.loads(generated_worksheet.question_ids)
        questions = Question.query.filter(Question.id.in_(question_ids)).all()

        # Prepare worksheet data
        worksheet_data = {
            'title': generated_worksheet.title,
            'questions': questions,
            'difficulty_level': generated_worksheet.difficulty_level,
            'include_answers': generated_worksheet.include_answers,
            'generated_at': generated_worksheet.generated_at,
            'teacher_name': session.get('user_name', 'Teacher')
        }

        # Update download count
        generated_worksheet.download_count += 1
        db.session.commit()

        return generate_worksheet_pdf(worksheet_data, worksheet_id)

    except Exception as e:
        flash(f'Error generating PDF: {str(e)}', 'error')
        return redirect(url_for('generate_worksheet'))

def generate_worksheet_pdf(worksheet_data, worksheet_id):
    """Generate PDF from worksheet data"""
    try:
        if WEASYPRINT_AVAILABLE:
            # Render HTML template
            html_content = render_template('worksheet_pdf.html', worksheet=worksheet_data)

            # Generate PDF using WeasyPrint
            pdf = weasyprint.HTML(string=html_content, base_url=request.url_root).write_pdf()

            # Create response
            response = make_response(pdf)
            response.headers['Content-Type'] = 'application/pdf'
            response.headers['Content-Disposition'] = f'attachment; filename="worksheet_{worksheet_id}.pdf"'

            return response
        else:
            # Fallback: Return HTML content with print-friendly styling
            html_content = render_template('worksheet_pdf.html', worksheet=worksheet_data)

            response = make_response(html_content)
            response.headers['Content-Type'] = 'text/html'
            response.headers['Content-Disposition'] = f'inline; filename="worksheet_{worksheet_id}.html"'

            flash('PDF generation not available. Displaying printable version instead. Use your browser\'s print function to create a PDF.', 'info')
            return response

    except Exception as e:
        flash(f'Error generating PDF: {str(e)}', 'error')
        return redirect(url_for('generate_worksheet'))

@app.route('/load_template/<int:template_id>')
@login_required
def load_worksheet_template(template_id):
    """Load a saved worksheet template"""
    if session.get('user_role') not in ['teacher', 'admin']:
        return jsonify({'error': 'Access denied'}), 403

    try:
        template = WorksheetTemplate.query.get_or_404(template_id)

        # Check if user owns this template
        if template.teacher_id != session['user_id'] and session.get('user_role') != 'admin':
            return jsonify({'error': 'Access denied'}), 403

        # Get topic names (simplified for now)
        topic_names = ['Algebra', 'Geometry', 'Fractions']  # This would be dynamic in a real system

        return jsonify({
            'name': template.name,
            'description': template.description,
            'topics': topic_names[:2],  # Simplified
            'difficulty_level': template.difficulty_level,
            'question_count': template.question_count,
            'include_answers': template.include_answers
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/delete_template/<int:template_id>', methods=['POST'])
@login_required
def delete_worksheet_template(template_id):
    """Delete a saved worksheet template"""
    if session.get('user_role') not in ['teacher', 'admin']:
        flash('Access denied.', 'error')
        return redirect(url_for('generate_worksheet'))

    try:
        template = WorksheetTemplate.query.get_or_404(template_id)

        # Check if user owns this template
        if template.teacher_id != session['user_id'] and session.get('user_role') != 'admin':
            flash('Access denied.', 'error')
            return redirect(url_for('generate_worksheet'))

        db.session.delete(template)
        db.session.commit()

        flash(f'Template "{template.name}" deleted successfully.', 'success')

    except Exception as e:
        flash(f'Error deleting template: {str(e)}', 'error')

    return redirect(url_for('generate_worksheet'))

@app.route('/my_worksheets')
@login_required
def my_worksheets():
    """View generated worksheets"""
    if session.get('user_role') not in ['teacher', 'admin']:
        flash('Access denied.', 'error')
        return redirect(url_for('login'))

    worksheets = GeneratedWorksheet.query.filter_by(teacher_id=session['user_id']).order_by(GeneratedWorksheet.generated_at.desc()).all()

    return render_template('my_worksheets.html', worksheets=worksheets)

# === MATH EXPRESSION EVALUATOR ROUTE ===

@app.route('/api/evaluate_expression', methods=['POST'])
def evaluate_expression():
    """
    Evaluate mathematical expressions using SymPy
    """
    try:
        data = request.get_json()
        expression = data.get('expression', '').strip()

        if not expression:
            return jsonify({
                'success': False,
                'error': 'No expression provided'
            })

        # Define common symbols
        x, y, z, t, a, b, c, n = symbols('x y z t a b c n')

        # Parse the expression
        try:
            # Convert common mathematical notation
            processed_expr = expression.replace('^', '**')  # x^2 -> x**2

            # Parse the expression
            expr = sympify(processed_expr)

            # Determine what type of operation to perform
            operation = data.get('operation', 'simplify')
            result = None
            result_latex = None

            if operation == 'simplify':
                result = simplify(expr)
                result_latex = latex(result)

            elif operation == 'expand':
                result = expand(expr)
                result_latex = latex(result)

            elif operation == 'factor':
                result = factor(expr)
                result_latex = latex(result)

            elif operation == 'differentiate':
                # Get the variable to differentiate with respect to
                diff_var = data.get('variable', 'x')
                var_symbol = symbols(diff_var)
                result = diff(expr, var_symbol)
                result_latex = latex(result)

            elif operation == 'integrate':
                # Get the variable to integrate with respect to
                int_var = data.get('variable', 'x')
                var_symbol = symbols(int_var)
                result = integrate(expr, var_symbol)
                result_latex = latex(result)

            elif operation == 'solve':
                # Get the variable to solve for
                solve_var = data.get('variable', 'x')
                var_symbol = symbols(solve_var)
                solutions = solve(expr, var_symbol)

                if solutions:
                    if len(solutions) == 1:
                        result = solutions[0]
                        result_latex = latex(result)
                    else:
                        result = solutions
                        result_latex = [latex(sol) for sol in solutions]
                else:
                    result = "No solutions found"
                    result_latex = "\\text{No solutions found}"

            else:
                # Default to simplify
                result = simplify(expr)
                result_latex = latex(result)

            # Get the free symbols (variables) in the expression
            free_symbols = list(expr.free_symbols)
            symbol_names = [str(sym) for sym in free_symbols]

            # Check if the expression is a constant
            is_constant = len(free_symbols) == 0

            # Try to evaluate numerically if it's a constant
            numerical_value = None
            if is_constant:
                try:
                    numerical_value = float(result)
                except:
                    pass

            return jsonify({
                'success': True,
                'original_expression': expression,
                'result': str(result),
                'result_latex': result_latex,
                'operation': operation,
                'variables': symbol_names,
                'is_constant': is_constant,
                'numerical_value': numerical_value,
                'expression_type': type(expr).__name__
            })

        except Exception as e:
            # Handle SymPy parsing errors
            return jsonify({
                'success': False,
                'error': f'Invalid mathematical expression: {str(e)}',
                'suggestion': 'Please check your syntax. Use * for multiplication, ** for exponents, and proper function names like sin(x), cos(x), etc.'
            })

    except Exception as e:
        # Handle general errors
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        })

if __name__ == '__main__':
    app.run(debug=True)
