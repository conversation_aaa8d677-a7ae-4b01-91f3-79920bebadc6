#!/usr/bin/env python3
import requests
import time

print('Testing navigation flow...')

# Test 1: Login page navigation
print('\n1. Testing login page navigation...')
response = requests.get('http://127.0.0.1:5000/login')
if response.status_code == 200:
    if '<header class="main-header">' not in response.text:
        print('✓ Login page correctly hides navigation header')
    else:
        print('✗ Login page still shows navigation header')
    
    if 'Logout' not in response.text:
        print('✓ Login page correctly hides logout button')
    else:
        print('✗ Login page still shows logout button')
else:
    print(f'✗ Failed to access login page: {response.status_code}')

# Test 2: Signup page navigation
print('\n2. Testing signup page navigation...')
response = requests.get('http://127.0.0.1:5000/signup')
if response.status_code == 200:
    if '<header class="main-header">' not in response.text:
        print('✓ Signup page correctly hides navigation header')
    else:
        print('✗ Signup page still shows navigation header')
    
    if 'Logout' not in response.text:
        print('✓ Signup page correctly hides logout button')
    else:
        print('✗ Signup page still shows logout button')
else:
    print(f'✗ Failed to access signup page: {response.status_code}')

# Test 3: Login and logout functionality
print('\n3. Testing login and logout functionality...')
session = requests.Session()
login_data = {'email': '<EMAIL>', 'password': 'Teacher123!'}
response = session.post('http://127.0.0.1:5000/login', data=login_data, allow_redirects=True)

if response.status_code == 200 and 'dashboard' in response.url:
    print('✓ Login successful')
    
    # Test dashboard has logout button
    if 'Logout' in response.text:
        print('✓ Dashboard has logout button')
    else:
        print('✗ Dashboard missing logout button')
    
    # Test logout
    response = session.get('http://127.0.0.1:5000/logout', allow_redirects=True)
    if response.status_code == 200 and 'login' in response.url:
        print('✓ Logout correctly redirects to login page')
    else:
        print(f'✗ Logout redirected to: {response.url}')
    
    # Test accessing dashboard after logout
    response = session.get('http://127.0.0.1:5000/teacher/dashboard', allow_redirects=True)
    if 'login' in response.url:
        print('✓ Dashboard access after logout redirects to login')
    else:
        print('✗ Dashboard access after logout does not redirect to login')
        
else:
    print(f'✗ Login failed: {response.status_code}, URL: {response.url}')

# Test 4: Logged-in user redirect from auth pages
print('\n4. Testing logged-in user redirect from auth pages...')
session = requests.Session()
login_data = {'email': '<EMAIL>', 'password': 'Teacher123!'}
response = session.post('http://127.0.0.1:5000/login', data=login_data, allow_redirects=False)

if response.status_code == 302:
    # Try to access login page while logged in
    response = session.get('http://127.0.0.1:5000/login', allow_redirects=False)
    if response.status_code == 302 and 'dashboard' in response.headers.get('Location', ''):
        print('✓ Logged-in user correctly redirected from login page')
    else:
        print(f'✗ Logged-in user not redirected from login page: {response.status_code}')
    
    # Try to access signup page while logged in
    response = session.get('http://127.0.0.1:5000/signup', allow_redirects=False)
    if response.status_code == 302 and 'dashboard' in response.headers.get('Location', ''):
        print('✓ Logged-in user correctly redirected from signup page')
    else:
        print(f'✗ Logged-in user not redirected from signup page: {response.status_code}')

print('\nNavigation flow test completed.')
