#!/usr/bin/env python3
"""
Comprehensive test for teacher email validation in signup and login
"""

import requests
import json
from app import app, db, User
from werkzeug.security import generate_password_hash

def test_backend_validation():
    """Test backend validation by making direct API calls"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 Testing Backend Validation")
    print("=" * 50)
    
    # Test cases for signup
    test_cases = [
        {
            "name": "Valid Teacher Signup",
            "data": {
                'name': 'Valid Teacher',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'confirm_password': 'TestPass123!',
                'role': 'teacher'
            },
            "expected": "success"
        },
        {
            "name": "Invalid Teacher Gmail",
            "data": {
                'name': 'Invalid Teacher',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'confirm_password': 'TestPass123!',
                'role': 'teacher'
            },
            "expected": "error"
        },
        {
            "name": "Invalid Teacher Yahoo",
            "data": {
                'name': 'Invalid Teacher',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'confirm_password': 'TestPass123!',
                'role': 'teacher'
            },
            "expected": "error"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📝 {test_case['name']}")
        print(f"   Email: {test_case['data']['email']}")
        
        try:
            response = requests.post(f"{base_url}/signup", data=test_case['data'], allow_redirects=False)
            
            if response.status_code == 302:
                location = response.headers.get('Location', '')
                if 'verify-otp' in location:
                    result = "success"
                    print(f"   ✅ SUCCESS - Redirected to OTP verification")
                elif 'signup' in location:
                    result = "error"
                    print(f"   ✅ ERROR - Validation failed as expected")
                else:
                    result = "unknown"
                    print(f"   ❓ UNKNOWN - Redirected to {location}")
            else:
                result = "error"
                print(f"   ❌ ERROR - Unexpected status code {response.status_code}")
            
            # Check if result matches expectation
            if result == test_case['expected']:
                print(f"   ✅ PASS")
            else:
                print(f"   ❌ FAIL - Expected {test_case['expected']}, got {result}")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")

def test_database_validation():
    """Test validation by checking database constraints"""
    print("\n🗄️  Testing Database Validation")
    print("=" * 50)
    
    try:
        with app.app_context():
            # Check existing teachers
            teachers = User.query.filter_by(role='teacher').all()
            print(f"\n📊 Current teachers in database: {len(teachers)}")
            
            for teacher in teachers:
                domain_status = "✅ Valid" if teacher.email.endswith('@jpischool.com') else "❌ Invalid"
                print(f"   {teacher.name}: {teacher.email} - {domain_status}")
            
            # Count valid vs invalid
            valid_count = sum(1 for t in teachers if t.email.endswith('@jpischool.com'))
            invalid_count = len(teachers) - valid_count
            
            print(f"\n📈 Summary:")
            print(f"   Valid (@jpischool.com): {valid_count}")
            print(f"   Invalid (other domains): {invalid_count}")
            
            if invalid_count > 0:
                print(f"\n⚠️  Warning: {invalid_count} teacher(s) have invalid email domains")
                print("   Consider running the migration script to update them.")
            else:
                print(f"\n✅ All teachers have valid email domains!")
    
    except Exception as e:
        print(f"❌ Database error: {e}")

def test_login_validation():
    """Test that existing teachers can still log in"""
    print("\n🔐 Testing Login Validation")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    try:
        with app.app_context():
            # Get existing teachers
            teachers = User.query.filter_by(role='teacher').all()
            
            for teacher in teachers:
                print(f"\n👤 Testing login for: {teacher.name} ({teacher.email})")
                
                # Test login
                login_data = {
                    'email': teacher.email,
                    'password': teacher.unhashed_password  # Use stored unhashed password
                }
                
                try:
                    response = requests.post(f"{base_url}/login", data=login_data, allow_redirects=False)
                    
                    if response.status_code == 302:
                        location = response.headers.get('Location', '')
                        if 'dashboard' in location:
                            print(f"   ✅ Login successful - Redirected to dashboard")
                        elif 'login' in location:
                            print(f"   ❌ Login failed - Redirected back to login")
                        else:
                            print(f"   ❓ Unexpected redirect to: {location}")
                    else:
                        print(f"   ❌ Unexpected status code: {response.status_code}")
                        
                except Exception as e:
                    print(f"   ❌ Login test failed: {e}")
    
    except Exception as e:
        print(f"❌ Login test error: {e}")

def run_all_tests():
    """Run all validation tests"""
    print("🚀 Starting Comprehensive Teacher Email Validation Tests")
    print("=" * 70)
    
    test_backend_validation()
    test_database_validation()
    test_login_validation()
    
    print("\n" + "=" * 70)
    print("🏁 All tests completed!")
    print("\n💡 Next steps:")
    print("   1. If any teachers have invalid domains, run migrate_teacher_emails.py")
    print("   2. Test the frontend validation by visiting http://127.0.0.1:5000/signup")
    print("   3. Verify admin edit user functionality works correctly")

if __name__ == "__main__":
    run_all_tests()
