#!/usr/bin/env python3
"""
Math Rendering Test Suite
========================

This script tests the math expression detection and LaTeX conversion functionality
for the Flask quiz application.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import detect_math_expressions, convert_to_latex, wrap_math_expressions, process_text_for_math

def test_math_detection():
    """Test math expression detection"""
    print("Testing math expression detection...")
    
    test_cases = [
        ("x^2 + 3x = 4", True),
        ("sqrt(16) = 4", True),
        ("sin(30) = 0.5", True),
        ("a = (b^2 + c^2 - 2bc*cos(A))^0.5", True),
        ("Hello world", False),
        ("The answer is 42", False),
        ("2 + 2 = 4", True),
        ("log(100) = 2", True),
        ("alpha + beta = gamma", True),
        ("This is just text", False),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for text, should_have_math in test_cases:
        matches = detect_math_expressions(text)
        has_math = len(matches) > 0
        
        if has_math == should_have_math:
            print(f"✓ '{text}' -> {'Math detected' if has_math else 'No math'}")
            passed += 1
        else:
            print(f"✗ '{text}' -> Expected {'math' if should_have_math else 'no math'}, got {'math' if has_math else 'no math'}")
    
    print(f"Math detection: {passed}/{total} tests passed\n")
    return passed == total

def test_latex_conversion():
    """Test LaTeX conversion"""
    print("Testing LaTeX conversion...")
    
    test_cases = [
        ("x^2", "x^{2}"),
        ("sqrt(16)", "\\sqrt{16}"),
        ("sin(30)", "\\sin(30)"),
        ("a/b", "\\frac{a}{b}"),
        ("alpha", "\\alpha"),
        ("pi", "\\pi"),
        ("infinity", "\\infty"),
        ("2*3", "2\\cdot3"),
        ("x <= y", "x \\leq y"),
        ("a >= b", "a \\geq b"),
        ("x != y", "x \\neq y"),
        ("+/-", "\\pm"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for input_text, expected in test_cases:
        result = convert_to_latex(input_text)
        
        if expected in result:
            print(f"✓ '{input_text}' -> '{result}'")
            passed += 1
        else:
            print(f"✗ '{input_text}' -> '{result}' (expected to contain '{expected}')")
    
    print(f"LaTeX conversion: {passed}/{total} tests passed\n")
    return passed == total

def test_math_wrapping():
    """Test math expression wrapping"""
    print("Testing math expression wrapping...")
    
    test_cases = [
        ("x^2 + 3x = 4", True),  # Should be wrapped
        ("Hello world", False),  # Should not be wrapped
        ("2 + 2 = 4", True),     # Should be wrapped
        ("$$x^2$$", False),      # Already wrapped, should not change
        ("\\(x^2\\)", False),    # Already wrapped, should not change
    ]
    
    passed = 0
    total = len(test_cases)
    
    for text, should_wrap in test_cases:
        result = wrap_math_expressions(text, inline=True)
        # Check if the function actually modified the text (added wrapping)
        was_modified = result != text

        if was_modified == should_wrap:
            print(f"✓ '{text}' -> {'Wrapped' if was_modified else 'Not wrapped'}")
            passed += 1
        else:
            print(f"✗ '{text}' -> Expected {'wrapped' if should_wrap else 'not wrapped'}, got {'wrapped' if was_modified else 'not wrapped'}")
    
    print(f"Math wrapping: {passed}/{total} tests passed\n")
    return passed == total

def test_text_processing():
    """Test complete text processing"""
    print("Testing complete text processing...")
    
    test_cases = [
        "Solve for x: x^2 + 3x - 4 = 0",
        "The quadratic formula is: x = (-b +/- sqrt(b^2 - 4ac)) / 2a",
        "Find the value of sin(30) + cos(60)",
        "Calculate log(100) + ln(e)",
        "The area of a circle is pi * r^2",
        "This is just regular text without math",
        "Mixed content: The equation x^2 = 4 has solutions x = +/-2",
    ]
    
    print("Processing sample texts:")
    for text in test_cases:
        result = process_text_for_math(text)
        has_latex = '\\(' in result or '$$' in result
        print(f"Input:  {text}")
        print(f"Output: {result}")
        print(f"LaTeX:  {'Yes' if has_latex else 'No'}")
        print("-" * 60)
    
    return True

def test_complex_expressions():
    """Test complex mathematical expressions"""
    print("Testing complex mathematical expressions...")
    
    complex_cases = [
        "a = (b^2 + c^2 - 2bc*cos(A))^0.5",
        "f(x) = sin(x) + cos(x) + tan(x)",
        "integral from 0 to infinity of e^(-x) dx = 1",
        "lim as x approaches 0 of sin(x)/x = 1",
        "The derivative of x^n is n*x^(n-1)",
        "E = mc^2 where c is the speed of light",
        "The Pythagorean theorem: a^2 + b^2 = c^2",
    ]
    
    print("Processing complex expressions:")
    for expr in complex_cases:
        latex_result = convert_to_latex(expr)
        wrapped_result = wrap_math_expressions(expr, inline=False)
        
        print(f"Original: {expr}")
        print(f"LaTeX:    {latex_result}")
        print(f"Wrapped:  {wrapped_result}")
        print("-" * 60)
    
    return True

def main():
    """Run all tests"""
    print("Math Rendering Test Suite")
    print("=" * 50)
    
    tests = [
        ("Math Detection", test_math_detection),
        ("LaTeX Conversion", test_latex_conversion),
        ("Math Wrapping", test_math_wrapping),
        ("Text Processing", test_text_processing),
        ("Complex Expressions", test_complex_expressions),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed_tests}/{total_tests} test suites passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Math rendering is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
