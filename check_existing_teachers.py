#!/usr/bin/env python3
"""
Check existing teachers in the database to see their email domains
"""

from app import app, db, User

def check_existing_teachers():
    """Check existing teachers and their email domains"""

    try:
        with app.app_context():
            # Query for all teachers
            teachers = User.query.filter_by(role='teacher').all()

            print("Existing Teachers in Database:")
            print("=" * 60)

            if not teachers:
                print("No teachers found in database.")
            else:
                print(f"{'ID':<5} {'Name':<20} {'Email':<30} {'Verified':<10}")
                print("-" * 60)

                jpischool_count = 0
                other_domain_count = 0

                for teacher in teachers:
                    verified_status = "Yes" if teacher.is_verified else "No"
                    print(f"{teacher.id:<5} {teacher.name:<20} {teacher.email:<30} {verified_status:<10}")

                    if teacher.email.endswith('@jpischool.com'):
                        jpischool_count += 1
                    else:
                        other_domain_count += 1

                print("-" * 60)
                print(f"Total teachers: {len(teachers)}")
                print(f"Teachers with @jpischool.com: {jpischool_count}")
                print(f"Teachers with other domains: {other_domain_count}")

                if other_domain_count > 0:
                    print("\n⚠️  WARNING: Found <NAME_EMAIL> domains!")
                    print("These teachers may need their email addresses updated to comply with the new validation rules.")
                    print("\<NAME_EMAIL> domains:")
                    for teacher in teachers:
                        if not teacher.email.endswith('@jpischool.com'):
                            print(f"  - {teacher.name} ({teacher.email})")

    except Exception as e:
        print(f"Error checking database: {e}")

if __name__ == "__main__":
    check_existing_teachers()
