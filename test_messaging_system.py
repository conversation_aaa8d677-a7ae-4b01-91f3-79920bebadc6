#!/usr/bin/env python3
"""
Test Script for Enhanced Messaging System
Tests the student+parent messaging functionality
"""

import requests
import sqlite3
import os
from datetime import datetime

class MessagingSystemTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def get_database_path(self):
        """Get the path to the SQLite database"""
        if os.path.exists('instance/quiz_management.db'):
            return 'instance/quiz_management.db'
        elif os.path.exists('quiz_management.db'):
            return 'quiz_management.db'
        else:
            return None
    
    def test_database_schema(self):
        """Test that the database has the required messaging enhancements"""
        print("=== Testing Database Schema ===")
        
        db_path = self.get_database_path()
        if not db_path:
            print("❌ FAILED: Database not found")
            return False
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check message table structure
            cursor.execute("PRAGMA table_info(message)")
            columns = {row[1]: row[2] for row in cursor.fetchall()}
            
            required_columns = {
                'id': 'INTEGER',
                'sender_id': 'INTEGER',
                'receiver_id': 'INTEGER',
                'subject': 'VARCHAR(200)',
                'body': 'TEXT',
                'timestamp': 'DATETIME',
                'is_read': 'BOOLEAN',
                'thread_id': 'VARCHAR(50)',
                'message_type': 'VARCHAR(20)'
            }
            
            for col_name, col_type in required_columns.items():
                if col_name not in columns:
                    print(f"❌ FAILED: Missing column {col_name}")
                    return False
                print(f"✓ Column {col_name} exists")
            
            # Check for parent-student relationships
            cursor.execute("""
                SELECT COUNT(*) FROM user s 
                JOIN user p ON s.parent_email = p.email 
                WHERE s.role = 'student' AND p.role = 'parent'
            """)
            parent_child_links = cursor.fetchone()[0]
            print(f"✓ Found {parent_child_links} parent-child relationships")
            
            # Check for teachers/admins
            cursor.execute("SELECT COUNT(*) FROM user WHERE role IN ('teacher', 'admin')")
            teachers_admins = cursor.fetchone()[0]
            print(f"✓ Found {teachers_admins} teachers/admins")
            
            # Check for students
            cursor.execute("SELECT COUNT(*) FROM user WHERE role = 'student'")
            students = cursor.fetchone()[0]
            print(f"✓ Found {students} students")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ FAILED: Database error: {e}")
            return False
    
    def login_as_teacher(self):
        """Login as a teacher to test messaging functionality"""
        print("\n=== Testing Teacher Login ===")
        
        try:
            # Get login page
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code != 200:
                print(f"❌ FAILED: Could not access login page (status: {response.status_code})")
                return False
            
            # Find a teacher account from database
            db_path = self.get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT email, unhashed_password FROM user WHERE role = 'teacher' LIMIT 1")
            teacher = cursor.fetchone()
            conn.close()
            
            if not teacher:
                print("❌ FAILED: No teacher account found in database")
                return False
            
            email, password = teacher
            
            # Login
            login_data = {
                'email': email,
                'password': password
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            
            if response.status_code == 200 and 'dashboard' in response.url:
                print(f"✓ Successfully logged in as teacher: {email}")
                return True
            else:
                print(f"❌ FAILED: Login failed (status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"❌ FAILED: Login error: {e}")
            return False
    
    def test_compose_to_student_parent_page(self):
        """Test that the compose to student+parent page loads correctly"""
        print("\n=== Testing Compose to Student+Parent Page ===")
        
        try:
            response = self.session.get(f"{self.base_url}/compose-to-student-parent")
            
            if response.status_code != 200:
                print(f"❌ FAILED: Could not access compose page (status: {response.status_code})")
                return False
            
            content = response.text
            
            # Check for required elements
            required_elements = [
                'Select Student:',
                'name="student_id"',
                'name="subject"',
                'name="body"',
                'Send to Student & Parent',
                'updateRecipientInfo()'
            ]
            
            for element in required_elements:
                if element in content:
                    print(f"✓ Found required element: {element}")
                else:
                    print(f"❌ FAILED: Missing element: {element}")
                    return False
            
            # Check for student options
            if '<option value="">-- Choose a student --</option>' in content:
                print("✓ Student dropdown found")
            else:
                print("❌ FAILED: Student dropdown not found")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ FAILED: Page test error: {e}")
            return False
    
    def test_student_parent_api(self):
        """Test the API endpoint for getting student+parent info"""
        print("\n=== Testing Student+Parent API ===")
        
        try:
            # Get a student ID from database
            db_path = self.get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT s.id, s.name, s.parent_email, p.id as parent_id, p.name as parent_name
                FROM user s 
                LEFT JOIN user p ON s.parent_email = p.email AND p.role = 'parent'
                WHERE s.role = 'student' 
                LIMIT 1
            """)
            student_data = cursor.fetchone()
            conn.close()
            
            if not student_data:
                print("❌ FAILED: No student found in database")
                return False
            
            student_id, student_name, parent_email, parent_id, parent_name = student_data
            
            # Test API endpoint
            response = self.session.get(f"{self.base_url}/api/student/{student_id}/parent")
            
            if response.status_code != 200:
                print(f"❌ FAILED: API request failed (status: {response.status_code})")
                return False
            
            data = response.json()
            
            # Verify response structure
            if 'student' not in data:
                print("❌ FAILED: Missing student data in API response")
                return False
            
            if data['student']['name'] != student_name:
                print(f"❌ FAILED: Student name mismatch: expected {student_name}, got {data['student']['name']}")
                return False
            
            print(f"✓ API returned correct student data: {student_name}")
            
            if parent_id:
                if 'parent' not in data or not data['parent']:
                    print("❌ FAILED: Missing parent data in API response")
                    return False
                
                if data['parent']['name'] != parent_name:
                    print(f"❌ FAILED: Parent name mismatch: expected {parent_name}, got {data['parent']['name']}")
                    return False
                
                print(f"✓ API returned correct parent data: {parent_name}")
            else:
                if data['parent'] is not None:
                    print("❌ FAILED: Expected null parent but got data")
                    return False
                
                print("✓ API correctly returned null for student without parent")
            
            return True
            
        except Exception as e:
            print(f"❌ FAILED: API test error: {e}")
            return False
    
    def test_inbox_enhancements(self):
        """Test that the inbox shows the new messaging features"""
        print("\n=== Testing Inbox Enhancements ===")
        
        try:
            response = self.session.get(f"{self.base_url}/inbox")
            
            if response.status_code != 200:
                print(f"❌ FAILED: Could not access inbox (status: {response.status_code})")
                return False
            
            content = response.text
            
            # Check for enhanced messaging button
            if 'Send to Student & Parent' in content:
                print("✓ Found 'Send to Student & Parent' button in inbox")
            else:
                print("❌ FAILED: Missing 'Send to Student & Parent' button")
                return False
            
            # Check for message preview and badges
            if 'message-preview' in content:
                print("✓ Found message preview styling")
            else:
                print("⚠ Warning: Message preview styling not found (may be empty inbox)")
            
            if 'message-badge' in content:
                print("✓ Found message badge styling")
            else:
                print("⚠ Warning: Message badge styling not found (may be empty inbox)")
            
            return True
            
        except Exception as e:
            print(f"❌ FAILED: Inbox test error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all messaging system tests"""
        print("🧪 Starting Messaging System Tests")
        print("=" * 50)
        
        tests = [
            self.test_database_schema,
            self.login_as_teacher,
            self.test_compose_to_student_parent_page,
            self.test_student_parent_api,
            self.test_inbox_enhancements
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    print(f"❌ Test failed: {test.__name__}")
            except Exception as e:
                print(f"❌ Test error in {test.__name__}: {e}")
        
        print("\n" + "=" * 50)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Messaging system is working correctly.")
        else:
            print("⚠ Some tests failed. Please check the implementation.")
        
        return passed == total

def main():
    """Main test function"""
    print("Enhanced Messaging System Test Suite")
    print("====================================")
    print()
    
    tester = MessagingSystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All messaging tests passed!")
        print("The enhanced messaging system is ready to use.")
    else:
        print("\n❌ Some tests failed!")
        print("Please check the Flask application and database setup.")

if __name__ == "__main__":
    main()
