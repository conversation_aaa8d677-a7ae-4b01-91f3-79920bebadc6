{% extends "base.html" %}

{% block title %}Report Card - {{ child.name }}{% endblock %}

{% block content %}
<div class="container report-card-container">
    <div class="report-header">
        <h1>Report Card</h1>
        <h2>{{ child.name }}</h2>
        <div class="header-actions">
            <a href="{{ url_for('parent_dashboard') }}" class="btn btn-sm btn-secondary">Back to Dashboard</a>
            <a href="{{ url_for('parent_export_report_card', child_id=child.id) }}" class="btn btn-sm btn-danger pdf-export-btn">
                📄 Download PDF Report
            </a>
        </div>
    </div>

    <div class="report-section summary-section">
        <h3>Overall Summary</h3>
        <p><strong>Cumulative Average Score:</strong> {{ "%.2f"|format(average_score) }}%</p>
        <p><strong>Total Quizzes Attempted:</strong> {{ attempts|length }}</p>
    </div>

    <div class="report-section attempts-section">
        <h3>Quiz Attempts</h3>
        {% if attempts %}
            <table class="table report-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Quiz Title</th>
                        <th>Score</th>
                        <th>Grade</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attempt in attempts %}
                    <tr>
                        <td>{{ attempt.submitted_at.strftime('%Y-%m-%d') }}</td>
                        <td>{{ attempt.quiz.title }}</td>
                        <td>{{ "%.2f"|format(attempt.score) }}%</td>
                        <td>{{ attempt.grade }}</td>
                        <td>
                            <a href="{{ url_for('view_child_result', attempt_id=attempt.id) }}" class="btn btn-xs btn-info">View</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p>No quiz attempts recorded yet.</p>
        {% endif %}
    </div>

    {% if child.report_comment %}
    <div class="report-section comments-section">
        <h3>Teacher's Comment</h3>
        <div class="comment-box">
            {{ child.report_comment | e | replace('\n', '<br>') | safe }}
        </div>
    </div>
    {% endif %}

</div>

<style>
/* Basic Report Card Styling */
.report-card-container {
    max-width: 850px;
    margin: 2rem auto;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.report-header {
    background-color: #f8f9fa;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    text-align: center;
    position: relative;
}

.report-header h1 {
    color: #007bff;
    margin-bottom: 0.2rem;
}
.report-header h2 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: normal;
}

.header-actions {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    display: flex;
    gap: 0.5rem;
}

.pdf-export-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.pdf-export-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
    text-decoration: none;
}

.report-section {
    padding: 1.5rem 2rem;
    margin-bottom: 1rem;
}

.report-section h3 {
    color: #17a2b8;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.6rem;
    margin-bottom: 1.2rem;
    font-size: 1.3rem;
}

.summary-section p {
    font-size: 1.05rem;
    margin-bottom: 0.5rem;
}
.summary-section p strong {
    color: #343a40;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}
.report-table th, .report-table td {
    border: 1px solid #dee2e6;
    padding: 0.7rem 0.9rem;
    text-align: left;
    vertical-align: middle;
}
.report-table th {
    background-color: #e9ecef;
    font-weight: 600;
}
.report-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.comment-box {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 1rem 1.5rem;
    min-height: 80px;
    line-height: 1.6;
    white-space: pre-wrap;
}

/* Button Styles */
.btn {
    display: inline-block; font-weight: 600; text-align: center; vertical-align: middle;
    cursor: pointer; user-select: none; border: 1px solid transparent; padding: 0.5rem 1rem;
    font-size: 0.9rem; border-radius: 0.25rem; transition: all .15s ease-in-out;
}
.btn-sm {
     padding: 0.375rem 0.75rem; font-size: 0.875rem;
}
.btn-xs {
     padding: 0.25rem 0.5rem; font-size: 0.75rem; line-height: 1.5; border-radius: 0.2rem;
}

.btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
.btn-secondary:hover { background-color: #5a6268; border-color: #545b62; }
.btn-danger { color: #fff; background-color: #dc3545; border-color: #dc3545; }
.btn-danger:hover { background-color: #c82333; border-color: #bd2130; }
.btn-info { color: #fff; background-color: #17a2b8; border-color: #17a2b8; }
.btn-info:hover { background-color: #138496; border-color: #117a8b; }

</style>
{% endblock %} 