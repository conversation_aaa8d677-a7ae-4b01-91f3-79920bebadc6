#!/usr/bin/env python3
"""
Database Migration Script: Teacher Replies
Creates teacher_reply table for teacher responses to student and parent comments.
"""

import sqlite3
import os
from datetime import datetime

def get_database_path():
    """Get the path to the database file."""
    # Check if instance folder exists
    instance_path = os.path.join(os.getcwd(), 'instance')
    if os.path.exists(instance_path):
        db_path = os.path.join(instance_path, 'quiz.db')
    else:
        # Fallback to current directory
        db_path = os.path.join(os.getcwd(), 'quiz.db')
    
    return os.path.abspath(db_path)

def create_teacher_reply_table():
    """Create the teacher_reply table with proper foreign keys and indexes."""
    
    db_path = get_database_path()
    print(f"=== Teacher Replies Database Migration ===")
    print(f"Started at: {datetime.now()}")
    print(f"Database path: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at {db_path}")
        print("Please make sure the Flask application has been run at least once to create the database.")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='teacher_reply'
        """)
        
        if cursor.fetchone():
            print("✅ teacher_reply table already exists")
            conn.close()
            return True
        
        print("Creating teacher_reply table...")
        
        # Create teacher_reply table
        cursor.execute("""
            CREATE TABLE teacher_reply (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                teacher_id INTEGER NOT NULL,
                quiz_id INTEGER NOT NULL,
                parent_comment_id INTEGER,
                student_comment_id INTEGER,
                reply_text TEXT NOT NULL,
                timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (teacher_id) REFERENCES user (id),
                FOREIGN KEY (quiz_id) REFERENCES quiz (id),
                FOREIGN KEY (parent_comment_id) REFERENCES parent_comment (id),
                FOREIGN KEY (student_comment_id) REFERENCES student_comment (id),
                CHECK (
                    (parent_comment_id IS NOT NULL AND student_comment_id IS NULL) OR
                    (parent_comment_id IS NULL AND student_comment_id IS NOT NULL)
                )
            )
        """)
        
        print("✅ teacher_reply table created successfully")
        
        print("Creating indexes...")
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX idx_teacher_reply_teacher_id ON teacher_reply(teacher_id)",
            "CREATE INDEX idx_teacher_reply_quiz_id ON teacher_reply(quiz_id)",
            "CREATE INDEX idx_teacher_reply_parent_comment_id ON teacher_reply(parent_comment_id)",
            "CREATE INDEX idx_teacher_reply_student_comment_id ON teacher_reply(student_comment_id)",
            "CREATE INDEX idx_teacher_reply_timestamp ON teacher_reply(timestamp)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✅ Indexes created successfully")
        
        # Commit changes
        conn.commit()
        
        print("Verifying migration...")
        
        # Verify table structure
        cursor.execute("PRAGMA table_info(teacher_reply)")
        columns = cursor.fetchall()
        
        expected_columns = {
            'id', 'teacher_id', 'quiz_id', 'parent_comment_id', 
            'student_comment_id', 'reply_text', 'timestamp'
        }
        actual_columns = {col[1] for col in columns}
        
        if expected_columns.issubset(actual_columns):
            print("✅ Migration verification successful")
            print(f"✅ Migration completed successfully")
            print(f"Completed at: {datetime.now()}")
            print()
            print("🎉 Teacher replies migration completed successfully!")
            print("Teachers can now reply to student and parent comments.")
            return True
        else:
            print("❌ Migration verification failed")
            print(f"Expected columns: {expected_columns}")
            print(f"Actual columns: {actual_columns}")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main migration function."""
    success = create_teacher_reply_table()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Restart your Flask application")
        print("2. Teachers can now reply to comments in the parent comments section")
        print("3. Students will see teacher replies in their report cards")
        return 0
    else:
        print("\n❌ Migration failed. Please check the errors above and try again.")
        return 1

if __name__ == "__main__":
    exit(main())
