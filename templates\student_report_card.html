{% extends "base.html" %}

{% block title %}My Report Card{% endblock %}

{% block content %}
<div class="container report-card-container">
    <div class="report-header">
        <h1>📊 My Report Card</h1>
        <p class="text-muted">View your quiz performance, add reflections, and see parent feedback.</p>
        <a href="{{ url_for('student_dashboard') }}" class="btn btn-secondary back-btn">Back to Dashboard</a>
    </div>

    <!-- Summary Statistics -->
    <div class="summary-section">
        <h2>📈 Performance Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ summary_stats.total_attempts }}</div>
                <div class="stat-label">Total Attempts</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ summary_stats.total_quizzes }}</div>
                <div class="stat-label">Quizzes Taken</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ summary_stats.average_score }}%</div>
                <div class="stat-label">Average Score</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ summary_stats.highest_score }}%</div>
                <div class="stat-label">Best Score</div>
            </div>
        </div>
        
        {% if summary_stats.total_attempts > 0 %}
        <div class="grade-distribution">
            <h3>Grade Distribution</h3>
            <div class="grade-bars">
                {% for grade, count in summary_stats.grade_distribution.items() %}
                <div class="grade-bar">
                    <span class="grade-label">{{ grade }}</span>
                    <div class="bar-container">
                        <div class="bar grade-{{ grade.lower() }}" style="width: {{ (count / summary_stats.total_attempts * 100) if summary_stats.total_attempts > 0 else 0 }}%"></div>
                    </div>
                    <span class="grade-count">{{ count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <h3>Filter Results</h3>
        <form method="GET" class="filter-form">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="quiz_id">Filter by Quiz:</label>
                    <select id="quiz_id" name="quiz_id" class="form-control">
                        <option value="">All Quizzes</option>
                        {% for quiz in attempted_quizzes %}
                        <option value="{{ quiz.id }}" {% if quiz_filter == quiz.id|string %}selected{% endif %}>
                            {{ quiz.title }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="comments_only" value="true" {% if comments_only %}checked{% endif %}>
                        Only quizzes with comments
                    </label>
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                    <a href="{{ url_for('student_report_card') }}" class="btn btn-outline">Clear Filters</a>
                </div>
            </div>
        </form>
    </div>

    <!-- Quiz Attempts Section -->
    <div class="attempts-section">
        {% if attempts %}
            <h2>📝 Quiz Results ({{ attempts|length }} attempt{{ 's' if attempts|length != 1 else '' }})</h2>
            
            {% for attempt in attempts %}
            <div class="attempt-card">
                <div class="attempt-header">
                    <div class="quiz-info">
                        <h3>{{ attempt.quiz.title }}</h3>
                        <div class="quiz-meta">
                            <span class="difficulty difficulty-{{ attempt.quiz.difficulty }}">{{ attempt.quiz.difficulty.title() }}</span>
                            <span class="date">{{ attempt.submitted_at.strftime('%b %d, %Y at %I:%M %p') }}</span>
                        </div>
                    </div>
                    <div class="score-info">
                        <div class="score">{{ attempt.score }}%</div>
                        <div class="grade grade-{{ attempt.grade.lower() }}">{{ attempt.grade }}</div>
                    </div>
                </div>
                
                <div class="attempt-details">
                    <div class="details-grid">
                        <div class="detail-item">
                            <span class="label">Total Marks:</span>
                            <span class="value">{{ attempt.quiz.total_marks }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Time Limit:</span>
                            <span class="value">{{ attempt.quiz.time_limit }} minutes</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">Description:</span>
                            <span class="value">{{ attempt.quiz.description or 'No description' }}</span>
                        </div>
                    </div>
                </div>

                <!-- Student Comment Section -->
                <div class="comments-section">
                    <h4>💭 My Reflection</h4>
                    
                    <!-- Existing student comments -->
                    {% if attempt.quiz_id in student_comments_by_quiz %}
                        {% for comment in student_comments_by_quiz[attempt.quiz_id] %}
                        <div class="comment student-comment">
                            <div class="comment-header">
                                <span class="comment-author">You</span>
                                <span class="comment-date">{{ comment.timestamp.strftime('%b %d, %Y at %I:%M %p') }}</span>
                            </div>
                            <div class="comment-text">{{ comment.comment_text }}</div>

                            <!-- Teacher replies to student comments -->
                            {% for reply in comment.teacher_replies %}
                            <div class="teacher-reply">
                                <div class="reply-header">
                                    <span class="reply-author">{{ reply.teacher.name }} (Teacher)</span>
                                    <span class="reply-date">{{ reply.timestamp.strftime('%b %d, %Y at %I:%M %p') }}</span>
                                </div>
                                <div class="reply-text">{{ reply.reply_text }}</div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endfor %}
                    {% endif %}
                    
                    <!-- Add new comment form -->
                    <form method="POST" action="{{ url_for('submit_student_comment', quiz_id=attempt.quiz_id) }}" class="comment-form">
                        <div class="form-group">
                            <textarea name="comment_text" placeholder="Add your reflection about this quiz... (e.g., 'I found this quiz challenging but learned a lot about fractions')" 
                                      class="form-control comment-textarea" maxlength="2000" rows="3"></textarea>
                            <div class="char-count">0/2000 characters</div>
                        </div>
                        <button type="submit" class="btn btn-primary btn-sm">Save Reflection</button>
                    </form>
                </div>

                <!-- Parent Comments Section -->
                {% if attempt.quiz_id in parent_comments_by_quiz %}
                <div class="parent-comments-section">
                    <h4>👨‍👩‍👧‍👦 Parent Feedback</h4>
                    {% for comment in parent_comments_by_quiz[attempt.quiz_id] %}
                    <div class="comment parent-comment">
                        <div class="comment-header">
                            <span class="comment-author">{{ comment.parent.name }}</span>
                            <span class="comment-date">{{ comment.timestamp.strftime('%b %d, %Y at %I:%M %p') }}</span>
                        </div>
                        <div class="comment-text">{{ comment.comment_text }}</div>

                        <!-- Teacher replies to parent comments -->
                        {% for reply in comment.teacher_replies %}
                        <div class="teacher-reply">
                            <div class="reply-header">
                                <span class="reply-author">{{ reply.teacher.name }} (Teacher)</span>
                                <span class="reply-date">{{ reply.timestamp.strftime('%b %d, %Y at %I:%M %p') }}</span>
                            </div>
                            <div class="reply-text">{{ reply.reply_text }}</div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="no-attempts">
                <div class="no-attempts-icon">📝</div>
                <h3>No Quiz Results Found</h3>
                {% if quiz_filter or comments_only %}
                <p>No quiz attempts match your current filters. Try adjusting your search criteria.</p>
                <a href="{{ url_for('student_report_card') }}" class="btn btn-primary">View All Results</a>
                {% else %}
                <p>You haven't attempted any quizzes yet. Visit your dashboard to take your first quiz!</p>
                <a href="{{ url_for('student_dashboard') }}" class="btn btn-primary">Go to Dashboard</a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<style>
.report-card-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.report-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.report-header h1 {
    color: #007bff;
    margin-bottom: 0.5rem;
}

.text-muted {
    color: #666;
    margin-bottom: 1rem;
}

.back-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #6c757d;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.back-btn:hover {
    background-color: #545b62;
}

/* Summary Statistics */
.summary-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.summary-section h2 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.grade-distribution h3 {
    margin-bottom: 1rem;
    text-align: center;
}

.grade-bars {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
    margin: 0 auto;
}

.grade-bar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.grade-label {
    width: 20px;
    font-weight: bold;
}

.bar-container {
    flex: 1;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
}

.bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.grade-a { background: #28a745; }
.grade-b { background: #17a2b8; }
.grade-c { background: #ffc107; }
.grade-d { background: #fd7e14; }
.grade-f { background: #dc3545; }

.grade-count {
    width: 30px;
    text-align: right;
    font-weight: bold;
}

/* Filters Section */
.filters-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.filters-section h3 {
    margin-bottom: 1rem;
    color: #333;
}

.filter-form {
    margin: 0;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    margin-top: 1.5rem;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-outline {
    background-color: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background-color: #007bff;
    color: white;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Quiz Attempts */
.attempts-section h2 {
    color: #333;
    margin-bottom: 1.5rem;
}

.attempt-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    overflow: hidden;
}

.attempt-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quiz-info h3 {
    margin: 0 0 0.5rem 0;
    color: #007bff;
    font-size: 1.3rem;
}

.quiz-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.difficulty {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.difficulty-easy { background: #d4edda; color: #155724; }
.difficulty-medium { background: #fff3cd; color: #856404; }
.difficulty-hard { background: #f8d7da; color: #721c24; }

.date {
    color: #666;
    font-size: 0.9rem;
}

.score-info {
    text-align: right;
}

.score {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 0.25rem;
}

.grade {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1.1rem;
}

.attempt-details {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
}

.detail-item .label {
    font-weight: 500;
    color: #666;
}

.detail-item .value {
    color: #333;
}

/* Comments */
.comments-section, .parent-comments-section {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.comments-section:last-child, .parent-comments-section:last-child {
    border-bottom: none;
}

.comments-section h4, .parent-comments-section h4 {
    margin-bottom: 1rem;
    color: #333;
}

.comment {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.student-comment {
    border-left: 4px solid #007bff;
}

.parent-comment {
    border-left: 4px solid #28a745;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.comment-author {
    font-weight: 500;
    color: #333;
}

.comment-date {
    color: #666;
    font-size: 0.85rem;
}

.comment-text {
    color: #333;
    line-height: 1.5;
    white-space: pre-wrap;
}

.teacher-reply {
    margin-top: 1rem;
    margin-left: 1rem;
    padding: 0.75rem;
    background: #e8f4fd;
    border-left: 3px solid #007bff;
    border-radius: 6px;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.reply-author {
    font-weight: 500;
    color: #007bff;
}

.reply-date {
    color: #666;
    font-size: 0.85rem;
}

.reply-text {
    color: #333;
    line-height: 1.5;
    white-space: pre-wrap;
}

.comment-form {
    margin-top: 1rem;
}

.comment-textarea {
    resize: vertical;
    min-height: 80px;
}

.char-count {
    text-align: right;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

/* No attempts state */
.no-attempts {
    text-align: center;
    padding: 3rem 2rem;
    color: #666;
}

.no-attempts-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-attempts h3 {
    color: #333;
    margin-bottom: 1rem;
}

.no-attempts p {
    margin-bottom: 1.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .attempt-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .score-info {
        text-align: left;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .details-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Character count for comment textarea
document.addEventListener('DOMContentLoaded', function() {
    const textareas = document.querySelectorAll('.comment-textarea');
    
    textareas.forEach(textarea => {
        const charCount = textarea.parentNode.querySelector('.char-count');
        
        function updateCharCount() {
            const count = textarea.value.length;
            charCount.textContent = `${count}/2000 characters`;
            
            if (count > 1800) {
                charCount.style.color = '#dc3545';
            } else if (count > 1500) {
                charCount.style.color = '#ffc107';
            } else {
                charCount.style.color = '#666';
            }
        }
        
        textarea.addEventListener('input', updateCharCount);
        updateCharCount(); // Initial count
    });
});
</script>
{% endblock %}
