<!DOCTYPE html>
<html>
<head>
    <title>My Quizzes - Quiz Management System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .quiz-badges {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .creator-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .creator-badge.admin {
            background-color: #dc3545;
            color: white;
            border: 1px solid #c82333;
        }

        .difficulty-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.archived {
            background-color: #6c757d;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .version-badge {
            background-color: #17a2b8;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .quiz-card.archived {
            opacity: 0.8;
            border-left: 4px solid #6c757d;
        }

        .archived-note {
            color: #6c757d;
            font-style: italic;
            font-size: 0.875rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .view-toggle .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
            border: 1px solid #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

        .quiz-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .quiz-actions .btn {
            font-size: 0.85rem;
            padding: 0.4rem 0.8rem;
        }

        @media (max-width: 768px) {
            .header-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .quiz-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .quiz-actions .btn {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>My Quizzes</h1>
            <nav>
                {% if session['user_role'] == 'admin' %}
                    <a href="{{ url_for('admin_dashboard') }}" class="btn">Back to Dashboard</a>
                {% else %}
                    <a href="{{ url_for('teacher_dashboard') }}" class="btn">Back to Dashboard</a>
                {% endif %}
                <a href="/logout" class="btn btn-danger">Logout</a>
            </nav>
        </div>
    </header>
    
    <main class="container">
        <section class="dashboard-section">
            <div class="section-header">
                <h2>My Created Quizzes</h2>
                <div class="header-actions">
                    <div class="view-toggle">
                        <a href="{{ url_for('my_quizzes', view='active') }}"
                           class="btn {{ 'btn-primary' if view_type == 'active' else 'btn-secondary' }}">
                            Active Quizzes
                        </a>
                        <a href="{{ url_for('my_quizzes', view='archived') }}"
                           class="btn {{ 'btn-primary' if view_type == 'archived' else 'btn-secondary' }}">
                            Archived Versions
                        </a>
                    </div>
                    <div class="header-actions">
                        <a href="{{ url_for('create_quiz') }}" class="btn">Create New Quiz</a>
                        <a href="/teacher/export-all-quizzes" class="btn btn-success" title="Export all quizzes as ZIP">
                            📦 Export All
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="quizzes-grid">
                {% for quiz in quizzes %}
                <div class="quiz-card {{ 'archived' if not quiz.is_active else '' }}">
                    <div class="quiz-header">
                        <h3>{{ quiz.get_display_title() }}</h3>
                        <div class="quiz-badges">
                            <span class="difficulty-badge {{ quiz.difficulty }}">{{ quiz.difficulty|title }}</span>
                            {% if quiz.teacher.role == 'admin' %}
                                <span class="creator-badge admin">By Admin</span>
                            {% endif %}
                            {% if not quiz.is_active %}
                                <span class="status-badge archived">Archived</span>
                            {% endif %}
                            {% if quiz.version > 1 %}
                                <span class="version-badge">v{{ quiz.version }}</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="quiz-details">
                        <p class="quiz-description">{{ quiz.description or 'No description provided' }}</p>
                        <div class="quiz-meta">
                            <span>Time: {{ quiz.time_limit }} mins</span>
                            <span>Total Marks: {{ quiz.total_marks }}</span>
                            <span>Questions: {{ quiz.questions|length }}</span>
                        </div>
                        <div class="grade-thresholds">
                            <span>A: ≥{{ quiz.grade_a_threshold }}%</span>
                            <span>B: ≥{{ quiz.grade_b_threshold }}%</span>
                            <span>C: ≥{{ quiz.grade_c_threshold }}%</span>
                            <span>D: ≥{{ quiz.grade_d_threshold }}%</span>
                        </div>
                    </div>
                    
                    <div class="quiz-actions">
                        {% if quiz.is_active %}
                            <a href="/teacher/edit-quiz/{{ quiz.id }}" class="btn btn-secondary">Edit</a>
                            <a href="/teacher/view-quiz/{{ quiz.id }}" class="btn">View</a>
                            <a href="/teacher/export-quiz/{{ quiz.id }}" class="btn btn-success" title="Export as CSV">
                                📊 Export
                            </a>
                            <button onclick="deleteQuiz({{ quiz.id }})" class="btn btn-danger">Delete</button>
                        {% else %}
                            <a href="/teacher/view-quiz/{{ quiz.id }}" class="btn">View (Read-Only)</a>
                            <a href="/teacher/export-quiz/{{ quiz.id }}" class="btn btn-success" title="Export as CSV">
                                📊 Export
                            </a>
                            <span class="archived-note">Archived - Cannot Edit</span>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="no-quizzes">
                    {% if view_type == 'archived' %}
                        <p>No archived quiz versions found.</p>
                        <p>Archived versions are created when you edit a quiz that has already been attempted by students.</p>
                    {% else %}
                        <p>You haven't created any quizzes yet.</p>
                        <a href="{{ url_for('create_quiz') }}" class="btn">Create Your First Quiz</a>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>
    </main>

    <script>
        function deleteQuiz(quizId) {
            if (confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
                fetch(`/teacher/delete-quiz/${quizId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Failed to delete quiz. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the quiz.');
                });
            }
        }
    </script>
</body>
</html> 