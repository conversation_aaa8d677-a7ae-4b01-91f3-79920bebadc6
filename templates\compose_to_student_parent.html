{% extends "base.html" %}

{% block title %}Send Message to Student & Parent{% endblock %}

{% block content %}
<div class="container compose-container">
    <div class="card-header">
        <h1>Send Message to Student & Parent</h1>
        <p class="header-subtitle">This message will be sent to both the selected student and their parent</p>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('compose_to_student_parent') }}">
            <div class="form-group">
                <label for="student_id">Select Student:</label>
                <select id="student_id" name="student_id" class="form-control" required onchange="updateRecipientInfo()">
                    <option value="">-- Choose a student --</option>
                    {% for student in students %}
                    <option value="{{ student.id }}">{{ student.name }} ({{ student.email }})</option>
                    {% endfor %}
                </select>
            </div>
            
            <div id="recipient-info" class="recipient-info" style="display: none;">
                <div class="info-card">
                    <h5>Message Recipients:</h5>
                    <div class="recipient-list">
                        <div class="recipient-item">
                            <span class="recipient-icon">👨‍🎓</span>
                            <div class="recipient-details">
                                <strong>Student:</strong> <span id="student-name">-</span><br>
                                <small id="student-email">-</small>
                            </div>
                        </div>
                        <div class="recipient-item" id="parent-info">
                            <span class="recipient-icon">👨‍👩‍👧‍👦</span>
                            <div class="recipient-details">
                                <strong>Parent:</strong> <span id="parent-name">-</span><br>
                                <small id="parent-email">-</small>
                            </div>
                        </div>
                        <div class="recipient-item" id="no-parent-info" style="display: none;">
                            <span class="recipient-icon">⚠️</span>
                            <div class="recipient-details">
                                <strong>Note:</strong> No parent account found for this student<br>
                                <small>Message will only be sent to the student</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="subject">Subject:</label>
                <input type="text" id="subject" name="subject" value="{{ subject if subject else '' }}" class="form-control" placeholder="Enter message subject">
            </div>
            
            <div class="form-group">
                <label for="body">Message:</label>
                <textarea id="body" name="body" rows="10" required class="form-control" placeholder="Type your message here...">{{ body if body else '' }}</textarea>
            </div>
            
            <div class="form-actions">
                <a href="{{ url_for('inbox') }}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Send to Student & Parent</button>
            </div>
        </form>
    </div>
</div>

<script>
async function updateRecipientInfo() {
    const studentSelect = document.getElementById('student_id');
    const recipientInfo = document.getElementById('recipient-info');
    const studentName = document.getElementById('student-name');
    const studentEmail = document.getElementById('student-email');
    const parentName = document.getElementById('parent-name');
    const parentEmail = document.getElementById('parent-email');
    const parentInfo = document.getElementById('parent-info');
    const noParentInfo = document.getElementById('no-parent-info');
    
    if (!studentSelect.value) {
        recipientInfo.style.display = 'none';
        return;
    }
    
    try {
        const response = await fetch(`/api/student/${studentSelect.value}/parent`);
        const data = await response.json();
        
        if (response.ok) {
            // Update student info
            studentName.textContent = data.student.name;
            studentEmail.textContent = data.student.email;
            
            // Update parent info
            if (data.parent) {
                parentName.textContent = data.parent.name;
                parentEmail.textContent = data.parent.email;
                parentInfo.style.display = 'flex';
                noParentInfo.style.display = 'none';
            } else {
                parentInfo.style.display = 'none';
                noParentInfo.style.display = 'flex';
            }
            
            recipientInfo.style.display = 'block';
        } else {
            console.error('Error fetching student info:', data.error);
            recipientInfo.style.display = 'none';
        }
    } catch (error) {
        console.error('Error fetching student info:', error);
        recipientInfo.style.display = 'none';
    }
}
</script>

<style>
/* Shared styles can go in base.css if preferred */
.compose-container {
    max-width: 800px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 1.5rem;
}

.card-header h1 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.header-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.card-body {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

select.form-control {
    cursor: pointer;
}

textarea.form-control {
    resize: vertical;
    min-height: 150px;
}

.recipient-info {
    margin-bottom: 1.5rem;
}

.info-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
}

.info-card h5 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
}

.recipient-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.recipient-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background-color: white;
    border-radius: 0.25rem;
    border: 1px solid #e9ecef;
}

.recipient-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.recipient-details {
    flex-grow: 1;
}

.recipient-details strong {
    color: #495057;
}

.recipient-details small {
    color: #6c757d;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.8rem;
}

.btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
    border-radius: 0.25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}

@media (max-width: 768px) {
    .compose-container {
        margin: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .recipient-list {
        gap: 0.5rem;
    }
    
    .recipient-item {
        padding: 0.75rem 0.5rem;
    }
}
</style>
{% endblock %}
