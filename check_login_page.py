import requests

response = requests.get('http://127.0.0.1:5000/login')
content = response.text

if '<header class="main-header">' in content:
    print('Navigation header HTML element found on login page')
else:
    print('Navigation header HTML element NOT found on login page')

# Check for navigation elements
if 'Logout' in content:
    print('Logout button found on login page')
else:
    print('Logout button NOT found on login page')

# Check for home link
if 'href="/home"' in content or 'url_for(\'home\')' in content:
    print('Home link found on login page')
else:
    print('Home link NOT found on login page')
