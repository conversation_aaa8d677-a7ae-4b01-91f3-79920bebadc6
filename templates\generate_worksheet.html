{% extends "base.html" %}

{% block title %}Generate Worksheet{% endblock %}

{% block content %}
<div class="container worksheet-generator-container">
    <div class="worksheet-header">
        <h1>📋 Generate Worksheet</h1>
        <p>Create customized math worksheets for your students</p>
    </div>

    <div class="row">
        <!-- Main Form -->
        <div class="col-lg-8">
            <div class="form-section">
                <h2>Worksheet Configuration</h2>
                
                <form method="POST" id="worksheet-form">
                    <!-- Worksheet Title -->
                    <div class="form-group">
                        <label for="worksheet_title">Worksheet Title:</label>
                        <input type="text" id="worksheet_title" name="worksheet_title" 
                               class="form-control" value="Math Worksheet" required>
                    </div>

                    <!-- Topic Selection -->
                    <div class="form-group">
                        <label for="topics">Select Topics:</label>
                        <div class="topic-selection">
                            {% for topic in topics %}
                            <div class="topic-checkbox">
                                <input type="checkbox" id="topic_{{ loop.index }}" 
                                       name="topics" value="{{ topic.name }}" class="topic-input">
                                <label for="topic_{{ loop.index }}" class="topic-label">
                                    <span class="topic-name">{{ topic.name }}</span>
                                    <span class="topic-description">{{ topic.description }}</span>
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        <small class="form-text text-muted">Select one or more topics to include in your worksheet</small>
                    </div>

                    <!-- Difficulty Level -->
                    <div class="form-group">
                        <label for="difficulty_level">Difficulty Level:</label>
                        <select id="difficulty_level" name="difficulty_level" class="form-control" required>
                            <option value="1">Easy (Level 1)</option>
                            <option value="2" selected>Medium (Level 2)</option>
                            <option value="3">Hard (Level 3)</option>
                        </select>
                    </div>

                    <!-- Number of Questions -->
                    <div class="form-group">
                        <label for="question_count">Number of Questions:</label>
                        <input type="number" id="question_count" name="question_count" 
                               class="form-control" min="1" max="30" value="10" required>
                        <small class="form-text text-muted">Maximum 30 questions per worksheet</small>
                    </div>

                    <!-- Options -->
                    <div class="form-group">
                        <div class="options-section">
                            <h4>Options</h4>
                            
                            <div class="form-check">
                                <input type="checkbox" id="include_answers" name="include_answers" class="form-check-input">
                                <label for="include_answers" class="form-check-label">
                                    Include answer key at the end
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" id="save_template" name="save_template" class="form-check-input">
                                <label for="save_template" class="form-check-label">
                                    Save as template for future use
                                </label>
                            </div>
                            
                            <div class="template-name-group" id="template-name-group" style="display: none;">
                                <label for="template_name">Template Name:</label>
                                <input type="text" id="template_name" name="template_name" 
                                       class="form-control" placeholder="Enter template name">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="form-group">
                        <div class="submit-buttons">
                            <button type="submit" name="action" value="preview" class="btn btn-primary">
                                👁️ Preview Worksheet
                            </button>
                            <button type="submit" name="export_pdf" value="1" class="btn btn-success">
                                📄 Generate PDF
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Saved Templates -->
            {% if saved_templates %}
            <div class="sidebar-section">
                <h3>📁 Saved Templates</h3>
                <div class="saved-templates">
                    {% for template in saved_templates %}
                    <div class="template-card">
                        <div class="template-header">
                            <h5>{{ template.name }}</h5>
                            <div class="template-actions">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="loadTemplate({{ template.id }})">Load</button>
                                <form method="POST" action="{{ url_for('delete_worksheet_template', template_id=template.id) }}" 
                                      style="display: inline;" onsubmit="return confirm('Delete this template?')">
                                    <button type="submit" class="btn btn-sm btn-outline-danger">Delete</button>
                                </form>
                            </div>
                        </div>
                        <div class="template-details">
                            <small>
                                Difficulty: Level {{ template.difficulty_level }} | 
                                Questions: {{ template.question_count }}
                                {% if template.include_answers %} | With Answers{% endif %}
                            </small>
                            <br>
                            <small class="text-muted">Created: {{ template.created_at.strftime('%Y-%m-%d') }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Quick Tips -->
            <div class="sidebar-section">
                <h3>💡 Quick Tips</h3>
                <div class="tips-list">
                    <div class="tip-item">
                        <strong>Topic Selection:</strong> Choose multiple topics for variety
                    </div>
                    <div class="tip-item">
                        <strong>Difficulty Levels:</strong>
                        <ul>
                            <li>Easy: Basic concepts and simple problems</li>
                            <li>Medium: Intermediate problem-solving</li>
                            <li>Hard: Advanced and challenging questions</li>
                        </ul>
                    </div>
                    <div class="tip-item">
                        <strong>Answer Keys:</strong> Include answers for easy grading
                    </div>
                    <div class="tip-item">
                        <strong>Templates:</strong> Save frequently used configurations
                    </div>
                </div>
            </div>

            <!-- Recent Worksheets -->
            <div class="sidebar-section">
                <h3>📊 Quick Actions</h3>
                <div class="quick-actions">
                    <a href="{{ url_for('my_worksheets') }}" class="btn btn-outline-info btn-block">
                        📋 View My Worksheets
                    </a>
                    <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-outline-secondary btn-block">
                        🏠 Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Show/hide template name input
document.getElementById('save_template').addEventListener('change', function() {
    const templateNameGroup = document.getElementById('template-name-group');
    if (this.checked) {
        templateNameGroup.style.display = 'block';
        document.getElementById('template_name').required = true;
    } else {
        templateNameGroup.style.display = 'none';
        document.getElementById('template_name').required = false;
    }
});

// Load template function
async function loadTemplate(templateId) {
    try {
        const response = await fetch(`/load_template/${templateId}`);
        const data = await response.json();
        
        if (data.error) {
            alert('Error loading template: ' + data.error);
            return;
        }
        
        // Fill form with template data
        document.getElementById('worksheet_title').value = data.name;
        document.getElementById('difficulty_level').value = data.difficulty_level;
        document.getElementById('question_count').value = data.question_count;
        document.getElementById('include_answers').checked = data.include_answers;
        
        // Clear and set topics
        const topicInputs = document.querySelectorAll('input[name="topics"]');
        topicInputs.forEach(input => input.checked = false);
        
        if (data.topics) {
            data.topics.forEach(topic => {
                const topicInput = document.querySelector(`input[value="${topic}"]`);
                if (topicInput) {
                    topicInput.checked = true;
                }
            });
        }
        
        alert('Template loaded successfully!');
        
    } catch (error) {
        alert('Error loading template: ' + error.message);
    }
}

// Form validation
document.getElementById('worksheet-form').addEventListener('submit', function(e) {
    const selectedTopics = document.querySelectorAll('input[name="topics"]:checked');
    
    if (selectedTopics.length === 0) {
        e.preventDefault();
        alert('Please select at least one topic.');
        return false;
    }
    
    const questionCount = parseInt(document.getElementById('question_count').value);
    if (questionCount < 1 || questionCount > 30) {
        e.preventDefault();
        alert('Please enter a valid number of questions (1-30).');
        return false;
    }
    
    return true;
});
</script>

<style>
.worksheet-generator-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.worksheet-header {
    text-align: center;
    margin-bottom: 2rem;
}

.worksheet-header h1 {
    color: #007bff;
    margin-bottom: 0.5rem;
}

.form-section {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-section h2 {
    color: #333;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.topic-selection {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.topic-checkbox {
    margin-bottom: 0.75rem;
}

.topic-checkbox:last-child {
    margin-bottom: 0;
}

.topic-input {
    margin-right: 0.75rem;
}

.topic-label {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.topic-label:hover {
    background-color: #f8f9fa;
}

.topic-name {
    font-weight: 500;
    color: #333;
}

.topic-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.options-section {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
}

.options-section h4 {
    margin-bottom: 1rem;
    color: #495057;
}

.template-name-group {
    margin-top: 1rem;
}

.submit-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.sidebar-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.sidebar-section h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.template-card {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.template-card:last-child {
    margin-bottom: 0;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.template-header h5 {
    margin: 0;
    color: #333;
    font-size: 1rem;
}

.template-actions {
    display: flex;
    gap: 0.5rem;
}

.template-details {
    font-size: 0.875rem;
}

.tips-list .tip-item {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.tips-list .tip-item:last-child {
    margin-bottom: 0;
}

.quick-actions .btn {
    margin-bottom: 0.5rem;
}

.quick-actions .btn:last-child {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .submit-buttons {
        flex-direction: column;
    }
    
    .template-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .template-actions {
        margin-top: 0.5rem;
    }
}
</style>
{% endblock %}
