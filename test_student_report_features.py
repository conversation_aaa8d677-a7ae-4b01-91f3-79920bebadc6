#!/usr/bin/env python3
"""
Comprehensive Test Script: Student Report Card Features
Tests student report card, comment submission, parent comment display, and teacher replies.
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TEST_DELAY = 1  # Delay between requests in seconds

class TestSession:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Student Report Test Script'
        })

def print_test_header(test_name):
    """Print a formatted test header."""
    print(f"\n{'='*60}")
    print(f"🧪 {test_name}")
    print(f"{'='*60}")

def print_step(step_name):
    """Print a test step."""
    print(f"\n📋 {step_name}")

def print_result(success, message):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"   {status}: {message}")

def login_user(session, email, password, expected_role):
    """Login a user and verify the role."""
    print_step(f"Logging in as {expected_role}: {email}")
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print_result(False, f"Failed to access login page: {response.status_code}")
        return False
    
    # Submit login
    login_data = {
        'email': email,
        'password': password
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
    
    if response.status_code == 302:  # Redirect indicates successful login
        print_result(True, f"Login successful for {email}")
        
        # Follow redirect to verify dashboard access
        dashboard_response = session.get(response.headers.get('Location', f"{BASE_URL}/"))
        if dashboard_response.status_code == 200:
            print_result(True, f"Dashboard access confirmed for {expected_role}")
            return True
        else:
            print_result(False, f"Dashboard access failed: {dashboard_response.status_code}")
            return False
    else:
        print_result(False, f"Login failed: {response.status_code}")
        return False

def test_student_report_card_access(session):
    """Test student report card page access."""
    print_step("Testing student report card access")
    
    response = session.get(f"{BASE_URL}/student/report-card")
    
    if response.status_code == 200:
        content = response.text
        
        # Check for key elements
        checks = [
            ("📊 My Report Card" in content, "Report card title present"),
            ("Performance Summary" in content, "Performance summary section present"),
            ("Quiz Results" in content or "No Quiz Results Found" in content, "Quiz results section present"),
            ("My Reflection" in content or "No Quiz Results Found" in content, "Student comment section present"),
            ("Parent Feedback" in content or "No Quiz Results Found" in content, "Parent comment section present")
        ]
        
        all_passed = True
        for check, description in checks:
            print_result(check, description)
            if not check:
                all_passed = False
        
        return all_passed
    else:
        print_result(False, f"Failed to access report card: {response.status_code}")
        return False

def test_student_comment_submission(session):
    """Test student comment submission."""
    print_step("Testing student comment submission")
    
    # First, check if there are any quizzes to comment on
    report_response = session.get(f"{BASE_URL}/student/report-card")
    if report_response.status_code != 200:
        print_result(False, "Cannot access report card to find quizzes")
        return False
    
    # Look for quiz IDs in the response (this is a simplified check)
    content = report_response.text
    
    # Try to submit a comment (we'll use quiz_id=1 as a test)
    comment_data = {
        'comment_text': 'This is a test reflection comment from the automated test script. I found this quiz helpful for learning.'
    }
    
    response = session.post(f"{BASE_URL}/student/comment/1", data=comment_data, allow_redirects=False)
    
    if response.status_code == 302:  # Redirect indicates successful submission
        print_result(True, "Comment submission successful (redirect received)")
        
        # Check if we're redirected back to report card
        redirect_url = response.headers.get('Location', '')
        if 'report-card' in redirect_url:
            print_result(True, "Redirected back to report card")
            return True
        else:
            print_result(False, f"Unexpected redirect: {redirect_url}")
            return False
    else:
        print_result(False, f"Comment submission failed: {response.status_code}")
        return False

def test_report_card_filtering(session):
    """Test report card filtering functionality."""
    print_step("Testing report card filtering")
    
    # Test filter by quiz
    response = session.get(f"{BASE_URL}/student/report-card?quiz_id=1")
    if response.status_code == 200:
        print_result(True, "Quiz filter parameter accepted")
    else:
        print_result(False, f"Quiz filter failed: {response.status_code}")
        return False
    
    # Test comments only filter
    response = session.get(f"{BASE_URL}/student/report-card?comments_only=true")
    if response.status_code == 200:
        print_result(True, "Comments only filter parameter accepted")
    else:
        print_result(False, f"Comments only filter failed: {response.status_code}")
        return False
    
    # Test combined filters
    response = session.get(f"{BASE_URL}/student/report-card?quiz_id=1&comments_only=true")
    if response.status_code == 200:
        print_result(True, "Combined filters accepted")
        return True
    else:
        print_result(False, f"Combined filters failed: {response.status_code}")
        return False

def test_dashboard_report_button(session):
    """Test report card button on student dashboard."""
    print_step("Testing report card button on dashboard")
    
    response = session.get(f"{BASE_URL}/student")
    
    if response.status_code == 200:
        content = response.text
        
        # Check for report card button
        checks = [
            ("View My Report Card" in content, "Report card button text present"),
            ("student/report-card" in content, "Report card link present"),
            ("📊" in content, "Report card icon present")
        ]
        
        all_passed = True
        for check, description in checks:
            print_result(check, description)
            if not check:
                all_passed = False
        
        return all_passed
    else:
        print_result(False, f"Failed to access student dashboard: {response.status_code}")
        return False

def test_teacher_reply_access(session):
    """Test teacher reply functionality access."""
    print_step("Testing teacher reply functionality")
    
    # Access parent comments page
    response = session.get(f"{BASE_URL}/teacher/parent-comments")
    
    if response.status_code == 200:
        content = response.text
        
        # Check for reply functionality
        checks = [
            ("Quick Reply" in content, "Quick reply button present"),
            ("submit_teacher_reply" in content, "Reply submission form present"),
            ("reply-form" in content, "Reply form elements present")
        ]
        
        all_passed = True
        for check, description in checks:
            print_result(check, description)
            if not check:
                all_passed = False
        
        return all_passed
    else:
        print_result(False, f"Failed to access parent comments: {response.status_code}")
        return False

def test_security_access_control(session):
    """Test security and access control."""
    print_step("Testing security and access control")
    
    # Test unauthorized access to report card (should redirect to login)
    session.get(f"{BASE_URL}/logout")  # Logout first
    
    response = session.get(f"{BASE_URL}/student/report-card", allow_redirects=False)
    
    if response.status_code == 302:
        redirect_url = response.headers.get('Location', '')
        if 'login' in redirect_url:
            print_result(True, "Unauthorized access properly redirected to login")
            return True
        else:
            print_result(False, f"Unexpected redirect for unauthorized access: {redirect_url}")
            return False
    else:
        print_result(False, f"Unauthorized access not properly handled: {response.status_code}")
        return False

def run_all_tests():
    """Run all tests."""
    print("🚀 Starting Student Report Card Feature Tests")
    print(f"Target URL: {BASE_URL}")
    print(f"Test started at: {datetime.now()}")
    
    # Test credentials (adjust these based on your test data)
    test_users = {
        'student': {'email': '<EMAIL>', 'password': 'student123'},
        'teacher': {'email': '<EMAIL>', 'password': 'teacher123'},
        'parent': {'email': '<EMAIL>', 'password': 'parent123'}
    }
    
    all_tests_passed = True
    
    # Test 1: Security (unauthorized access)
    print_test_header("Security and Access Control Test")
    session = TestSession()
    if not test_security_access_control(session.session):
        all_tests_passed = False
    time.sleep(TEST_DELAY)
    
    # Test 2: Student functionality
    print_test_header("Student Report Card Tests")
    session = TestSession()
    
    if login_user(session.session, test_users['student']['email'], 
                  test_users['student']['password'], 'student'):
        
        tests = [
            test_dashboard_report_button,
            test_student_report_card_access,
            test_report_card_filtering,
            test_student_comment_submission
        ]
        
        for test_func in tests:
            if not test_func(session.session):
                all_tests_passed = False
            time.sleep(TEST_DELAY)
    else:
        all_tests_passed = False
    
    # Test 3: Teacher functionality
    print_test_header("Teacher Reply Functionality Tests")
    session = TestSession()
    
    if login_user(session.session, test_users['teacher']['email'], 
                  test_users['teacher']['password'], 'teacher'):
        
        if not test_teacher_reply_access(session.session):
            all_tests_passed = False
        time.sleep(TEST_DELAY)
    else:
        all_tests_passed = False
    
    # Final results
    print_test_header("Test Results Summary")
    
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Student report card functionality is working correctly")
        print("✅ Comment system is functional")
        print("✅ Teacher reply system is accessible")
        print("✅ Security controls are in place")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please review the failed tests above and check:")
        print("- Database migrations have been run")
        print("- Flask application is running")
        print("- Test user accounts exist")
        print("- Routes are properly configured")
    
    print(f"\nTest completed at: {datetime.now()}")
    return all_tests_passed

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
