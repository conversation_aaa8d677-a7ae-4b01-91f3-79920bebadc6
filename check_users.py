import sqlite3

conn = sqlite3.connect('instance/quiz.db')
cursor = conn.cursor()

print("Available verified users:")
cursor.execute('SELECT id, name, email, role FROM user WHERE role IN ("teacher", "parent") AND is_verified = 1')
users = cursor.fetchall()
for user in users:
    print(f'  ID: {user[0]}, Name: {user[1]}, Email: {user[2]}, Role: {user[3]}')

print("\nUser credentials for testing:")
cursor.execute('SELECT name, email, role, unhashed_password FROM user WHERE role IN ("teacher", "parent") AND is_verified = 1')
cred_users = cursor.fetchall()
for user in cred_users:
    print(f'  Name: {user[0]}, Email: {user[1]}, Role: {user[2]}, Password: {user[3]}')

print("\nAll users:")
cursor.execute('SELECT id, name, email, role, is_verified FROM user')
all_users = cursor.fetchall()
for user in all_users:
    print(f'  ID: {user[0]}, Name: {user[1]}, Email: {user[2]}, Role: {user[3]}, Verified: {user[4]}')

conn.close()
