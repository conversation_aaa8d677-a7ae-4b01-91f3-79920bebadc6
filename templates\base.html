<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Quiz Management System{% endblock %}</title>

    <!-- MathJax Configuration and CDN -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['\\(', '\\)'], ['$', '$']],
                displayMath: [['\\[', '\\]'], ['$$', '$$']],
                processEscapes: true,
                processEnvironments: true,
                packages: {'[+]': ['ams', 'newcommand', 'configmacros']}
            },
            options: {
                ignoreHtmlClass: 'tex2jax_ignore',
                processHtmlClass: 'tex2jax_process'
            },
            startup: {
                ready: function () {
                    MathJax.startup.defaultReady();
                    // Custom initialization if needed
                }
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

    <!-- JSXGraph for interactive graphing -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/jsxgraph@1.7.0/distrib/jsxgraph.css" />
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/jsxgraph@1.7.0/distrib/jsxgraphcore.js"></script>
    <style>
        /* Flash messages styling */
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .flash-message {
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            animation: slideIn 0.5s ease-out;
        }

        .flash-message.success {
            background-color: #28a745;
        }

        .flash-message.error {
            background-color: #dc3545;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Existing styles */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Add any other existing styles here */
        .main-header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            font-family: 'Poppins', sans-serif; /* Assuming Poppins is used */
        }
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.8em;
            font-weight: 600;
            color: #333;
            text-decoration: none;
        }
        nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            gap: 1.5rem; /* Spacing between nav items */
        }
        nav a {
            text-decoration: none;
            color: #555;
            font-weight: 500;
            transition: color 0.2s ease;
        }
        nav a:hover {
            color: dodgerblue;
        }
        /* Add styles for Poppins font if not already globally applied */
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Poppins', sans-serif; }

        /* Animated Button Styles (Blue Theme) - Copied from home.html */
        .animated-button {
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: center; /* Center content within the button */
          gap: 4px;
          padding: 16px 36px;
          border: 4px solid;
          border-color: transparent;
          font-size: 16px;
          background-color: inherit;
          border-radius: 100px;
          font-weight: 600;
          color: dodgerblue;
          box-shadow: 0 0 0 2px dodgerblue;
          cursor: pointer;
          overflow: hidden;
          transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
          text-decoration: none;
        }

        .animated-button svg {
          position: absolute;
          width: 24px;
          fill: dodgerblue;
          z-index: 9;
          transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
        }

        .animated-button .arr-1 {
          right: 16px;
        }

        .animated-button .arr-2 {
          left: -25%;
        }

        .animated-button .circle {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 20px;
          height: 20px;
          background-color: dodgerblue;
          border-radius: 50%;
          opacity: 0;
          transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
        }

        .animated-button .text {
          position: relative;
          z-index: 1;
          /* Removed translateX for better centering by default */
          transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
        }

        .animated-button:hover {
          box-shadow: 0 0 0 12px transparent;
          color: #fff;
          border-radius: 12px;
        }

        .animated-button:hover .arr-1 {
          right: -25%;
        }

        .animated-button:hover .arr-2 {
          left: 16px;
        }

        /* Removed translateX on hover for text */
        /* .animated-button:hover .text {
          transform: translateX(12px);
        } */

        .animated-button:hover svg {
          fill: #fff;
        }

        .animated-button:active {
          scale: 0.95;
          box-shadow: 0 0 0 4px dodgerblue;
        }

        .animated-button:hover .circle {
          /* Adjust size to ensure full coverage */
          width: 100%;
          height: 150%;
          opacity: 1;
        }
        /* End Animated Button Styles */

        /* Morphing Checkbox Styles - Start */
        .morphing-checkbox {
          --checkbox-size: 1.8em; /* Adjusted size slightly */
          --checkbox-color: #6366f1;
          --checkbox-color-hover: #4f46e5;
          --checkbox-shadow: rgba(99, 102, 241, 0.3);
          --checkmark-color: white;
          --cubic-bezier: cubic-bezier(0.16, 1, 0.3, 1);

          position: relative;
          display: inline-block;
          cursor: pointer;
          user-select: none;
          vertical-align: middle; /* Align nicely with text */
        }

        .morphing-checkbox input {
          position: absolute;
          opacity: 0;
          width: 0;
          height: 0;
        }

        .checkbox-body {
          position: relative;
          width: var(--checkbox-size);
          height: var(--checkbox-size);
          perspective: 25em;
        }

        .cube {
          position: absolute;
          width: 100%;
          height: 100%;
          transform-style: preserve-3d;
          transition: transform 0.6s var(--cubic-bezier);
        }

        .cube div {
          position: absolute;
          width: 100%;
          height: 100%;
          background-color: var(--checkbox-color);
          backface-visibility: hidden;
          border-radius: 0.3em;
          box-shadow: 0 0.2em 0.6em var(--checkbox-shadow);
          transform-style: preserve-3d;
        }

        .cube .front {
          transform: translateZ(calc(var(--checkbox-size) / 2));
        }
        .cube .back {
          transform: rotateY(180deg) translateZ(calc(var(--checkbox-size) / 2));
        }
        .cube .top {
          transform: rotateX(90deg) translateZ(calc(var(--checkbox-size) / 2));
        }
        .cube .bottom {
          transform: rotateX(-90deg) translateZ(calc(var(--checkbox-size) / 2));
        }
        .cube .left {
          transform: rotateY(-90deg) translateZ(calc(var(--checkbox-size) / 2));
        }
        .cube .right {
          transform: rotateY(90deg) translateZ(calc(var(--checkbox-size) / 2));
        }

        .morphing-checkbox:hover .cube div {
          background-color: var(--checkbox-color-hover);
        }

        .morphing-checkbox input:checked + .checkbox-body .cube {
          transform: rotateY(180deg) rotateZ(180deg);
        }

        .checkmark {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0);
          width: 60%;
          height: 60%;
          fill: none;
          stroke: var(--checkmark-color);
          stroke-width: 3;
          stroke-linecap: round;
          stroke-linejoin: round;
          stroke-dasharray: 24;
          stroke-dashoffset: 24;
          z-index: 2;
          pointer-events: none;
        }

        .morphing-checkbox input:checked + .checkbox-body .checkmark {
          animation: check-animation 0.5s var(--cubic-bezier) forwards 0.3s;
        }

        @keyframes check-animation {
          0% {
            transform: translate(-50%, -50%) scale(0);
            stroke-dashoffset: 24;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.2);
          }
          100% {
            transform: translate(-50%, -50%) scale(1);
            stroke-dashoffset: 0;
          }
        }

        .ripple {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          transform: translate(-50%, -50%);
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.5);
          opacity: 0;
          z-index: 1;
          pointer-events: none;
        }

        .morphing-checkbox input:checked + .checkbox-body .ripple {
          animation: ripple 0.6s var(--cubic-bezier);
        }

        @keyframes ripple {
          0% {
            width: 0;
            height: 0;
            opacity: 0.5;
          }
          100% {
            width: 200%;
            height: 200%;
            opacity: 0;
          }
        }

        .morphing-checkbox input:focus + .checkbox-body::after {
          content: "";
          position: absolute;
          top: -0.3em;
          left: -0.3em;
          right: -0.3em;
          bottom: -0.3em;
          border-radius: 0.6em;
          border: 0.15em solid rgba(99, 102, 241, 0.4);
          pointer-events: none;
          opacity: 0;
          animation: focus-animation 0.3s var(--cubic-bezier) forwards;
        }

        @keyframes focus-animation {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .morphing-checkbox:active .cube {
          transform: scale(0.9);
        }

        .morphing-checkbox input:checked:active + .checkbox-body .cube {
          transform: scale(0.9) rotateY(180deg) rotateZ(180deg);
        }
        /* Morphing Checkbox Styles - End */

        /* Fancy Button Styles - Start */
        .fancy {
         background-color: transparent;
         border: 2px solid #000;
         border-radius: 0;
         box-sizing: border-box;
         color: #fff;
         cursor: pointer;
         display: inline-block;
         /* Removed float: right; */
         font-weight: 700;
         letter-spacing: 0.05em;
         margin: 0;
         outline: none;
         overflow: visible;
         padding: 1.25em 2em;
         position: relative;
         text-align: center;
         text-decoration: none;
         text-transform: none;
         transition: all 0.3s ease-in-out;
         user-select: none;
         font-size: 13px;
         vertical-align: middle; /* Align with other inline elements */
        }

        .fancy::before {
         content: " ";
         width: 1.5625rem;
         height: 2px;
         background: black;
         top: 50%;
         left: 1.5em;
         position: absolute;
         transform: translateY(-50%);
         transform-origin: center;
         transition: background 0.3s linear, width 0.3s linear;
        }

        .fancy .text {
         font-size: 1.125em;
         line-height: 1.33333em;
         padding-left: 2em;
         display: block;
         text-align: left;
         transition: all 0.3s ease-in-out;
         text-transform: uppercase;
         text-decoration: none;
         color: black;
        }

        .fancy .top-key {
         height: 2px;
         width: 1.5625rem;
         top: -2px;
         left: 0.625rem;
         position: absolute;
         background: #e8e8e8;
         transition: width 0.5s ease-out, left 0.3s ease-out;
        }

        .fancy .bottom-key-1 {
         height: 2px;
         width: 1.5625rem;
         right: 1.875rem;
         bottom: -2px;
         position: absolute;
         background: #e8e8e8;
         transition: width 0.5s ease-out, right 0.3s ease-out;
        }

        .fancy .bottom-key-2 {
         height: 2px;
         width: 0.625rem;
         right: 0.625rem;
         bottom: -2px;
         position: absolute;
         background: #e8e8e8;
         transition: width 0.5s ease-out, right 0.3s ease-out;
        }

        .fancy:hover {
         color: white;
         background: black;
        }

        .fancy:hover::before {
         width: 0.9375rem;
         background: white;
        }

        .fancy:hover .text {
         color: white;
         padding-left: 1.5em;
        }

        .fancy:hover .top-key {
         left: -2px;
         width: 0px;
        }

        .fancy:hover .bottom-key-1,
         .fancy:hover .bottom-key-2 {
         right: 0;
         width: 0;
        }
        /* Fancy Button Styles - End */

        /* Auth Button Styles - Start */
        button.auth-button {
          padding: 17px 40px;
          border-radius: 50px;
          cursor: pointer;
          border: 0;
          background-color: white;
          box-shadow: rgb(0 0 0 / 5%) 0 0 8px;
          letter-spacing: 1.5px;
          text-transform: uppercase;
          font-size: 15px;
          transition: all 0.5s ease;
          color: #333; /* Set default text color */
          font-weight: bold; /* Make text bold */
        }

        button.auth-button:hover {
          letter-spacing: 3px;
          background-color: hsl(261deg 80% 48%);
          color: hsl(0, 0%, 100%);
          box-shadow: rgb(93 24 220) 0px 7px 29px 0px;
        }

        button.auth-button:active {
          letter-spacing: 3px;
          background-color: hsl(261deg 80% 48%);
          color: hsl(0, 0%, 100%);
          box-shadow: rgb(93 24 220) 0px 0px 0px 0px;
          transform: translateY(10px);
          transition: 100ms;
        }
        /* Auth Button Styles - End */
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
    {% if request.endpoint not in ['login', 'signup'] %}
    <header class="main-header">
        <div class="container header-container">
            <a href="{{ url_for('home') }}" class="logo">Quiz Platform</a>
            <nav>
                <ul>
                    <li><a href="{{ url_for('home') }}">Home</a></li>
                    {% if session.user_id %}
                        <li><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                        <li><a href="{{ url_for('inbox') }}">Inbox</a></li>
                        {% if session.user_role == 'admin' %}
                            <li><a href="{{ url_for('admin_dashboard') }}">Admin Panel</a></li>
                        {% endif %}
                        <li><a href="{{ url_for('logout') }}">Logout ({{ session.user_name }})</a></li>
                    {% else %}
                        <li><a href="{{ url_for('login') }}">Login</a></li>
                        <li><a href="{{ url_for('signup') }}">Sign Up</a></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </header>
    {% endif %}

    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    {% block content %}{% endblock %}

    <script>
        // Auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(message => {
                setTimeout(() => {
                    message.style.opacity = '0';
                    setTimeout(() => message.remove(), 500);
                }, 5000);
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>