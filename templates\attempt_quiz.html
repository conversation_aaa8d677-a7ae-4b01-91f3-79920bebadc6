{% extends "base.html" %}

{% block title %}{{ quiz.title }} - Attempt Quiz{% endblock %}

{% block content %}
<div class="container quiz-attempt-container">
    <div class="quiz-header">
        <h1>{{ quiz.title }}</h1>
        <div class="quiz-tools">
            {% if quiz.allow_calculator %}
            <button type="button" id="calculator-btn" class="tool-btn calculator-btn" title="Open Calculator">
                🧮 Calculator
            </button>
            {% endif %}
            <button type="button" id="graph-tool-btn" class="tool-btn graph-tool-btn" title="Open Graph Tool" onclick="openGraphTool()">
                📊 Graph Tool
            </button>
            <div id="timer">Time Left: <span>{{ quiz.time_limit }}:00</span></div>
        </div>
    </div>
    {% if quiz.description %}
        <p class="quiz-description">{{ quiz.description }}</p>
    {% endif %}

    <form id="quiz-form" method="post" action="{{ url_for('submit_quiz', quiz_id=quiz.id) }}">
        {% for question in questions %}
        <div class="question-card" id="question-{{ question.id }}">
            <div class="question-header">
                 <span class="question-number">Question {{ loop.index }}</span>
                 <span class="question-marks">({{ question.marks }} Marks)</span>
            </div>
           
            <p class="question-text tex2jax_process">{{ question.question_text | process_math | safe }}</p>

            <!-- Question Image -->
            {% if question.image_filename %}
            <div class="question-image-container">
                <img src="{{ url_for('static', filename='uploads/' + question.image_filename) }}"
                     alt="Question Image"
                     class="question-image"
                     onclick="openImageModal(this.src)">
            </div>
            {% endif %}

            <div class="options">
                {% if question.question_type == 'mcq' %}
                    {% set options = [question.option1, question.option2, question.option3, question.option4] %}
                    {% for i in range(options|length) %}
                        {% if options[i] %}
                            <div class="option-item">
                                <input type="radio" id="q{{ question.id }}_opt{{ i+1 }}" name="question_{{ question.id }}" value="{{ i+1 }}">
                                <label for="q{{ question.id }}_opt{{ i+1 }}" class="option-label">
                                    <span class="option-text tex2jax_process">{{ options[i] | process_math | safe }}</span>
                                    <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                                </label>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% elif question.question_type == 'true_false' %}
                     <div class="option-item">
                        <input type="radio" id="q{{ question.id }}_true" name="question_{{ question.id }}" value="True">
                         <label for="q{{ question.id }}_true" class="option-label">
                            <span class="option-text tex2jax_process">True</span>
                             <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                        </label>
                    </div>
                     <div class="option-item">
                        <input type="radio" id="q{{ question.id }}_false" name="question_{{ question.id }}" value="False">
                         <label for="q{{ question.id }}_false" class="option-label">
                            <span class="option-text tex2jax_process">False</span>
                             <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                        </label>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <button type="submit" class="btn submit-btn">Submit Quiz</button>
    </form>
</div>

<!-- Calculator Modal -->
{% if quiz.allow_calculator %}
<div id="calculator-modal" class="calculator-modal">
    <div class="calculator-container">
        <div class="calculator-header">
            <h3>Calculator</h3>
            <button type="button" id="calculator-close" class="close-btn">&times;</button>
        </div>
        <div class="calculator-body">
            <div class="calculator-display">
                <input type="text" id="calculator-screen" readonly value="0" maxlength="15">
            </div>
            <div class="calculator-buttons">
                <button type="button" class="calc-btn calc-clear" data-action="clear">C</button>
                <button type="button" class="calc-btn calc-clear" data-action="clear-entry">CE</button>
                <button type="button" class="calc-btn calc-backspace" data-action="backspace">⌫</button>
                <button type="button" class="calc-btn calc-operator" data-action="divide">÷</button>

                <button type="button" class="calc-btn calc-number" data-number="7">7</button>
                <button type="button" class="calc-btn calc-number" data-number="8">8</button>
                <button type="button" class="calc-btn calc-number" data-number="9">9</button>
                <button type="button" class="calc-btn calc-operator" data-action="multiply">×</button>

                <button type="button" class="calc-btn calc-number" data-number="4">4</button>
                <button type="button" class="calc-btn calc-number" data-number="5">5</button>
                <button type="button" class="calc-btn calc-number" data-number="6">6</button>
                <button type="button" class="calc-btn calc-operator" data-action="subtract">-</button>

                <button type="button" class="calc-btn calc-number" data-number="1">1</button>
                <button type="button" class="calc-btn calc-number" data-number="2">2</button>
                <button type="button" class="calc-btn calc-number" data-number="3">3</button>
                <button type="button" class="calc-btn calc-operator" data-action="add">+</button>

                <button type="button" class="calc-btn calc-number calc-zero" data-number="0">0</button>
                <button type="button" class="calc-btn calc-decimal" data-action="decimal">.</button>
                <button type="button" class="calc-btn calc-equals" data-action="equals">=</button>
            </div>
        </div>
        <div class="calculator-footer">
            <small>Calculator state is saved during your quiz attempt</small>
        </div>
    </div>
</div>
{% endif %}

<!-- Image Modal -->
<div id="image-modal" class="image-modal" onclick="closeImageModal()">
    <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
    <img class="image-modal-content" id="modal-image" src="" alt="Question Image">
</div>

<!-- Graph Tool Modal -->
<div id="graph-modal" class="graph-modal">
    <div class="graph-modal-content">
        <div class="graph-modal-header">
            <h3>Graph Tool</h3>
            <span class="graph-modal-close" onclick="closeGraphTool()">&times;</span>
        </div>

        <div class="graph-modal-body">
            <!-- Graph Controls -->
            <div class="graph-controls">
                <div class="control-group">
                    <label>Tools:</label>
                    <button id="tool-point" class="tool-btn active" onclick="setTool('point')">
                        <span class="tool-icon">●</span> Point
                    </button>
                    <button id="tool-line" class="tool-btn" onclick="setTool('line')">
                        <span class="tool-icon">─</span> Line
                    </button>
                    <button id="tool-function" class="tool-btn" onclick="setTool('function')">
                        <span class="tool-icon">f(x)</span> Function
                    </button>
                </div>

                <div class="control-group">
                    <label>Actions:</label>
                    <button class="action-btn" onclick="clearGraph()">Clear All</button>
                    <button class="action-btn" onclick="resetView()">Reset View</button>
                    <button class="action-btn" onclick="toggleGrid()">Toggle Grid</button>
                </div>

                <div class="control-group function-input" id="function-input" style="display: none;">
                    <label for="equation-input">Equation (y = ...):</label>
                    <input type="text" id="equation-input" placeholder="e.g., x^2, 2*x + 3, sin(x)" />
                    <button onclick="plotFunction()">Plot</button>
                </div>

                <div class="coordinate-display">
                    <span id="coordinate-info">Click on the graph to see coordinates</span>
                </div>
            </div>

            <!-- Graph Canvas -->
            <div class="graph-container">
                <div id="graph-board" class="graph-board"></div>
            </div>
        </div>

        <div class="graph-modal-footer">
            <button class="btn-secondary" onclick="closeGraphTool()">Close</button>
            <button class="btn-primary" onclick="saveGraphState()">Save & Close</button>
        </div>
    </div>
</div>

<script>
// --- Timer Logic --- 
const timerDisplay = document.querySelector('#timer span');
const quizForm = document.getElementById('quiz-form');
// Ensure time_limit is treated as a number, default to 0 if invalid
const timeLimitMinutes = parseInt("{{ quiz.time_limit | default(0, true) }}", 10) || 0;
let timeLeft = timeLimitMinutes * 60; // Time in seconds

function updateTimer() {
    const minutes = Math.floor(timeLeft / 60);
    let seconds = timeLeft % 60;
    seconds = seconds < 10 ? '0' + seconds : seconds;
    timerDisplay.textContent = `${minutes}:${seconds}`;
    
    if (timeLeft <= 0) {
        clearInterval(timerInterval);
        alert('Time is up! Submitting your quiz.');
        quizForm.submit();
    } else {
        timeLeft--;
    }
}

const timerInterval = setInterval(updateTimer, 1000);

// --- Confirmation Before Submit --- 
quizForm.addEventListener('submit', function(event) {
    if (timeLeft > 0) { // Only ask for confirmation if time is not up
        const confirmation = confirm('Are you sure you want to submit your quiz?');
        if (!confirmation) {
            event.preventDefault(); // Stop submission if user cancels
        }
    }
    // If time is up, form submits automatically without confirmation
});

// --- Strikethrough Logic ---
function toggleStrike(button) {
    const label = button.closest('.option-label');
    const textSpan = label.querySelector('.option-text');
    textSpan.classList.toggle('strikethrough');
}

// --- Calculator Logic ---
class Calculator {
    constructor() {
        this.screen = document.getElementById('calculator-screen');
        this.currentInput = '0';
        this.previousInput = '';
        this.operator = '';
        this.waitingForOperand = false;
        this.sessionKey = 'quiz_calculator_state';

        this.initializeEventListeners();
        this.loadState();
    }

    initializeEventListeners() {
        // Calculator button
        const calcBtn = document.getElementById('calculator-btn');
        if (calcBtn) {
            calcBtn.addEventListener('click', () => this.openCalculator());
        }

        // Close button
        const closeBtn = document.getElementById('calculator-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeCalculator());
        }

        // Modal background click
        const modal = document.getElementById('calculator-modal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeCalculator();
                }
            });
        }

        // Number buttons
        document.querySelectorAll('.calc-number').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.inputNumber(e.target.dataset.number);
            });
        });

        // Operator buttons
        document.querySelectorAll('.calc-operator').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.inputOperator(e.target.dataset.action);
            });
        });

        // Action buttons
        document.querySelectorAll('[data-action="clear"]').forEach(btn => {
            btn.addEventListener('click', () => this.clear());
        });

        document.querySelectorAll('[data-action="clear-entry"]').forEach(btn => {
            btn.addEventListener('click', () => this.clearEntry());
        });

        document.querySelectorAll('[data-action="backspace"]').forEach(btn => {
            btn.addEventListener('click', () => this.backspace());
        });

        document.querySelectorAll('[data-action="decimal"]').forEach(btn => {
            btn.addEventListener('click', () => this.inputDecimal());
        });

        document.querySelectorAll('[data-action="equals"]').forEach(btn => {
            btn.addEventListener('click', () => this.calculate());
        });

        // Keyboard support
        document.addEventListener('keydown', (e) => {
            if (this.isCalculatorOpen()) {
                this.handleKeyboard(e);
            }
        });

        // Prevent copy/paste in calculator screen
        if (this.screen) {
            this.screen.addEventListener('copy', (e) => e.preventDefault());
            this.screen.addEventListener('paste', (e) => e.preventDefault());
            this.screen.addEventListener('cut', (e) => e.preventDefault());
        }
    }

    openCalculator() {
        const modal = document.getElementById('calculator-modal');
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }
    }

    closeCalculator() {
        const modal = document.getElementById('calculator-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling
        }
        this.saveState();
    }

    isCalculatorOpen() {
        const modal = document.getElementById('calculator-modal');
        return modal && modal.style.display === 'block';
    }

    updateScreen() {
        if (this.screen) {
            this.screen.value = this.currentInput;
        }
        this.saveState();
    }

    inputNumber(num) {
        if (this.waitingForOperand) {
            this.currentInput = num;
            this.waitingForOperand = false;
        } else {
            this.currentInput = this.currentInput === '0' ? num : this.currentInput + num;
        }

        // Limit input length
        if (this.currentInput.length > 15) {
            this.currentInput = this.currentInput.slice(0, 15);
        }

        this.updateScreen();
    }

    inputOperator(nextOperator) {
        const inputValue = parseFloat(this.currentInput);

        if (this.previousInput === '') {
            this.previousInput = inputValue;
        } else if (this.operator) {
            const currentValue = this.previousInput || 0;
            const newValue = this.performCalculation(currentValue, inputValue, this.operator);

            this.currentInput = String(newValue);
            this.previousInput = newValue;
        }

        this.waitingForOperand = true;
        this.operator = nextOperator;
        this.updateScreen();
    }

    inputDecimal() {
        if (this.waitingForOperand) {
            this.currentInput = '0.';
            this.waitingForOperand = false;
        } else if (this.currentInput.indexOf('.') === -1) {
            this.currentInput += '.';
        }

        this.updateScreen();
    }

    clear() {
        this.currentInput = '0';
        this.previousInput = '';
        this.operator = '';
        this.waitingForOperand = false;
        this.updateScreen();
    }

    clearEntry() {
        this.currentInput = '0';
        this.updateScreen();
    }

    backspace() {
        if (this.currentInput.length > 1) {
            this.currentInput = this.currentInput.slice(0, -1);
        } else {
            this.currentInput = '0';
        }
        this.updateScreen();
    }

    calculate() {
        const inputValue = parseFloat(this.currentInput);

        if (this.previousInput !== '' && this.operator) {
            const currentValue = this.previousInput || 0;
            const newValue = this.performCalculation(currentValue, inputValue, this.operator);

            this.currentInput = String(newValue);
            this.previousInput = '';
            this.operator = '';
            this.waitingForOperand = true;
        }

        this.updateScreen();
    }

    performCalculation(firstOperand, secondOperand, operator) {
        let result;

        switch (operator) {
            case 'add':
                result = firstOperand + secondOperand;
                break;
            case 'subtract':
                result = firstOperand - secondOperand;
                break;
            case 'multiply':
                result = firstOperand * secondOperand;
                break;
            case 'divide':
                if (secondOperand === 0) {
                    return 'Error';
                }
                result = firstOperand / secondOperand;
                break;
            default:
                return secondOperand;
        }

        // Round to avoid floating point precision issues
        return Math.round(result * 100000000) / 100000000;
    }

    handleKeyboard(e) {
        const key = e.key;

        if (key >= '0' && key <= '9') {
            e.preventDefault();
            this.inputNumber(key);
        } else if (key === '.') {
            e.preventDefault();
            this.inputDecimal();
        } else if (key === '+') {
            e.preventDefault();
            this.inputOperator('add');
        } else if (key === '-') {
            e.preventDefault();
            this.inputOperator('subtract');
        } else if (key === '*') {
            e.preventDefault();
            this.inputOperator('multiply');
        } else if (key === '/') {
            e.preventDefault();
            this.inputOperator('divide');
        } else if (key === 'Enter' || key === '=') {
            e.preventDefault();
            this.calculate();
        } else if (key === 'Escape') {
            e.preventDefault();
            this.closeCalculator();
        } else if (key === 'Backspace') {
            e.preventDefault();
            this.backspace();
        } else if (key === 'Delete') {
            e.preventDefault();
            this.clear();
        }
    }

    saveState() {
        const state = {
            currentInput: this.currentInput,
            previousInput: this.previousInput,
            operator: this.operator,
            waitingForOperand: this.waitingForOperand
        };

        try {
            sessionStorage.setItem(this.sessionKey, JSON.stringify(state));
        } catch (e) {
            console.warn('Could not save calculator state to sessionStorage');
        }
    }

    loadState() {
        try {
            const savedState = sessionStorage.getItem(this.sessionKey);
            if (savedState) {
                const state = JSON.parse(savedState);
                this.currentInput = state.currentInput || '0';
                this.previousInput = state.previousInput || '';
                this.operator = state.operator || '';
                this.waitingForOperand = state.waitingForOperand || false;
                this.updateScreen();
            }
        } catch (e) {
            console.warn('Could not load calculator state from sessionStorage');
        }
    }
}

// Initialize calculator when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('calculator-btn')) {
        new Calculator();
    }

    // Initialize MathJax rendering for math expressions
    if (window.MathJax) {
        MathJax.typesetPromise().then(() => {
            console.log('MathJax rendering complete');
        }).catch((err) => {
            console.log('MathJax error:', err.message);
        });
    }
});

// Image Modal Functions
function openImageModal(imageSrc) {
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    modal.style.display = 'block';
    modalImage.src = imageSrc;
}

function closeImageModal() {
    const modal = document.getElementById('image-modal');
    modal.style.display = 'none';
}

// Close modal when clicking outside the image
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeImageModal();
        closeGraphTool();
    }
});

// === GRAPH TOOL FUNCTIONALITY ===

let graphBoard = null;
let currentTool = 'point';
let graphElements = [];
let isGridVisible = true;

function initializeGraphTool() {
    if (typeof JXG === 'undefined') {
        console.error('JSXGraph library not loaded');
        return;
    }

    // Initialize the graph board
    graphBoard = JXG.JSXGraph.initBoard('graph-board', {
        boundingbox: [-10, 10, 10, -10],
        axis: true,
        grid: true,
        showCopyright: false,
        showNavigation: true,
        zoom: {
            factorX: 1.25,
            factorY: 1.25,
            wheel: true,
            needshift: false,
            eps: 0.1
        },
        pan: {
            enabled: true,
            needshift: false
        }
    });

    // Add click event listener for point plotting
    graphBoard.on('down', function(e) {
        if (currentTool === 'point') {
            const coords = getMouseCoords(e);
            createPoint(coords[0], coords[1]);
        }
    });

    // Add mouse move listener for coordinate display
    graphBoard.on('move', function(e) {
        const coords = getMouseCoords(e);
        updateCoordinateDisplay(coords[0], coords[1]);
    });
}

function getMouseCoords(e) {
    const pos = graphBoard.getUsrCoordsOfMouse(e);
    return [pos[0], pos[1]];
}

function updateCoordinateDisplay(x, y) {
    const coordInfo = document.getElementById('coordinate-info');
    coordInfo.textContent = `(${x.toFixed(2)}, ${y.toFixed(2)})`;
}

function setTool(tool) {
    currentTool = tool;

    // Update button states
    document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(`tool-${tool}`).classList.add('active');

    // Show/hide function input
    const functionInput = document.getElementById('function-input');
    if (tool === 'function') {
        functionInput.style.display = 'flex';
    } else {
        functionInput.style.display = 'none';
    }

    // Update cursor and instructions
    updateInstructions(tool);
}

function updateInstructions(tool) {
    const coordInfo = document.getElementById('coordinate-info');
    switch(tool) {
        case 'point':
            coordInfo.textContent = 'Click to place points';
            break;
        case 'line':
            coordInfo.textContent = 'Click two points to draw a line';
            break;
        case 'function':
            coordInfo.textContent = 'Enter equation and click Plot';
            break;
        default:
            coordInfo.textContent = 'Move mouse to see coordinates';
    }
}

function createPoint(x, y) {
    const point = graphBoard.create('point', [x, y], {
        name: `P${graphElements.length + 1}`,
        size: 4,
        fillColor: '#007bff',
        strokeColor: '#007bff'
    });

    graphElements.push({
        type: 'point',
        element: point,
        coords: [x, y]
    });

    return point;
}

let linePoints = [];

function createLine() {
    if (linePoints.length === 2) {
        const line = graphBoard.create('line', linePoints, {
            strokeColor: '#28a745',
            strokeWidth: 2
        });

        graphElements.push({
            type: 'line',
            element: line,
            points: linePoints.slice()
        });

        linePoints = [];
        return line;
    }
}

function plotFunction() {
    const equationInput = document.getElementById('equation-input');
    const equation = equationInput.value.trim();

    if (!equation) {
        alert('Please enter an equation');
        return;
    }

    try {
        // Convert common math notation to JSXGraph format
        let jsxEquation = equation
            .replace(/\^/g, '**')  // x^2 -> x**2
            .replace(/(\d)([a-zA-Z])/g, '$1*$2')  // 2x -> 2*x
            .replace(/sin/g, 'Math.sin')
            .replace(/cos/g, 'Math.cos')
            .replace(/tan/g, 'Math.tan')
            .replace(/log/g, 'Math.log10')
            .replace(/ln/g, 'Math.log')
            .replace(/sqrt/g, 'Math.sqrt')
            .replace(/pi/g, 'Math.PI')
            .replace(/e(?![a-zA-Z])/g, 'Math.E');

        const func = graphBoard.create('functiongraph', [
            function(x) {
                try {
                    return eval(jsxEquation.replace(/x/g, x));
                } catch (e) {
                    return NaN;
                }
            }
        ], {
            strokeColor: '#dc3545',
            strokeWidth: 2
        });

        graphElements.push({
            type: 'function',
            element: func,
            equation: equation
        });

        equationInput.value = '';

    } catch (error) {
        alert('Invalid equation. Please check your syntax.');
        console.error('Function plotting error:', error);
    }
}

function clearGraph() {
    if (confirm('Clear all elements from the graph?')) {
        graphElements.forEach(item => {
            graphBoard.removeObject(item.element);
        });
        graphElements = [];
        linePoints = [];
    }
}

function resetView() {
    if (graphBoard) {
        graphBoard.setBoundingBox([-10, 10, 10, -10]);
    }
}

function toggleGrid() {
    if (graphBoard) {
        isGridVisible = !isGridVisible;
        graphBoard.setAttribute({grid: isGridVisible});
    }
}

function openGraphTool() {
    const modal = document.getElementById('graph-modal');
    modal.style.display = 'block';

    // Initialize graph if not already done
    if (!graphBoard) {
        setTimeout(() => {
            initializeGraphTool();
        }, 100);
    } else {
        // Resize board if already initialized
        setTimeout(() => {
            graphBoard.resizeContainer();
        }, 100);
    }
}

function closeGraphTool() {
    const modal = document.getElementById('graph-modal');
    modal.style.display = 'none';
}

function saveGraphState() {
    // For now, just close the modal
    // In the future, this could save the graph state to local storage
    // or allow students to export the graph
    closeGraphTool();
}

// Handle line tool clicks
document.addEventListener('click', function(e) {
    if (currentTool === 'line' && graphBoard && e.target.closest('#graph-board')) {
        const coords = getMouseCoords(e);
        const point = createPoint(coords[0], coords[1]);
        linePoints.push(point);

        if (linePoints.length === 2) {
            createLine();
        }
    }
});

</script>

<style>
.quiz-attempt-container {
    max-width: 900px;
    margin: 2rem auto;
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.quiz-tools {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #007bff;
    background-color: #007bff;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tool-btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

.calculator-btn, .graph-tool-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.graph-tool-btn {
    background-color: #28a745;
    border-color: #28a745;
}

.graph-tool-btn:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.quiz-header h1 {
    margin: 0;
    color: #343a40;
}

#timer {
    font-size: 1.2em;
    font-weight: bold;
    color: #dc3545;
}

.quiz-description {
    background-color: #e9ecef;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 2rem;
    color: #495057;
}

.question-card {
    background-color: #fff;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 0.8rem;
}

.question-number {
    font-weight: bold;
    font-size: 1.1em;
    color: #007bff;
}

.question-marks {
    font-size: 0.9em;
    color: #6c757d;
}

.question-text {
    margin-bottom: 1.2rem;
    font-size: 1.1em;
    color: #212529;
    line-height: 1.6;
}

.options {
    display: flex;
    flex-direction: column;
    gap: 0.8rem; /* Spacing between options */
}

.option-item {
    display: flex; /* Align items for spacing */
    align-items: center; 
}

/* Hide the default radio button */
.option-item input[type="radio"] {
   position: absolute;
   opacity: 0;
   width: 0;
   height: 0;
}

.option-label {
    display: flex; /* Use flex to align text and button */
    align-items: center;
    width: 100%; /* Make label take full width */
    padding: 0.8rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 5px;
    background-color: #fff;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
}

/* Style the label when the radio is checked */
.option-item input[type="radio"]:checked + .option-label {
    background-color: #e2f0ff; /* Light blue background */
    border-color: #007bff;
}

.option-label:hover {
    background-color: #f1f3f5;
}

.option-text {
    flex-grow: 1; /* Allow text to take available space */
    margin-right: 10px; /* Space between text and strike button */
    transition: text-decoration 0.2s;
}

.strike-btn {
    background: none;
    border: 1px solid #adb5bd;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8em;
    line-height: 1;
    margin-left: auto; /* Push button to the right */
}

.strike-btn s {
    text-decoration: none; /* Remove default strikethrough */
}

.strike-btn:hover {
    background-color: #e9ecef;
}

.option-text.strikethrough {
    text-decoration: line-through;
    color: #adb5bd; /* Grey out struck-through text */
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.1em;
    margin-top: 2rem;
}

/* Calculator Styles */
.calculator-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.calculator-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 320px;
    max-width: 90vw;
    overflow: hidden;
}

.calculator-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calculator-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.calculator-body {
    padding: 1rem;
}

.calculator-display {
    margin-bottom: 1rem;
}

#calculator-screen {
    width: 100%;
    height: 60px;
    font-size: 1.5rem;
    text-align: right;
    padding: 0 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    font-family: 'Courier New', monospace;
    color: #333;
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.calc-btn {
    height: 50px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
    color: #333;
}

.calc-btn:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calc-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.calc-number {
    background-color: #fff;
}

.calc-number:hover {
    background-color: #e3f2fd;
}

.calc-operator {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.calc-operator:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.calc-equals {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

.calc-equals:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
}

.calc-clear {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.calc-clear:hover {
    background-color: #c82333;
    border-color: #c82333;
}

.calc-backspace {
    background-color: #ffc107;
    color: #333;
    border-color: #ffc107;
}

.calc-backspace:hover {
    background-color: #e0a800;
    border-color: #e0a800;
}

.calc-decimal {
    background-color: #6c757d;
    color: white;
    border-color: #6c757d;
}

.calc-decimal:hover {
    background-color: #545b62;
    border-color: #545b62;
}

.calc-zero {
    grid-column: span 2;
}

.calculator-footer {
    background-color: #f8f9fa;
    padding: 0.75rem 1rem;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.calculator-footer small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .calculator-container {
        width: 280px;
    }

    .calc-btn {
        height: 45px;
        font-size: 1rem;
    }

    #calculator-screen {
        height: 50px;
        font-size: 1.3rem;
    }
}

/* Question Image Styles */
.question-image-container {
    margin: 1rem 0;
    text-align: center;
}

.question-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.question-image:hover {
    transform: scale(1.02);
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    cursor: pointer;
}

.image-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
    border-radius: 8px;
}

.image-modal-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.image-modal-close:hover {
    opacity: 0.7;
}

/* Graph Tool Modal Styles */
.graph-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.graph-modal-content {
    position: relative;
    background-color: white;
    margin: 2% auto;
    width: 95%;
    max-width: 1200px;
    height: 90%;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.graph-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.graph-modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.25rem;
}

.graph-modal-close {
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    line-height: 1;
}

.graph-modal-close:hover {
    color: #000;
}

.graph-modal-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    overflow: hidden;
}

.graph-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #495057;
    margin-right: 0.5rem;
}

.tool-btn, .action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ced4da;
    background-color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.tool-btn:hover, .action-btn:hover {
    background-color: #e9ecef;
}

.tool-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.tool-icon {
    font-weight: bold;
    margin-right: 0.25rem;
}

.function-input {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
}

.function-input input {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    width: 200px;
}

.function-input button {
    padding: 0.5rem 1rem;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.function-input button:hover {
    background-color: #218838;
}

.coordinate-display {
    margin-left: auto;
    font-family: monospace;
    font-size: 0.875rem;
    color: #495057;
    background-color: white;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.graph-container {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    background-color: white;
}

.graph-board {
    width: 100%;
    height: 100%;
    min-height: 400px;
}

.graph-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.btn-primary, .btn-secondary {
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

/* Responsive adjustments for graph tool */
@media (max-width: 768px) {
    .graph-modal-content {
        width: 98%;
        height: 95%;
        margin: 1% auto;
    }

    .graph-controls {
        flex-direction: column;
        gap: 0.5rem;
    }

    .control-group {
        flex-wrap: wrap;
    }

    .coordinate-display {
        margin-left: 0;
        margin-top: 0.5rem;
    }
}

</style>
{% endblock %} 