<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ worksheet.title }}</title>
    <style>
        @page {
            size: A4;
            margin: 1in;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
        }
        
        .worksheet-title-section {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid #000;
            padding-bottom: 1rem;
        }
        
        .worksheet-title {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .worksheet-info {
            margin: 1rem 0;
        }
        
        .info-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 11pt;
        }
        
        .info-label {
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .info-line {
            border-bottom: 1px solid #000;
            flex: 1;
            margin: 0 1rem;
            min-width: 150px;
            height: 1.2em;
        }
        
        .worksheet-instructions {
            text-align: left;
            margin-top: 1rem;
            padding: 0.75rem;
            border: 1px solid #000;
            font-size: 10pt;
        }
        
        .questions-section {
            margin: 2rem 0;
        }
        
        .question-item {
            display: flex;
            margin-bottom: 2rem;
            page-break-inside: avoid;
        }
        
        .question-number {
            font-weight: bold;
            font-size: 12pt;
            margin-right: 1rem;
            min-width: 2rem;
        }
        
        .question-content {
            flex: 1;
        }
        
        .question-text {
            font-size: 11pt;
            line-height: 1.5;
            margin-bottom: 1rem;
        }
        
        .question-image {
            margin: 1rem 0;
            text-align: center;
        }
        
        .question-image img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #ccc;
        }
        
        .question-options {
            margin: 1rem 0;
        }
        
        .option {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }
        
        .option-letter {
            font-weight: bold;
            margin-right: 0.5rem;
            min-width: 1.5rem;
        }
        
        .option-text {
            flex: 1;
            line-height: 1.4;
        }
        
        .answer-space {
            margin-top: 1rem;
            display: flex;
            align-items: center;
        }
        
        .answer-label {
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .answer-line {
            border-bottom: 1px solid #000;
            min-width: 100px;
            height: 1.2em;
        }
        
        .answer-space-large {
            margin-top: 1rem;
        }
        
        .answer-lines {
            margin-top: 0.5rem;
        }
        
        .answer-line-full {
            border-bottom: 1px solid #000;
            margin-bottom: 0.5rem;
            height: 1.5rem;
        }
        
        .answer-key-section {
            page-break-before: always;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 2px solid #000;
        }
        
        .answer-key-title {
            text-align: center;
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }
        
        .answer-key-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }
        
        .answer-key-item {
            display: flex;
            align-items: flex-start;
            padding: 0.25rem 0;
            font-size: 10pt;
        }
        
        .answer-key-number {
            font-weight: bold;
            margin-right: 0.5rem;
            min-width: 2rem;
        }
        
        .answer-key-answer {
            flex: 1;
        }
        
        .worksheet-footer {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #ccc;
            text-align: center;
            font-size: 9pt;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- Worksheet Title Section -->
    <div class="worksheet-title-section">
        <h1 class="worksheet-title">{{ worksheet.title }}</h1>
        <div class="worksheet-info">
            <div class="info-row">
                <span class="info-label">Name:</span>
                <span class="info-line"></span>
                <span class="info-label">Date:</span>
                <span class="info-line"></span>
            </div>
            <div class="info-row">
                <span class="info-label">Class:</span>
                <span class="info-line"></span>
                <span class="info-label">Score:</span>
                <span class="info-line"></span>
            </div>
        </div>
        <div class="worksheet-instructions">
            <p><strong>Instructions:</strong> Solve each problem carefully. Show your work where applicable.</p>
            <p><strong>Difficulty Level:</strong> 
                {% if worksheet.difficulty_level == 1 %}Easy{% elif worksheet.difficulty_level == 2 %}Medium{% else %}Hard{% endif %}
                | <strong>Total Questions:</strong> {{ worksheet.questions|length }}
            </p>
        </div>
    </div>

    <!-- Questions Section -->
    <div class="questions-section">
        {% for question in worksheet.questions %}
        <div class="question-item">
            <div class="question-number">{{ loop.index }}.</div>
            <div class="question-content">
                <div class="question-text">
                    {{ question.question_text }}
                    
                    <!-- Display image if available -->
                    {% if question.image_filename %}
                    <div class="question-image">
                        <img src="{{ url_for('static', filename='uploads/' + question.image_filename, _external=True) }}" 
                             alt="Question Image">
                    </div>
                    {% endif %}
                </div>
                
                {% if question.question_type == 'mcq' %}
                <div class="question-options">
                    {% if question.option1 %}
                    <div class="option">
                        <span class="option-letter">A)</span>
                        <span class="option-text">{{ question.option1 }}</span>
                    </div>
                    {% endif %}
                    {% if question.option2 %}
                    <div class="option">
                        <span class="option-letter">B)</span>
                        <span class="option-text">{{ question.option2 }}</span>
                    </div>
                    {% endif %}
                    {% if question.option3 %}
                    <div class="option">
                        <span class="option-letter">C)</span>
                        <span class="option-text">{{ question.option3 }}</span>
                    </div>
                    {% endif %}
                    {% if question.option4 %}
                    <div class="option">
                        <span class="option-letter">D)</span>
                        <span class="option-text">{{ question.option4 }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="answer-space">
                    <span class="answer-label">Answer:</span>
                    <span class="answer-line"></span>
                </div>
                {% else %}
                <!-- For other question types, provide space for written answers -->
                <div class="answer-space-large">
                    <div class="answer-lines">
                        <div class="answer-line-full"></div>
                        <div class="answer-line-full"></div>
                        <div class="answer-line-full"></div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Answer Key Section (if included) -->
    {% if worksheet.include_answers %}
    <div class="answer-key-section">
        <h2 class="answer-key-title">Answer Key</h2>
        <div class="answer-key-grid">
            {% for question in worksheet.questions %}
            <div class="answer-key-item">
                <span class="answer-key-number">{{ loop.index }}.</span>
                <span class="answer-key-answer">
                    {% if question.question_type == 'mcq' %}
                        {% if question.correct_answer == '1' %}A) {{ question.option1 }}
                        {% elif question.correct_answer == '2' %}B) {{ question.option2 }}
                        {% elif question.correct_answer == '3' %}C) {{ question.option3 }}
                        {% elif question.correct_answer == '4' %}D) {{ question.option4 }}
                        {% else %}{{ question.correct_answer }}
                        {% endif %}
                    {% else %}
                        {{ question.correct_answer }}
                    {% endif %}
                </span>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Footer -->
    <div class="worksheet-footer">
        <div class="footer-info">
            Generated by {{ worksheet.teacher_name }} on {{ worksheet.generated_at.strftime('%B %d, %Y at %I:%M %p') }}
            | Math Quiz Management System
        </div>
    </div>
</body>
</html>
