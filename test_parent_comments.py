#!/usr/bin/env python3
"""
Comprehensive Test Script for Parent Comments Feature
Tests parent comment submission, teacher viewing, and security restrictions.
"""

import sqlite3
import os
from datetime import datetime

def get_database_path():
    """Get the path to the database file."""
    # Check for database in instance folder first
    instance_path = os.path.join(os.getcwd(), 'instance')
    if os.path.exists(instance_path):
        db_files = [f for f in os.listdir(instance_path) if f.endswith('.db')]
        if db_files:
            return os.path.join(instance_path, db_files[0])
    
    # Fallback to current directory
    db_files = [f for f in os.listdir('.') if f.endswith('.db')]
    if db_files:
        return db_files[0]
    
    raise FileNotFoundError("No database file found.")

def test_database_schema():
    """Test 1: Verify parent_comment table exists with correct schema."""
    print("=== Test 1: Database Schema ===")
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if parent_comment table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='parent_comment'
        """)
        
        if not cursor.fetchone():
            print("❌ FAILED: parent_comment table does not exist")
            return False
        
        # Check table structure
        cursor.execute("PRAGMA table_info(parent_comment)")
        columns = cursor.fetchall()
        column_names = {col[1] for col in columns}
        
        expected_columns = {
            'id', 'student_id', 'quiz_id', 'attempt_id', 
            'parent_id', 'comment_text', 'timestamp'
        }
        
        if not expected_columns.issubset(column_names):
            missing = expected_columns - column_names
            print(f"❌ FAILED: Missing columns: {missing}")
            return False
        
        print("✅ PASSED: parent_comment table exists with correct schema")
        print(f"   Columns: {', '.join(sorted(column_names))}")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Database schema test error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_foreign_key_relationships():
    """Test 2: Verify foreign key relationships are properly set up."""
    print("\n=== Test 2: Foreign Key Relationships ===")
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check foreign key constraints
        cursor.execute("PRAGMA foreign_key_list(parent_comment)")
        foreign_keys = cursor.fetchall()
        
        expected_fks = {
            'student_id': 'user',
            'quiz_id': 'quiz',
            'attempt_id': 'quiz_attempt',
            'parent_id': 'user'
        }
        
        actual_fks = {fk[3]: fk[2] for fk in foreign_keys}
        
        for column, table in expected_fks.items():
            if column not in actual_fks:
                print(f"❌ FAILED: Missing foreign key for {column}")
                return False
            if actual_fks[column] != table:
                print(f"❌ FAILED: Wrong foreign key table for {column}: expected {table}, got {actual_fks[column]}")
                return False
        
        print("✅ PASSED: All foreign key relationships are correct")
        print(f"   Foreign keys: {len(foreign_keys)} found")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Foreign key test error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_data_relationships():
    """Test 3: Verify data relationships and sample data."""
    print("\n=== Test 3: Data Relationships ===")
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if we have users with different roles
        cursor.execute("SELECT role, COUNT(*) FROM user GROUP BY role")
        roles = dict(cursor.fetchall())
        
        required_roles = ['parent', 'teacher', 'student']
        for role in required_roles:
            if role not in roles:
                print(f"⚠ WARNING: No {role} users found in database")
            else:
                print(f"   {role.title()}s: {roles[role]}")
        
        # Check if we have quiz attempts
        cursor.execute("SELECT COUNT(*) FROM quiz_attempt")
        attempt_count = cursor.fetchone()[0]
        print(f"   Quiz attempts: {attempt_count}")
        
        # Check if we have quizzes
        cursor.execute("SELECT COUNT(*) FROM quiz WHERE is_active = 1")
        quiz_count = cursor.fetchone()[0]
        print(f"   Active quizzes: {quiz_count}")
        
        # Check parent-student relationships
        cursor.execute("""
            SELECT COUNT(*) FROM user s 
            JOIN user p ON s.parent_email = p.email 
            WHERE s.role = 'student' AND p.role = 'parent'
        """)
        parent_child_links = cursor.fetchone()[0]
        print(f"   Parent-child links: {parent_child_links}")
        
        if attempt_count > 0 and quiz_count > 0:
            print("✅ PASSED: Database has sufficient test data")
            return True
        else:
            print("⚠ WARNING: Limited test data available")
            return True
        
    except Exception as e:
        print(f"❌ FAILED: Data relationships test error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_comment_insertion():
    """Test 4: Test comment insertion functionality."""
    print("\n=== Test 4: Comment Insertion ===")
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Find a quiz attempt with parent-student relationship
        cursor.execute("""
            SELECT qa.id, qa.student_id, qa.quiz_id, s.parent_email, p.id as parent_id
            FROM quiz_attempt qa
            JOIN user s ON qa.student_id = s.id
            JOIN user p ON s.parent_email = p.email
            WHERE s.role = 'student' AND p.role = 'parent'
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("⚠ WARNING: No suitable quiz attempt found for testing")
            return True
        
        attempt_id, student_id, quiz_id, parent_email, parent_id = result
        
        # Insert a test comment
        test_comment = f"Test comment inserted at {datetime.now()}"
        cursor.execute("""
            INSERT INTO parent_comment 
            (student_id, quiz_id, attempt_id, parent_id, comment_text, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (student_id, quiz_id, attempt_id, parent_id, test_comment, datetime.now()))
        
        comment_id = cursor.lastrowid
        conn.commit()
        
        # Verify the comment was inserted
        cursor.execute("SELECT * FROM parent_comment WHERE id = ?", (comment_id,))
        inserted_comment = cursor.fetchone()
        
        if inserted_comment:
            print("✅ PASSED: Comment insertion successful")
            print(f"   Comment ID: {comment_id}")
            print(f"   Student ID: {student_id}, Parent ID: {parent_id}")
            
            # Clean up test comment
            cursor.execute("DELETE FROM parent_comment WHERE id = ?", (comment_id,))
            conn.commit()
            print("   Test comment cleaned up")
            return True
        else:
            print("❌ FAILED: Comment was not inserted")
            return False
        
    except Exception as e:
        print(f"❌ FAILED: Comment insertion test error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_teacher_quiz_relationship():
    """Test 5: Verify teacher can see comments for their quizzes only."""
    print("\n=== Test 5: Teacher-Quiz Relationship ===")
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if teachers have quizzes
        cursor.execute("""
            SELECT u.id, u.name, COUNT(q.id) as quiz_count
            FROM user u
            LEFT JOIN quiz q ON u.id = q.teacher_id AND q.is_active = 1
            WHERE u.role = 'teacher'
            GROUP BY u.id, u.name
        """)
        
        teachers = cursor.fetchall()
        
        if not teachers:
            print("⚠ WARNING: No teachers found in database")
            return True
        
        teachers_with_quizzes = [t for t in teachers if t[2] > 0]
        
        print(f"   Total teachers: {len(teachers)}")
        print(f"   Teachers with quizzes: {len(teachers_with_quizzes)}")
        
        for teacher_id, teacher_name, quiz_count in teachers_with_quizzes[:3]:  # Show first 3
            print(f"   {teacher_name}: {quiz_count} quiz(s)")
        
        if teachers_with_quizzes:
            print("✅ PASSED: Teachers have quizzes for comment testing")
            return True
        else:
            print("⚠ WARNING: No teachers have quizzes")
            return True
        
    except Exception as e:
        print(f"❌ FAILED: Teacher-quiz relationship test error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_existing_comments():
    """Test 6: Check for existing parent comments."""
    print("\n=== Test 6: Existing Comments ===")
    
    try:
        db_path = get_database_path()
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Count existing comments
        cursor.execute("SELECT COUNT(*) FROM parent_comment")
        comment_count = cursor.fetchone()[0]
        
        print(f"   Existing parent comments: {comment_count}")
        
        if comment_count > 0:
            # Show sample comments
            cursor.execute("""
                SELECT pc.id, s.name as student_name, p.name as parent_name, 
                       q.title as quiz_title, pc.timestamp
                FROM parent_comment pc
                JOIN user s ON pc.student_id = s.id
                JOIN user p ON pc.parent_id = p.id
                JOIN quiz q ON pc.quiz_id = q.id
                ORDER BY pc.timestamp DESC
                LIMIT 3
            """)
            
            comments = cursor.fetchall()
            print("   Recent comments:")
            for comment in comments:
                comment_id, student, parent, quiz, timestamp = comment
                print(f"     ID {comment_id}: {parent} → {student} on '{quiz}' ({timestamp})")
        
        print("✅ PASSED: Comment data analysis complete")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Existing comments test error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Run all tests."""
    print("=== Parent Comments Feature Test Suite ===")
    print(f"Started at: {datetime.now()}")
    
    tests = [
        test_database_schema,
        test_foreign_key_relationships,
        test_data_relationships,
        test_comment_insertion,
        test_teacher_quiz_relationship,
        test_existing_comments
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ FAILED: {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Parent comments feature is ready.")
    else:
        print("⚠ Some tests failed or had warnings. Review the output above.")
    
    print(f"\nCompleted at: {datetime.now()}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
