{% extends "base.html" %}

{% block title %}Quiz Attempts - Admin{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<style>
/* Time Filter Styles */
.time-filter {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.time-filter h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.filter-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.6rem 1.2rem;
    background-color: #ffffff;
    color: #495057;
    text-decoration: none;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-block;
    font-size: 0.9rem;
}

.filter-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    text-decoration: none;
    color: #495057;
}

.filter-btn.active {
    background-color: #007bff;
    color: #ffffff;
    border-color: #007bff;
}

.filter-btn.active:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: #ffffff;
}

/* Attempts Count */
.attempts-count {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    color: #0056b3;
    font-weight: 500;
}

/* Table Enhancements */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Score styling */
.score-excellent { color: #28a745; font-weight: 600; }
.score-good { color: #17a2b8; font-weight: 600; }
.score-average { color: #ffc107; font-weight: 600; }
.score-poor { color: #dc3545; font-weight: 600; }

/* Date styling */
.date-recent { color: #28a745; }
.date-old { color: #6c757d; }

/* Empty state */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-buttons {
        flex-direction: column;
    }
    
    .filter-btn {
        text-align: center;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container admin-container">
    <div class="admin-header">
        <h1>Quiz Attempts</h1>
        <div class="admin-actions">
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <div class="admin-content">
        <div class="card">
            <div class="card-header">
                <h2>Filter Quiz Attempts</h2>
            </div>
            <div class="card-body">
                <!-- Time Filter -->
                <div class="time-filter">
                    <h3>Filter by Time Period:</h3>
                    <div class="filter-buttons">
                        {% for filter_option in valid_filters %}
                            <a href="{{ url_for('admin_quiz_attempts', time_filter=filter_option) }}" 
                               class="filter-btn {% if time_filter == filter_option %}active{% endif %}">
                                {{ filter_option }}
                            </a>
                        {% endfor %}
                    </div>
                </div>

                <!-- Attempts Count -->
                {% if attempts_count > 0 %}
                    <div class="attempts-count">
                        📊 Showing {{ attempts_count }} quiz attempt{{ 's' if attempts_count != 1 else '' }}
                        {% if time_filter != 'All' %} for {{ time_filter.lower() }}{% endif %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Quiz Attempts Table -->
        <div class="card">
            <div class="card-header">
                <h2>Quiz Attempts 
                    {% if time_filter != 'All' %}
                        <small class="text-muted">({{ time_filter }})</small>
                    {% endif %}
                </h2>
            </div>
            <div class="card-body">
                {% if attempts %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Student</th>
                                    <th>Quiz Title</th>
                                    <th>Score</th>
                                    <th>Difficulty</th>
                                    <th>Date & Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attempt in attempts %}
                                <tr>
                                    <td>
                                        <strong>{{ attempt.student.name }}</strong><br>
                                        <small class="text-muted">{{ attempt.student.email }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ attempt.quiz.title }}</strong><br>
                                        <small class="text-muted">by {{ attempt.quiz.teacher.name }}</small>
                                    </td>
                                    <td>
                                        {% set score = attempt.score %}
                                        <span class="{% if score >= 90 %}score-excellent{% elif score >= 75 %}score-good{% elif score >= 60 %}score-average{% else %}score-poor{% endif %}">
                                            {{ "%.1f"|format(score) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ attempt.quiz.difficulty|lower }}">
                                            {{ attempt.quiz.difficulty }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="date-recent">
                                            {{ attempt.submitted_at.strftime('%Y-%m-%d') }}<br>
                                            <small>{{ attempt.submitted_at.strftime('%H:%M') }}</small>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('view_past_result', attempt_id=attempt.id) }}" 
                                           class="btn btn-sm btn-info" 
                                           title="View detailed results">
                                            View Details
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="empty-state">
                        <i>📝</i>
                        <h3>No Quiz Attempts Found</h3>
                        <p>
                            {% if time_filter == 'All' %}
                                No quiz attempts have been recorded yet.
                            {% else %}
                                No quiz attempts found for {{ time_filter.lower() }}.
                            {% endif %}
                        </p>
                        {% if time_filter != 'All' %}
                            <a href="{{ url_for('admin_quiz_attempts', time_filter='All') }}" class="btn btn-primary">
                                View All Attempts
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
