{% extends "base.html" %}

{% block title %}Admin - Quiz Management{% endblock %}

{% block content %}
<div class="container admin-container">
    <div class="admin-header">
        <h1>Quiz Management</h1>
        <div class="admin-header-actions">
            <a href="/teacher/export-all-quizzes" class="btn btn-success" title="Export all quizzes as ZIP">
                📦 Export All Quizzes
            </a>
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <div class="admin-content">
        <div class="card">
            <div class="card-header">
                <h2>All Quizzes</h2>
            </div>
            <div class="card-body">
                {% if quizzes %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Teacher</th>
                                    <th>Difficulty</th>
                                    <th>Questions</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for quiz in quizzes %}
                                <tr>
                                    <td>{{ quiz.title }}</td>
                                    <td>
                                        {{ quiz.teacher.name }}
                                        {% if quiz.teacher.role == 'admin' %}
                                            <span class="badge badge-danger">Admin</span>
                                        {% endif %}
                                    </td>
                                    <td><span class="badge badge-{{ quiz.difficulty|lower }}">{{ quiz.difficulty }}</span></td>
                                    <td>{{ quiz.questions|length }}</td>
                                    <td>{{ quiz.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <a href="{{ url_for('view_quiz', quiz_id=quiz.id) }}" class="btn btn-sm btn-info">View</a>
                                        <a href="/teacher/export-quiz/{{ quiz.id }}" class="btn btn-sm btn-success" title="Export as CSV">📊</a>
                                        <button class="btn btn-sm btn-danger" onclick="deleteQuiz({{ quiz.id }})">Delete</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No quizzes found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin: 0;
    color: #2c3e50;
}

.admin-header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.btn-success {
    background-color: #28a745;
    color: white;
    border: 1px solid #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: #2c3e50;
}

.card-body {
    padding: 1rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    border-bottom: 1px solid #eee;
}

.table th {
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

.badge-easy { background: #d4edda; color: #155724; }
.badge-medium { background: #fff3cd; color: #856404; }
.badge-hard { background: #f8d7da; color: #721c24; }

.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}
</style>

<script>
function deleteQuiz(quizId) {
    if (confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
        fetch(`/teacher/delete-quiz/${quizId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert('Quiz deleted successfully');
                location.reload();
            } else {
                alert('Error deleting quiz');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting quiz');
        });
    }
}
</script>
{% endblock %} 