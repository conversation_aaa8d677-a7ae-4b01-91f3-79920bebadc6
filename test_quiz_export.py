#!/usr/bin/env python3
"""
Test Script for Quiz Export Functionality
Tests CSV export and ZIP bulk export features
"""

import requests
import sqlite3
import os
import csv
import zipfile
import io
from datetime import datetime

class QuizExportTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def get_database_path(self):
        """Get the path to the SQLite database"""
        if os.path.exists('instance/quiz.db'):
            return 'instance/quiz.db'
        elif os.path.exists('quiz.db'):
            return 'quiz.db'
        elif os.path.exists('instance/quiz_management.db'):
            return 'instance/quiz_management.db'
        elif os.path.exists('quiz_management.db'):
            return 'quiz_management.db'
        else:
            return None
    
    def test_database_structure(self):
        """Test that the database has quizzes and questions for export"""
        print("=== Testing Database Structure ===")
        
        db_path = self.get_database_path()
        if not db_path:
            print("❌ FAILED: Database not found")
            return False
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check for quizzes
            cursor.execute("SELECT COUNT(*) FROM quiz WHERE is_active = 1")
            active_quizzes = cursor.fetchone()[0]
            print(f"✓ Found {active_quizzes} active quizzes")
            
            # Check for questions
            cursor.execute("SELECT COUNT(*) FROM question")
            total_questions = cursor.fetchone()[0]
            print(f"✓ Found {total_questions} total questions")
            
            # Check for teachers
            cursor.execute("SELECT COUNT(*) FROM user WHERE role IN ('teacher', 'admin')")
            teachers_admins = cursor.fetchone()[0]
            print(f"✓ Found {teachers_admins} teachers/admins")
            
            # Get sample quiz data
            cursor.execute("""
                SELECT q.id, q.title, q.teacher_id, COUNT(qu.id) as question_count
                FROM quiz q 
                LEFT JOIN question qu ON q.id = qu.quiz_id 
                WHERE q.is_active = 1 
                GROUP BY q.id 
                LIMIT 3
            """)
            sample_quizzes = cursor.fetchall()
            
            print("✓ Sample quizzes for testing:")
            for quiz_id, title, teacher_id, q_count in sample_quizzes:
                print(f"  - Quiz {quiz_id}: '{title}' ({q_count} questions)")
            
            conn.close()
            return active_quizzes > 0 and total_questions > 0
            
        except Exception as e:
            print(f"❌ FAILED: Database error: {e}")
            return False
    
    def login_as_teacher(self):
        """Login as a teacher to test export functionality"""
        print("\n=== Testing Teacher Login ===")
        
        try:
            # Get login page
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code != 200:
                print(f"❌ FAILED: Could not access login page (status: {response.status_code})")
                return False
            
            # Find a teacher account from database
            db_path = self.get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT email, unhashed_password FROM user WHERE role = 'teacher' LIMIT 1")
            teacher = cursor.fetchone()
            conn.close()
            
            if not teacher:
                print("❌ FAILED: No teacher account found in database")
                return False
            
            email, password = teacher
            
            # Login
            login_data = {
                'email': email,
                'password': password
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            
            if response.status_code == 200 and 'dashboard' in response.url:
                print(f"✓ Successfully logged in as teacher: {email}")
                return True
            else:
                print(f"❌ FAILED: Login failed (status: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"❌ FAILED: Login error: {e}")
            return False
    
    def test_my_quizzes_page_export_buttons(self):
        """Test that export buttons appear on my quizzes page"""
        print("\n=== Testing My Quizzes Page Export Buttons ===")
        
        try:
            response = self.session.get(f"{self.base_url}/my-quizzes")
            
            if response.status_code != 200:
                print(f"❌ FAILED: Could not access my quizzes page (status: {response.status_code})")
                return False
            
            content = response.text
            
            # Check for export buttons
            required_elements = [
                '📦 Export All',
                '📊 Export',
                '/teacher/export-all-quizzes',
                '/teacher/export-quiz/'
            ]
            
            for element in required_elements:
                if element in content:
                    print(f"✓ Found export element: {element}")
                else:
                    print(f"❌ FAILED: Missing export element: {element}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ FAILED: My quizzes page test error: {e}")
            return False
    
    def test_single_quiz_export(self):
        """Test exporting a single quiz as CSV"""
        print("\n=== Testing Single Quiz Export ===")
        
        try:
            # Get a quiz ID from database
            db_path = self.get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT q.id, q.title, COUNT(qu.id) as question_count
                FROM quiz q 
                LEFT JOIN question qu ON q.id = qu.quiz_id 
                WHERE q.is_active = 1 
                GROUP BY q.id 
                HAVING question_count > 0
                LIMIT 1
            """)
            quiz_data = cursor.fetchone()
            conn.close()
            
            if not quiz_data:
                print("❌ FAILED: No quiz with questions found for testing")
                return False
            
            quiz_id, quiz_title, question_count = quiz_data
            print(f"✓ Testing export of quiz {quiz_id}: '{quiz_title}' ({question_count} questions)")
            
            # Test export endpoint
            response = self.session.get(f"{self.base_url}/teacher/export-quiz/{quiz_id}")
            
            if response.status_code != 200:
                print(f"❌ FAILED: Export request failed (status: {response.status_code})")
                return False
            
            # Check response headers
            content_type = response.headers.get('Content-Type', '')
            content_disposition = response.headers.get('Content-Disposition', '')
            
            if 'text/csv' not in content_type:
                print(f"❌ FAILED: Wrong content type: {content_type}")
                return False
            
            if 'attachment' not in content_disposition:
                print(f"❌ FAILED: Missing attachment header: {content_disposition}")
                return False
            
            print("✓ Export response headers are correct")
            
            # Test CSV content
            csv_content = response.text
            
            # Check for required CSV sections
            required_sections = [
                'Quiz Export',
                'Quiz Information',
                'Questions',
                'Summary Statistics'
            ]
            
            for section in required_sections:
                if section in csv_content:
                    print(f"✓ Found CSV section: {section}")
                else:
                    print(f"❌ FAILED: Missing CSV section: {section}")
                    return False
            
            # Parse CSV and validate structure
            csv_reader = csv.reader(io.StringIO(csv_content))
            rows = list(csv_reader)
            
            if len(rows) < 10:  # Should have at least header + quiz info + questions
                print(f"❌ FAILED: CSV too short: {len(rows)} rows")
                return False
            
            print(f"✓ CSV export successful: {len(rows)} rows generated")
            return True
            
        except Exception as e:
            print(f"❌ FAILED: Single quiz export error: {e}")
            return False
    
    def test_bulk_quiz_export(self):
        """Test exporting all quizzes as ZIP file"""
        print("\n=== Testing Bulk Quiz Export ===")
        
        try:
            # Test bulk export endpoint
            response = self.session.get(f"{self.base_url}/teacher/export-all-quizzes")
            
            if response.status_code != 200:
                print(f"❌ FAILED: Bulk export request failed (status: {response.status_code})")
                return False
            
            # Check response headers
            content_type = response.headers.get('Content-Type', '')
            content_disposition = response.headers.get('Content-Disposition', '')
            
            if 'application/zip' not in content_type:
                print(f"❌ FAILED: Wrong content type: {content_type}")
                return False
            
            if 'attachment' not in content_disposition:
                print(f"❌ FAILED: Missing attachment header: {content_disposition}")
                return False
            
            print("✓ Bulk export response headers are correct")
            
            # Test ZIP content
            zip_content = response.content
            
            try:
                zip_file = zipfile.ZipFile(io.BytesIO(zip_content))
                file_list = zip_file.namelist()
                
                print(f"✓ ZIP file contains {len(file_list)} files:")
                
                csv_files = [f for f in file_list if f.endswith('.csv')]
                summary_files = [f for f in file_list if f.endswith('.txt')]
                
                print(f"  - {len(csv_files)} CSV files")
                print(f"  - {len(summary_files)} summary files")
                
                if len(csv_files) == 0:
                    print("❌ FAILED: No CSV files in ZIP")
                    return False
                
                if 'export_summary.txt' not in file_list:
                    print("❌ FAILED: Missing export summary file")
                    return False
                
                # Test reading one CSV file from ZIP
                if csv_files:
                    sample_csv = zip_file.read(csv_files[0]).decode('utf-8')
                    if 'Quiz Export' in sample_csv and 'Questions' in sample_csv:
                        print("✓ Sample CSV file content is valid")
                    else:
                        print("❌ FAILED: Invalid CSV content in ZIP")
                        return False
                
                zip_file.close()
                print("✓ Bulk export successful")
                return True
                
            except zipfile.BadZipFile:
                print("❌ FAILED: Invalid ZIP file received")
                return False
            
        except Exception as e:
            print(f"❌ FAILED: Bulk export error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all quiz export tests"""
        print("🧪 Starting Quiz Export Tests")
        print("=" * 50)
        
        tests = [
            self.test_database_structure,
            self.login_as_teacher,
            self.test_my_quizzes_page_export_buttons,
            self.test_single_quiz_export,
            self.test_bulk_quiz_export
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    print(f"❌ Test failed: {test.__name__}")
            except Exception as e:
                print(f"❌ Test error in {test.__name__}: {e}")
        
        print("\n" + "=" * 50)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Quiz export functionality is working correctly.")
        else:
            print("⚠ Some tests failed. Please check the implementation.")
        
        return passed == total

def main():
    """Main test function"""
    print("Quiz Export Functionality Test Suite")
    print("====================================")
    print()
    
    tester = QuizExportTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All export tests passed!")
        print("The quiz export system is ready to use.")
    else:
        print("\n❌ Some tests failed!")
        print("Please check the Flask application and routes.")

if __name__ == "__main__":
    main()
