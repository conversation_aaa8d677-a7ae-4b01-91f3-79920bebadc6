# 📙 Appendix B – Requirement Gathering

## Structured Interview Transcript
**Date:** October 22, 2024
**Participants:** <PERSON><PERSON><PERSON> (<PERSON>) and Mr. <PERSON> (Client)
**Duration:** 45 minutes
**Format:** Virtual meeting via Zoom

---

**Developer:** Good morning, Mr. <PERSON>. Thank you for taking the time to discuss your quiz management needs in detail. Let's start with your current workflow. What challenges do you face in your current assessment process?

**Client:** Good morning! Well, the biggest issue is time. I teach four sections of 6th-grade math, about 120 students total. Every week, I create quizzes by hand, make copies, distribute them, collect them, and then spend my entire weekend grading. It's exhausting and leaves no time for actual teaching preparation.

**Developer:** That sounds overwhelming. Can you walk me through a typical quiz creation process?

**Client:** Sure. I usually start by writing questions on paper, then typing them up in Word. I have to format everything manually, create answer keys separately, and make sure I have enough copies. If I want to include mathematical expressions or diagrams, I either draw them by hand or struggle with <PERSON>'s equation editor. The whole process takes about 3-4 hours just to create one quiz.

**Developer:** And what about the grading process?

**Client:** That's even worse. I grade each paper individually, calculate scores manually, and then transfer everything to my Excel gradebook. I make arithmetic errors constantly, and sometimes I lose track of which papers I've graded. Students don't get their results for days, which defeats the purpose of formative assessment.

**Developer:** What specific quiz types do you typically use?

**Client:** Mostly multiple choice and true/false questions. I'd love to include more varied question types, but they're harder to grade quickly. I also need to include mathematical expressions and sometimes diagrams or graphs. Oh, and I'd like to randomize question order so students can't easily copy from each other.

**Developer:** Tell me about your reporting needs. What information do you need to track?

**Client:** I need to see individual student progress over time, class averages, and identify which topics students are struggling with most. For parent conferences, I need comprehensive reports showing quiz history, trends, and areas for improvement. Parents are always asking for more frequent updates than just report cards.

**Developer:** What are your expectations for a dashboard interface?

**Client:** I want something clean and simple. I'm comfortable with computers but I'm not a tech expert. The main dashboard should show me my classes, recent quiz results, and maybe some quick statistics. I'd love to see visual charts showing class performance trends. For students, they should be able to see their own progress and upcoming quizzes.

**Developer:** Do you have any preferences for the technical platform?

**Client:** I've heard good things about web-based systems because they work on any computer. Our school uses Windows computers, but I sometimes work from my Mac at home. I don't want to install special software everywhere. Also, it needs to work reliably - I can't afford system crashes during quiz time.

**Developer:** What about parent communication features?

**Client:** This is huge for me. I want parents to automatically get notified when their child completes a quiz, with the score and maybe some feedback. I'd also like a way for parents to message me with questions, and for me to send updates to multiple parents at once.

**Developer:** Are there any specific time constraints or performance requirements?

**Client:** Quiz creation should be much faster than my current 3-4 hours. Ideally under 30 minutes for a 15-question quiz. Students need immediate feedback when they finish - waiting days for results is unacceptable. The system should handle my entire class taking a quiz simultaneously without slowing down.

**Developer:** What about data export and backup needs?

**Client:** I need to export grades to CSV for our school's main gradebook system. Also, I want to be able to download student reports as PDFs for parent conferences. And please, please make sure I can't lose all my data if something goes wrong!

**Developer:** Any thoughts on additional features like practice worksheets?

**Client:** Oh, that would be amazing! If I could generate practice worksheets with different difficulty levels and topics, that would save me even more time. Students could use them for homework or extra practice.

**Developer:** Based on everything we've discussed, let me summarize the 15 key success criteria I'm hearing:

**Client:** Yes, please do.

**Developer:**
1. **Teacher can create a 15-question quiz (MCQ/TF with math expressions/images) in under 30 minutes**
2. **System provides 100% accurate grading, with results shown instantly after submission**
3. **Parents receive quiz score email notifications within 60 seconds of quiz completion**
4. **Dashboard displays class average, individual student data, and topic trends via Chart.js**
5. **Students can view quiz history, performance graphs, and progress trends over time**
6. **Customizable worksheets can be generated by topic and difficulty, with PDF export under 2 minutes**
7. **Admin dashboard supports user approval, search, and role-based access for teachers/students/parents**
8. **Quiz and performance data can be exported in CSV format with one click**
9. **System supports 30+ concurrent users with no load delay beyond 3 seconds**
10. **Edited quizzes are saved as new versions while retaining past attempt data**
11. **Built-in messaging allows email-style communication between teachers, students, and parents**
12. **PDF reports (for parents/teachers) are generated in under 10 seconds via WeasyPrint**
13. **MathJax renders mathematical expressions in all quiz and worksheet views**
14. **Frequently missed questions are flagged, supporting learning gap analysis**
15. **All user data is securely stored with password hashing and session management**

Does this capture your main needs?

**Client:** That's perfect! You really listened to everything I said. This would completely transform how I manage assessments.

---

## Developer's Technology Selection Summary

Based on this detailed requirements gathering, I selected the following technology stack:

**Python Flask Framework:**
- **Rationale:** Mr. Johnson needs a reliable, easy-to-maintain system. Flask's simplicity and extensive documentation make it ideal for educational applications.
- **Benefits:** Rapid development, excellent community support, and straightforward deployment on school infrastructure.

**MySQL Database with SQLAlchemy:**
- **Rationale:** Robust data persistence for student records, quiz attempts, and performance analytics.
- **Benefits:** ACID compliance ensures data integrity, and SQLAlchemy ORM simplifies database operations while preventing SQL injection.

**Web-Based Architecture:**
- **Rationale:** Cross-platform compatibility (Windows/Mac), no software installation required, accessible from any device.
- **Benefits:** Meets Mr. Johnson's need for flexibility and the school's diverse hardware environment.

**Additional Libraries:**
- **MathJax:** For mathematical expression rendering (addresses math content needs)
- **Chart.js:** For visual analytics and progress tracking
- **Flask-Mail:** For automated parent notifications
- **WeasyPrint:** For PDF report generation

This technology selection directly addresses Mr. Johnson's requirements for reliability, ease of use, and comprehensive functionality while staying within the constraints of a school environment.
