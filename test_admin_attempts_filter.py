#!/usr/bin/env python3
"""
Test script to verify admin quiz attempts time filtering functionality
"""

import requests
from datetime import datetime, timedelta
from app import app, db, User, Quiz, QuizAttempt
from werkzeug.security import generate_password_hash

def create_test_attempts():
    """Create test quiz attempts with different dates"""
    print("🔧 Setting up test data for admin attempts filter...")
    
    try:
        with app.app_context():
            # Ensure we have a test admin user
            admin_user = User.query.filter_by(email='<EMAIL>').first()
            if not admin_user:
                admin_user = User(
                    name='Test Admin',
                    email='<EMAIL>',
                    password=generate_password_hash('AdminPass123!'),
                    unhashed_password='AdminPass123!',
                    role='admin',
                    is_verified=True
                )
                db.session.add(admin_user)
                print("✅ Created test admin user")
            
            # Get existing test student and quiz
            test_student = User.query.filter_by(email='<EMAIL>').first()
            test_quiz = Quiz.query.filter_by(title='Test Easy Quiz').first()
            
            if not test_student or not test_quiz:
                print("❌ Test student or quiz not found. Run test_difficulty_filter.py first.")
                return
            
            # Create attempts with different dates
            now = datetime.utcnow()
            test_dates = [
                (now - timedelta(days=1), 85.5),  # Yesterday
                (now - timedelta(days=5), 92.0),  # 5 days ago
                (now - timedelta(days=15), 78.5), # 15 days ago
                (now - timedelta(days=45), 88.0), # 45 days ago (last month)
                (now - timedelta(days=2), 95.0),  # 2 days ago
            ]
            
            # Check if test attempts already exist
            existing_attempts = QuizAttempt.query.filter_by(
                student_id=test_student.id,
                quiz_id=test_quiz.id
            ).count()
            
            if existing_attempts >= len(test_dates):
                print("✅ Test attempts already exist")
                return
            
            # Create test attempts
            for i, (attempt_date, score) in enumerate(test_dates):
                # Check if this specific attempt already exists
                existing = QuizAttempt.query.filter_by(
                    student_id=test_student.id,
                    quiz_id=test_quiz.id
                ).filter(
                    QuizAttempt.submitted_at.between(
                        attempt_date - timedelta(minutes=1),
                        attempt_date + timedelta(minutes=1)
                    )
                ).first()
                
                if not existing:
                    attempt = QuizAttempt(
                        student_id=test_student.id,
                        quiz_id=test_quiz.id,
                        score=score,
                        submitted_at=attempt_date
                    )
                    db.session.add(attempt)
                    print(f"✅ Created test attempt {i+1} ({score}% on {attempt_date.strftime('%Y-%m-%d')})")
            
            db.session.commit()
            print("✅ Test attempts setup complete")
            
    except Exception as e:
        print(f"❌ Error setting up test data: {e}")
        db.session.rollback()

def test_admin_attempts_filter():
    """Test the admin attempts filter functionality"""
    print("\n🧪 Testing Admin Attempts Filter")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # Login as admin
    login_data = {
        'email': '<EMAIL>',
        'password': 'AdminPass123!'
    }
    
    try:
        # Login
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if login_response.status_code != 302 or 'dashboard' not in login_response.headers.get('Location', ''):
            print("❌ Failed to login as admin")
            return
        
        print("✅ Successfully logged in as admin")
        
        # Test different time filters
        test_cases = [
            {'filter': 'All', 'description': 'All attempts'},
            {'filter': 'Last 7 days', 'description': 'Last 7 days'},
            {'filter': 'Last 30 days', 'description': 'Last 30 days'},
            {'filter': 'This Month', 'description': 'This month'},
            {'filter': 'Invalid', 'description': 'Invalid filter (should default to All)'}
        ]
        
        for test_case in test_cases:
            print(f"\n📝 Testing: {test_case['description']}")
            
            # Make request to admin attempts page with time filter
            attempts_url = f"{base_url}/admin/attempts"
            params = {'time_filter': test_case['filter']} if test_case['filter'] != 'All' else {}
            
            response = session.get(attempts_url, params=params)
            
            if response.status_code == 200:
                content = response.text
                
                # Check if filter buttons are present
                if 'filter-btn' in content:
                    print("   ✅ Filter buttons are present")
                else:
                    print("   ❌ Filter buttons are missing")
                
                # Check if the active filter is highlighted
                if test_case['filter'] in ['All', 'Last 7 days', 'Last 30 days', 'This Month']:
                    if 'filter-btn active' in content:
                        print("   ✅ Active filter is highlighted")
                    else:
                        print("   ⚠️  Active filter highlighting not detected")
                
                # Check if attempts count is displayed
                if 'Showing' in content and 'attempt' in content:
                    print("   ✅ Attempts count is displayed")
                else:
                    print("   ⚠️  Attempts count not found")
                
                # Check if table is present (when there are attempts)
                if 'table-responsive' in content or 'empty-state' in content:
                    print("   ✅ Content area is displayed")
                else:
                    print("   ⚠️  Content area not found")
                
                print(f"   ✅ Response received (Status: {response.status_code})")
            else:
                print(f"   ❌ Failed to load attempts page (Status: {response.status_code})")
        
        print("\n✅ Admin attempts filter testing completed")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")

def check_attempts_data():
    """Check what attempts data exists in the database"""
    print("\n📊 Checking Quiz Attempts Data")
    print("=" * 40)
    
    try:
        with app.app_context():
            attempts = QuizAttempt.query.order_by(QuizAttempt.submitted_at.desc()).all()
            
            if not attempts:
                print("❌ No quiz attempts found in database")
                return
            
            print(f"Total attempts: {len(attempts)}")
            print()
            
            # Group by time periods
            now = datetime.utcnow()
            last_7_days = now - timedelta(days=7)
            last_30_days = now - timedelta(days=30)
            this_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            counts = {
                'All': len(attempts),
                'Last 7 days': len([a for a in attempts if a.submitted_at >= last_7_days]),
                'Last 30 days': len([a for a in attempts if a.submitted_at >= last_30_days]),
                'This Month': len([a for a in attempts if a.submitted_at >= this_month])
            }
            
            for period, count in counts.items():
                print(f"{period}: {count} attempt(s)")
            
            print("\nRecent attempts:")
            for attempt in attempts[:5]:  # Show first 5
                print(f"  - {attempt.student.name}: {attempt.score}% on {attempt.submitted_at.strftime('%Y-%m-%d %H:%M')}")
    
    except Exception as e:
        print(f"❌ Error checking attempts data: {e}")

if __name__ == "__main__":
    print("🚀 Admin Attempts Filter Test Suite")
    print("=" * 50)
    
    check_attempts_data()
    create_test_attempts()
    test_admin_attempts_filter()
    
    print("\n💡 Next steps:")
    print("   1. Visit http://127.0.0.1:5000/login")
    print("   2. Login with: <EMAIL> / AdminPass123!")
    print("   3. Go to Admin Dashboard and click 'View Attempts'")
    print("   4. Test the time filter buttons")
    print("   5. Verify the attempts count and filtering accuracy")
