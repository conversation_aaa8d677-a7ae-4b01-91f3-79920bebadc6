{% extends "base.html" %}

{% block title %}{{ worksheet.title }}{% endblock %}

{% block content %}
<div class="container worksheet-container">
    <!-- Worksheet Header -->
    <div class="worksheet-header no-print">
        <div class="header-actions">
            <button onclick="window.print()" class="btn btn-primary">
                🖨️ Print Worksheet
            </button>
            <a href="{{ url_for('download_worksheet_pdf', worksheet_id=worksheet_id) }}" class="btn btn-success">
                📄 Download PDF
            </a>
            <a href="{{ url_for('generate_worksheet') }}" class="btn btn-secondary">
                ← Back to Generator
            </a>
        </div>
    </div>

    <!-- Printable Worksheet -->
    <div class="worksheet-content">
        <!-- Worksheet Title Section -->
        <div class="worksheet-title-section">
            <h1 class="worksheet-title">{{ worksheet.title }}</h1>
            <div class="worksheet-info">
                <div class="info-row">
                    <span class="info-label">Name:</span>
                    <span class="info-line">_________________________________</span>
                    <span class="info-label">Date:</span>
                    <span class="info-line">_______________</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Class:</span>
                    <span class="info-line">_________________________________</span>
                    <span class="info-label">Score:</span>
                    <span class="info-line">_______________</span>
                </div>
            </div>
            <div class="worksheet-instructions">
                <p><strong>Instructions:</strong> Solve each problem carefully. Show your work where applicable.</p>
                <p><strong>Difficulty Level:</strong> 
                    {% if worksheet.difficulty_level == 1 %}Easy{% elif worksheet.difficulty_level == 2 %}Medium{% else %}Hard{% endif %}
                    | <strong>Total Questions:</strong> {{ worksheet.questions|length }}
                </p>
            </div>
        </div>

        <!-- Questions Section -->
        <div class="questions-section">
            {% for question in worksheet.questions %}
            <div class="question-item">
                <div class="question-number">{{ loop.index }}.</div>
                <div class="question-content">
                    <div class="question-text">
                        {{ question.question_text }}
                        
                        <!-- Display image if available -->
                        {% if question.image_filename %}
                        <div class="question-image">
                            <img src="{{ url_for('static', filename='uploads/' + question.image_filename) }}" 
                                 alt="Question Image" class="img-fluid">
                        </div>
                        {% endif %}
                    </div>
                    
                    {% if question.question_type == 'mcq' %}
                    <div class="question-options">
                        {% if question.option1 %}
                        <div class="option">
                            <span class="option-letter">A)</span>
                            <span class="option-text">{{ question.option1 }}</span>
                        </div>
                        {% endif %}
                        {% if question.option2 %}
                        <div class="option">
                            <span class="option-letter">B)</span>
                            <span class="option-text">{{ question.option2 }}</span>
                        </div>
                        {% endif %}
                        {% if question.option3 %}
                        <div class="option">
                            <span class="option-letter">C)</span>
                            <span class="option-text">{{ question.option3 }}</span>
                        </div>
                        {% endif %}
                        {% if question.option4 %}
                        <div class="option">
                            <span class="option-letter">D)</span>
                            <span class="option-text">{{ question.option4 }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="answer-space">
                        <span class="answer-label">Answer:</span>
                        <span class="answer-line">_______</span>
                    </div>
                    {% else %}
                    <!-- For other question types, provide space for written answers -->
                    <div class="answer-space-large">
                        <div class="answer-lines">
                            <div class="answer-line-full">_________________________________________________</div>
                            <div class="answer-line-full">_________________________________________________</div>
                            <div class="answer-line-full">_________________________________________________</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Answer Key Section (if included) -->
        {% if worksheet.include_answers %}
        <div class="answer-key-section page-break">
            <h2 class="answer-key-title">📝 Answer Key</h2>
            <div class="answer-key-grid">
                {% for question in worksheet.questions %}
                <div class="answer-key-item">
                    <span class="answer-key-number">{{ loop.index }}.</span>
                    <span class="answer-key-answer">
                        {% if question.question_type == 'mcq' %}
                            {% if question.correct_answer == '1' %}A) {{ question.option1 }}
                            {% elif question.correct_answer == '2' %}B) {{ question.option2 }}
                            {% elif question.correct_answer == '3' %}C) {{ question.option3 }}
                            {% elif question.correct_answer == '4' %}D) {{ question.option4 }}
                            {% else %}{{ question.correct_answer }}
                            {% endif %}
                        {% else %}
                            {{ question.correct_answer }}
                        {% endif %}
                    </span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="worksheet-footer">
            <div class="footer-info">
                <small>
                    Generated by {{ worksheet.teacher_name }} on {{ worksheet.generated_at.strftime('%B %d, %Y at %I:%M %p') }}
                    | Math Quiz Management System
                </small>
            </div>
        </div>
    </div>
</div>

<style>
/* Print and display styles */
.worksheet-container {
    max-width: 8.5in;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
}

.worksheet-header {
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 2rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.worksheet-content {
    padding: 0 1rem;
}

.worksheet-title-section {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 2px solid #333;
    padding-bottom: 1rem;
}

.worksheet-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 1rem;
}

.worksheet-info {
    margin: 1rem 0;
}

.info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.info-label {
    font-weight: 500;
    margin-right: 0.5rem;
}

.info-line {
    border-bottom: 1px solid #333;
    flex: 1;
    margin: 0 1rem;
    min-width: 200px;
}

.worksheet-instructions {
    text-align: left;
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.questions-section {
    margin: 2rem 0;
}

.question-item {
    display: flex;
    margin-bottom: 2rem;
    page-break-inside: avoid;
}

.question-number {
    font-weight: bold;
    font-size: 1.1rem;
    margin-right: 1rem;
    min-width: 2rem;
    color: #333;
}

.question-content {
    flex: 1;
}

.question-text {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    color: #333;
}

.question-image {
    margin: 1rem 0;
    text-align: center;
}

.question-image img {
    max-width: 100%;
    max-height: 300px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.question-options {
    margin: 1rem 0;
}

.option {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.option-letter {
    font-weight: 500;
    margin-right: 0.5rem;
    min-width: 1.5rem;
}

.option-text {
    flex: 1;
    line-height: 1.4;
}

.answer-space {
    margin-top: 1rem;
    display: flex;
    align-items: center;
}

.answer-label {
    font-weight: 500;
    margin-right: 0.5rem;
}

.answer-line {
    border-bottom: 1px solid #333;
    min-width: 100px;
}

.answer-space-large {
    margin-top: 1rem;
}

.answer-lines {
    margin-top: 0.5rem;
}

.answer-line-full {
    border-bottom: 1px solid #333;
    margin-bottom: 0.5rem;
    height: 1.5rem;
}

.answer-key-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #333;
}

.answer-key-title {
    text-align: center;
    color: #333;
    margin-bottom: 1.5rem;
}

.answer-key-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 0.5rem;
}

.answer-key-item {
    display: flex;
    align-items: flex-start;
    padding: 0.25rem 0;
}

.answer-key-number {
    font-weight: bold;
    margin-right: 0.5rem;
    min-width: 2rem;
}

.answer-key-answer {
    flex: 1;
    font-size: 0.9rem;
}

.worksheet-footer {
    margin-top: 3rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.footer-info {
    color: #6c757d;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .worksheet-container {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    
    .worksheet-content {
        padding: 0;
    }
    
    .page-break {
        page-break-before: always;
    }
    
    .question-item {
        page-break-inside: avoid;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .worksheet-title {
        font-size: 18pt;
    }
    
    .question-text {
        font-size: 11pt;
    }
    
    .answer-key-item {
        font-size: 10pt;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-line {
        margin: 0.25rem 0;
        min-width: 100%;
    }
    
    .answer-key-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Initialize MathJax rendering for math expressions
document.addEventListener('DOMContentLoaded', function() {
    if (window.MathJax) {
        MathJax.typesetPromise().then(() => {
            console.log('MathJax rendering complete for worksheet');
        }).catch((err) => {
            console.log('MathJax error:', err.message);
        });
    }
});
</script>
{% endblock %}
