{% extends "base.html" %}

{% block title %}My Worksheets{% endblock %}

{% block content %}
<div class="container my-worksheets-container">
    <div class="page-header">
        <h1>📋 My Worksheets</h1>
        <p>View and manage your generated worksheets</p>
        <div class="header-actions">
            <a href="{{ url_for('generate_worksheet') }}" class="btn btn-primary">
                ➕ Generate New Worksheet
            </a>
            <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-secondary">
                🏠 Back to Dashboard
            </a>
        </div>
    </div>

    {% if worksheets %}
    <div class="worksheets-grid">
        {% for worksheet in worksheets %}
        <div class="worksheet-card">
            <div class="worksheet-card-header">
                <h3 class="worksheet-title">{{ worksheet.title }}</h3>
                <div class="worksheet-meta">
                    <span class="meta-item">
                        <i class="icon">📅</i>
                        {{ worksheet.generated_at.strftime('%Y-%m-%d %H:%M') }}
                    </span>
                    <span class="meta-item">
                        <i class="icon">📊</i>
                        Level {{ worksheet.difficulty_level }}
                    </span>
                </div>
            </div>
            
            <div class="worksheet-card-body">
                <div class="worksheet-stats">
                    <div class="stat-item">
                        <span class="stat-label">Questions:</span>
                        <span class="stat-value">{{ worksheet.question_ids|length if worksheet.question_ids else 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Downloads:</span>
                        <span class="stat-value">{{ worksheet.download_count }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Answer Key:</span>
                        <span class="stat-value">
                            {% if worksheet.include_answers %}
                                <span class="badge badge-success">Yes</span>
                            {% else %}
                                <span class="badge badge-secondary">No</span>
                            {% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="worksheet-difficulty">
                    <span class="difficulty-label">Difficulty:</span>
                    <span class="difficulty-badge difficulty-{{ worksheet.difficulty_level }}">
                        {% if worksheet.difficulty_level == 1 %}Easy
                        {% elif worksheet.difficulty_level == 2 %}Medium
                        {% else %}Hard
                        {% endif %}
                    </span>
                </div>
            </div>
            
            <div class="worksheet-card-footer">
                <div class="worksheet-actions">
                    <a href="{{ url_for('download_worksheet_pdf', worksheet_id=worksheet.id) }}" 
                       class="btn btn-sm btn-success" title="Download PDF">
                        📄 PDF
                    </a>
                    <button type="button" class="btn btn-sm btn-info" 
                            onclick="viewWorksheetDetails({{ worksheet.id }})" title="View Details">
                        👁️ View
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" 
                            onclick="regenerateWorksheet({{ worksheet.id }})" title="Regenerate">
                        🔄 Regenerate
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" 
                            onclick="deleteWorksheet({{ worksheet.id }})" title="Delete">
                        🗑️ Delete
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination (if needed in the future) -->
    <div class="pagination-section">
        <p class="results-info">
            Showing {{ worksheets|length }} worksheet{{ 's' if worksheets|length != 1 else '' }}
        </p>
    </div>

    {% else %}
    <div class="empty-state">
        <div class="empty-state-icon">📋</div>
        <h3>No Worksheets Yet</h3>
        <p>You haven't generated any worksheets yet. Create your first worksheet to get started!</p>
        <a href="{{ url_for('generate_worksheet') }}" class="btn btn-primary btn-lg">
            ➕ Generate Your First Worksheet
        </a>
    </div>
    {% endif %}
</div>

<!-- Worksheet Details Modal -->
<div class="modal fade" id="worksheetDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Worksheet Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="worksheetDetailsContent">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewWorksheetDetails(worksheetId) {
    // For now, just show basic info
    // In a full implementation, you'd fetch detailed info via AJAX
    const modal = document.getElementById('worksheetDetailsModal');
    const content = document.getElementById('worksheetDetailsContent');
    
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p>Loading worksheet details...</p>
        </div>
    `;
    
    // Show modal (using Bootstrap 4 syntax)
    $('#worksheetDetailsModal').modal('show');
    
    // Simulate loading (replace with actual AJAX call)
    setTimeout(() => {
        content.innerHTML = `
            <div class="alert alert-info">
                <h6>Worksheet ID: ${worksheetId}</h6>
                <p>Detailed view functionality would be implemented here.</p>
                <p>This could include:</p>
                <ul>
                    <li>Preview of all questions</li>
                    <li>Usage statistics</li>
                    <li>Student performance data (if available)</li>
                    <li>Edit options</li>
                </ul>
            </div>
        `;
    }, 1000);
}

function regenerateWorksheet(worksheetId) {
    if (confirm('This will create a new worksheet with the same settings but different questions. Continue?')) {
        // Redirect to generator with pre-filled settings
        // In a full implementation, you'd load the worksheet settings
        window.location.href = '/generate_worksheet?regenerate=' + worksheetId;
    }
}

function deleteWorksheet(worksheetId) {
    if (confirm('Are you sure you want to delete this worksheet? This action cannot be undone.')) {
        // In a full implementation, you'd make an AJAX call to delete
        fetch(`/delete_worksheet/${worksheetId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting worksheet: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error deleting worksheet: ' + error.message);
        });
    }
}
</script>

<style>
.my-worksheets-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    color: #007bff;
    margin-bottom: 0.5rem;
}

.header-actions {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.worksheets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.worksheet-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.worksheet-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.worksheet-card-header {
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid #e9ecef;
}

.worksheet-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.worksheet-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.worksheet-card-body {
    padding: 1rem 1.5rem;
}

.worksheet-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-top: 0.25rem;
}

.worksheet-difficulty {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.difficulty-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.difficulty-1 {
    background-color: #d4edda;
    color: #155724;
}

.difficulty-2 {
    background-color: #fff3cd;
    color: #856404;
}

.difficulty-3 {
    background-color: #f8d7da;
    color: #721c24;
}

.worksheet-card-footer {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.worksheet-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-secondary {
    background-color: #6c757d;
    color: white;
}

.pagination-section {
    text-align: center;
    padding: 2rem 0;
    border-top: 1px solid #e9ecef;
}

.results-info {
    color: #6c757d;
    font-size: 0.875rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #333;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .worksheets-grid {
        grid-template-columns: 1fr;
    }
    
    .header-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .worksheet-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .worksheet-actions {
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}
