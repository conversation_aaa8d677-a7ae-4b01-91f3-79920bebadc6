#!/usr/bin/env python3
"""
Database migration script to add quiz versioning columns
"""

from app import app, db, Quiz
import sqlite3
import os

def migrate_database():
    """Add versioning columns to existing Quiz table"""
    print("🔧 Starting quiz versioning migration...")

    try:
        # Use direct SQLite connection to avoid SQLAlchemy model conflicts
        db_path = os.path.join(app.instance_path, 'quiz_management.db')

        if not os.path.exists(db_path):
            print("❌ Database file not found, creating new database with versioning...")
            with app.app_context():
                db.create_all()
            return

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if columns already exist
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [row[1] for row in cursor.fetchall()]

        if 'version' in columns:
            print("✅ Versioning columns already exist, skipping migration")
            conn.close()
            return

        print("📊 Adding versioning columns to Quiz table...")

        # Add new columns using raw SQL
        cursor.execute('ALTER TABLE quiz ADD COLUMN version INTEGER DEFAULT 1 NOT NULL')
        cursor.execute('ALTER TABLE quiz ADD COLUMN is_active BOOLEAN DEFAULT 1 NOT NULL')
        cursor.execute('ALTER TABLE quiz ADD COLUMN original_quiz_id INTEGER')

        # Update existing quizzes to have proper default values
        cursor.execute('UPDATE quiz SET version = 1, is_active = 1, original_quiz_id = NULL')

        conn.commit()
        conn.close()

        print("✅ Successfully added versioning columns and updated existing data")

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        raise

def verify_migration():
    """Verify the migration was successful"""
    print("\n🧪 Verifying migration...")
    
    try:
        with app.app_context():
            # Check if we can query with new columns
            quizzes = Quiz.query.filter_by(is_active=True).all()
            print(f"✅ Found {len(quizzes)} active quizzes")
            
            for quiz in quizzes:
                print(f"  - {quiz.title}: v{quiz.version}, active={quiz.is_active}")
                
            print("✅ Migration verification successful")
            
    except Exception as e:
        print(f"❌ Migration verification failed: {e}")

if __name__ == "__main__":
    print("🚀 Quiz Versioning Migration")
    print("=" * 40)
    
    migrate_database()
    verify_migration()
    
    print("\n💡 Migration complete!")
    print("   - All existing quizzes are now version 1 and active")
    print("   - Quiz versioning system is ready to use")
    print("   - Edit operations will now create new versions instead of modifying originals")
