#!/usr/bin/env python3
"""
Database migration script to add randomization fields
Adds:
1. randomize_questions field to Quiz table
2. question_order field to QuizAttempt table
"""

import sqlite3
import os

def migrate_database():
    """Add randomization fields to existing database"""
    
    # Database path
    db_path = os.path.join('instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Starting randomization migration...")
        
        # Check if randomize_questions column already exists in quiz table
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'randomize_questions' not in columns:
            print("➕ Adding randomize_questions column to quiz table...")
            cursor.execute("""
                ALTER TABLE quiz 
                ADD COLUMN randomize_questions BOOLEAN DEFAULT 1 NOT NULL
            """)
            print("✅ Added randomize_questions column")
        else:
            print("ℹ️  randomize_questions column already exists")
        
        # Check if question_order column already exists in quiz_attempt table
        cursor.execute("PRAGMA table_info(quiz_attempt)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'question_order' not in columns:
            print("➕ Adding question_order column to quiz_attempt table...")
            cursor.execute("""
                ALTER TABLE quiz_attempt 
                ADD COLUMN question_order TEXT
            """)
            print("✅ Added question_order column")
        else:
            print("ℹ️  question_order column already exists")
        
        # Update existing quizzes to have randomization enabled by default
        cursor.execute("""
            UPDATE quiz 
            SET randomize_questions = 1 
            WHERE randomize_questions IS NULL
        """)
        
        # Commit changes
        conn.commit()
        
        # Verify the changes
        print("\n🔍 Verifying migration...")
        
        # Check quiz table structure
        cursor.execute("PRAGMA table_info(quiz)")
        quiz_columns = [column[1] for column in cursor.fetchall()]
        if 'randomize_questions' in quiz_columns:
            print("✅ Quiz table: randomize_questions column verified")
        else:
            print("❌ Quiz table: randomize_questions column missing")
        
        # Check quiz_attempt table structure
        cursor.execute("PRAGMA table_info(quiz_attempt)")
        attempt_columns = [column[1] for column in cursor.fetchall()]
        if 'question_order' in attempt_columns:
            print("✅ QuizAttempt table: question_order column verified")
        else:
            print("❌ QuizAttempt table: question_order column missing")
        
        # Check how many quizzes have randomization enabled
        cursor.execute("SELECT COUNT(*) FROM quiz WHERE randomize_questions = 1")
        randomized_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM quiz")
        total_count = cursor.fetchone()[0]
        
        print(f"📊 {randomized_count}/{total_count} quizzes have randomization enabled")
        
        conn.close()
        
        print("\n🎉 Migration completed successfully!")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error during migration: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return False
    except Exception as e:
        print(f"❌ Unexpected error during migration: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return False

def verify_migration():
    """Verify that the migration was successful"""
    db_path = os.path.join('instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Verifying randomization migration...")
        
        # Test that we can query the new columns
        cursor.execute("""
            SELECT id, title, randomize_questions 
            FROM quiz 
            LIMIT 3
        """)
        quizzes = cursor.fetchall()
        
        print("📋 Sample quizzes with randomization settings:")
        for quiz_id, title, randomize in quizzes:
            status = "✅ Enabled" if randomize else "❌ Disabled"
            print(f"  - Quiz {quiz_id}: {title[:30]}... - {status}")
        
        # Test quiz_attempt table
        cursor.execute("""
            SELECT id, quiz_id, question_order 
            FROM quiz_attempt 
            LIMIT 3
        """)
        attempts = cursor.fetchall()
        
        print("\n📋 Sample quiz attempts with question order:")
        for attempt_id, quiz_id, order in attempts:
            order_status = "📝 Has order" if order else "⭕ No order"
            print(f"  - Attempt {attempt_id} (Quiz {quiz_id}): {order_status}")
        
        conn.close()
        print("\n✅ Migration verification completed!")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error during verification: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Quiz Randomization Migration")
    print("=" * 50)
    
    success = migrate_database()
    
    if success:
        print("\n" + "=" * 50)
        verify_migration()
    else:
        print("\n❌ Migration failed!")
