#!/usr/bin/env python3
"""
Database Migration Script for Parent Comments Feature
Adds the parent_comment table to support parent feedback on quiz attempts.
"""

import sqlite3
import os
from datetime import datetime

def get_database_path():
    """Get the path to the database file."""
    # Check for database in instance folder first
    instance_path = os.path.join(os.getcwd(), 'instance')
    if os.path.exists(instance_path):
        db_files = [f for f in os.listdir(instance_path) if f.endswith('.db')]
        if db_files:
            return os.path.join(instance_path, db_files[0])
    
    # Fallback to current directory
    db_files = [f for f in os.listdir('.') if f.endswith('.db')]
    if db_files:
        return db_files[0]
    
    raise FileNotFoundError("No database file found. Please ensure the Flask app has been run at least once.")

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database."""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None

def create_parent_comment_table(cursor):
    """Create the parent_comment table."""
    cursor.execute("""
        CREATE TABLE parent_comment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            quiz_id INTEGER NOT NULL,
            attempt_id INTEGER NOT NULL,
            parent_id INTEGER NOT NULL,
            comment_text TEXT NOT NULL,
            timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES user (id),
            FOREIGN KEY (quiz_id) REFERENCES quiz (id),
            FOREIGN KEY (attempt_id) REFERENCES quiz_attempt (id),
            FOREIGN KEY (parent_id) REFERENCES user (id)
        )
    """)
    print("✓ Created parent_comment table")

def create_indexes(cursor):
    """Create indexes for better query performance."""
    indexes = [
        ("idx_parent_comment_student", "parent_comment", "student_id"),
        ("idx_parent_comment_quiz", "parent_comment", "quiz_id"),
        ("idx_parent_comment_attempt", "parent_comment", "attempt_id"),
        ("idx_parent_comment_parent", "parent_comment", "parent_id"),
        ("idx_parent_comment_timestamp", "parent_comment", "timestamp")
    ]
    
    for index_name, table_name, column_name in indexes:
        cursor.execute(f"""
            CREATE INDEX IF NOT EXISTS {index_name} 
            ON {table_name} ({column_name})
        """)
        print(f"✓ Created index {index_name}")

def verify_migration(cursor):
    """Verify the migration was successful."""
    # Check table exists
    if not check_table_exists(cursor, 'parent_comment'):
        raise Exception("parent_comment table was not created successfully")
    
    # Check table structure
    cursor.execute("PRAGMA table_info(parent_comment)")
    columns = cursor.fetchall()
    expected_columns = {
        'id', 'student_id', 'quiz_id', 'attempt_id', 
        'parent_id', 'comment_text', 'timestamp'
    }
    actual_columns = {col[1] for col in columns}
    
    if not expected_columns.issubset(actual_columns):
        missing = expected_columns - actual_columns
        raise Exception(f"Missing columns in parent_comment table: {missing}")
    
    print("✓ Migration verification successful")
    print(f"✓ parent_comment table has {len(columns)} columns: {', '.join(actual_columns)}")

def rollback_migration(cursor):
    """Rollback the migration by dropping the parent_comment table."""
    cursor.execute("DROP TABLE IF EXISTS parent_comment")
    print("✓ Rolled back migration - dropped parent_comment table")

def main():
    """Main migration function."""
    print("=== Parent Comments Migration Script ===")
    print(f"Started at: {datetime.now()}")
    
    try:
        # Get database path
        db_path = get_database_path()
        print(f"Using database: {db_path}")
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if migration already applied
        if check_table_exists(cursor, 'parent_comment'):
            print("⚠ parent_comment table already exists. Migration may have been applied already.")
            response = input("Do you want to continue anyway? (y/N): ").lower()
            if response != 'y':
                print("Migration cancelled.")
                return
        
        print("\n--- Applying Migration ---")
        
        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        try:
            # Create parent_comment table
            create_parent_comment_table(cursor)
            
            # Create indexes
            create_indexes(cursor)
            
            # Verify migration
            verify_migration(cursor)
            
            # Commit transaction
            conn.commit()
            print("\n✅ Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            conn.rollback()
            print(f"\n❌ Migration failed: {e}")
            print("Transaction rolled back.")
            raise
            
    except Exception as e:
        print(f"\n❌ Migration error: {e}")
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()
    
    print(f"Completed at: {datetime.now()}")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
