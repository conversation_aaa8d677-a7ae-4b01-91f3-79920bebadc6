#!/usr/bin/env python3
"""
Database Migration Script for Student Comments Feature
Adds student_comment table to support student reflections on quizzes.
"""

import sqlite3
import os
from datetime import datetime

def get_database_path():
    """Get the path to the database file."""
    # Check for database in instance folder first
    instance_path = os.path.join(os.getcwd(), 'instance')
    if os.path.exists(instance_path):
        db_files = [f for f in os.listdir(instance_path) if f.endswith('.db')]
        if db_files:
            return os.path.join(instance_path, db_files[0])
    
    # Fallback to current directory
    db_files = [f for f in os.listdir('.') if f.endswith('.db')]
    if db_files:
        return db_files[0]
    
    raise FileNotFoundError("No database file found.")

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database."""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None

def create_student_comment_table(cursor):
    """Create the student_comment table."""
    print("Creating student_comment table...")
    
    cursor.execute("""
        CREATE TABLE student_comment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            quiz_id INTEGER NOT NULL,
            attempt_id INTEGER,
            comment_text TEXT NOT NULL,
            timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES user (id) ON DELETE CASCADE,
            FOREIGN KEY (quiz_id) REFERENCES quiz (id) ON DELETE CASCADE,
            FOREIGN KEY (attempt_id) REFERENCES quiz_attempt (id) ON DELETE SET NULL
        )
    """)
    
    print("✅ student_comment table created successfully")

def create_indexes(cursor):
    """Create indexes for better query performance."""
    print("Creating indexes...")
    
    # Index for finding comments by student
    cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_student_comment_student_id 
        ON student_comment(student_id)
    """)
    
    # Index for finding comments by quiz
    cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_student_comment_quiz_id 
        ON student_comment(quiz_id)
    """)
    
    # Index for finding comments by attempt
    cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_student_comment_attempt_id 
        ON student_comment(attempt_id)
    """)
    
    # Index for ordering by timestamp
    cursor.execute("""
        CREATE INDEX IF NOT EXISTS idx_student_comment_timestamp 
        ON student_comment(timestamp)
    """)
    
    print("✅ Indexes created successfully")

def verify_migration(cursor):
    """Verify the migration was successful."""
    print("Verifying migration...")
    
    # Check if table exists
    if not check_table_exists(cursor, 'student_comment'):
        raise Exception("student_comment table was not created")
    
    # Check table structure
    cursor.execute("PRAGMA table_info(student_comment)")
    columns = cursor.fetchall()
    column_names = {col[1] for col in columns}
    
    expected_columns = {
        'id', 'student_id', 'quiz_id', 'attempt_id', 
        'comment_text', 'timestamp'
    }
    
    if not expected_columns.issubset(column_names):
        missing = expected_columns - column_names
        raise Exception(f"Missing columns: {missing}")
    
    # Check foreign key constraints
    cursor.execute("PRAGMA foreign_key_list(student_comment)")
    foreign_keys = cursor.fetchall()
    
    expected_fks = {
        'student_id': 'user',
        'quiz_id': 'quiz',
        'attempt_id': 'quiz_attempt'
    }
    
    actual_fks = {fk[3]: fk[2] for fk in foreign_keys}
    
    for column, table in expected_fks.items():
        if column not in actual_fks:
            raise Exception(f"Missing foreign key for {column}")
        if actual_fks[column] != table:
            raise Exception(f"Wrong foreign key table for {column}: expected {table}, got {actual_fks[column]}")
    
    # Check indexes
    cursor.execute("PRAGMA index_list(student_comment)")
    indexes = cursor.fetchall()
    index_names = {idx[1] for idx in indexes}
    
    expected_indexes = {
        'idx_student_comment_student_id',
        'idx_student_comment_quiz_id',
        'idx_student_comment_attempt_id',
        'idx_student_comment_timestamp'
    }
    
    if not expected_indexes.issubset(index_names):
        missing_indexes = expected_indexes - index_names
        print(f"⚠ Warning: Some indexes missing: {missing_indexes}")
    
    print("✅ Migration verification successful")

def main():
    """Run the migration."""
    print("=== Student Comments Database Migration ===")
    print(f"Started at: {datetime.now()}")
    
    try:
        # Get database path
        db_path = get_database_path()
        print(f"Database path: {db_path}")
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Check if migration is needed
        if check_table_exists(cursor, 'student_comment'):
            print("⚠ student_comment table already exists. Migration may have already been run.")
            
            # Verify existing table structure
            try:
                verify_migration(cursor)
                print("✅ Existing table structure is correct.")
                return True
            except Exception as e:
                print(f"❌ Existing table has issues: {e}")
                return False
        
        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        try:
            # Create table
            create_student_comment_table(cursor)
            
            # Create indexes
            create_indexes(cursor)
            
            # Verify migration
            verify_migration(cursor)
            
            # Commit transaction
            conn.commit()
            print("✅ Migration completed successfully")
            
        except Exception as e:
            # Rollback on error
            conn.rollback()
            print(f"❌ Migration failed: {e}")
            raise
        
        return True
        
    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()
        print(f"Completed at: {datetime.now()}")

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
    else:
        print("\n🎉 Student comments migration completed successfully!")
        print("You can now run the Flask application to use the new student comment features.")
