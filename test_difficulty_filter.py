#!/usr/bin/env python3
"""
Test script to verify difficulty filter functionality on student dashboard
"""

import requests
from app import app, db, User, Quiz
from werkzeug.security import generate_password_hash

def create_test_data():
    """Create test data with quizzes of different difficulties"""
    print("🔧 Setting up test data...")
    
    try:
        with app.app_context():
            # Check if test student exists
            test_student = User.query.filter_by(email='<EMAIL>').first()
            
            if not test_student:
                # Create test student
                test_student = User(
                    name='Test Student',
                    email='<EMAIL>',
                    password=generate_password_hash('TestPass123!'),
                    unhashed_password='TestPass123!',
                    role='student',
                    parent_email='<EMAIL>',
                    is_verified=True
                )
                db.session.add(test_student)
                print("✅ Created test student")
            else:
                print("✅ Test student already exists")
            
            # Create test quizzes with different difficulties if they don't exist
            difficulties = ['Easy', 'Medium', 'Hard']
            
            for difficulty in difficulties:
                existing_quiz = Quiz.query.filter_by(
                    title=f'Test {difficulty} Quiz',
                    difficulty=difficulty
                ).first()
                
                if not existing_quiz:
                    test_quiz = Quiz(
                        title=f'Test {difficulty} Quiz',
                        description=f'A test quiz with {difficulty.lower()} difficulty',
                        time_limit=30,
                        total_marks=100,
                        grade_a_threshold=90,
                        grade_b_threshold=80,
                        grade_c_threshold=70,
                        grade_d_threshold=60,
                        difficulty=difficulty,
                        teacher_id=5  # Assuming teacher ID 5 exists
                    )
                    db.session.add(test_quiz)
                    print(f"✅ Created {difficulty} quiz")
                else:
                    print(f"✅ {difficulty} quiz already exists")
            
            db.session.commit()
            print("✅ Test data setup complete")
            
    except Exception as e:
        print(f"❌ Error setting up test data: {e}")
        db.session.rollback()

def test_difficulty_filter():
    """Test the difficulty filter functionality"""
    print("\n🧪 Testing Difficulty Filter")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test login as student first
    login_data = {
        'email': '<EMAIL>',
        'password': 'TestPass123!'
    }
    
    session = requests.Session()
    
    try:
        # Login
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if login_response.status_code != 302 or 'dashboard' not in login_response.headers.get('Location', ''):
            print("❌ Failed to login as test student")
            return
        
        print("✅ Successfully logged in as test student")
        
        # Test different difficulty filters
        test_cases = [
            {'difficulty': 'All', 'description': 'All quizzes'},
            {'difficulty': 'Easy', 'description': 'Easy quizzes only'},
            {'difficulty': 'Medium', 'description': 'Medium quizzes only'},
            {'difficulty': 'Hard', 'description': 'Hard quizzes only'},
            {'difficulty': 'Invalid', 'description': 'Invalid filter (should default to All)'}
        ]
        
        for test_case in test_cases:
            print(f"\n📝 Testing: {test_case['description']}")
            
            # Make request to student dashboard with difficulty filter
            dashboard_url = f"{base_url}/student/dashboard"
            params = {'difficulty': test_case['difficulty']} if test_case['difficulty'] != 'All' else {}
            
            response = session.get(dashboard_url, params=params)
            
            if response.status_code == 200:
                # Check if the response contains expected content
                content = response.text
                
                # Check if filter buttons are present
                if 'filter-btn' in content:
                    print("   ✅ Filter buttons are present")
                else:
                    print("   ❌ Filter buttons are missing")
                
                # Check if the active filter is highlighted
                if test_case['difficulty'] in ['All', 'Easy', 'Medium', 'Hard']:
                    expected_active = f'filter-btn active'
                    if expected_active in content:
                        print("   ✅ Active filter is highlighted")
                    else:
                        print("   ⚠️  Active filter highlighting not detected")
                
                # Check if quizzes are displayed
                if 'quiz-card' in content:
                    print("   ✅ Quiz cards are displayed")
                else:
                    print("   ⚠️  No quiz cards found")
                
                print(f"   ✅ Response received (Status: {response.status_code})")
            else:
                print(f"   ❌ Failed to load dashboard (Status: {response.status_code})")
        
        print("\n✅ Difficulty filter testing completed")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")

def check_quiz_data():
    """Check what quiz data exists in the database"""
    print("\n📊 Checking Quiz Data")
    print("=" * 30)
    
    try:
        with app.app_context():
            quizzes = Quiz.query.all()
            
            if not quizzes:
                print("❌ No quizzes found in database")
                return
            
            print(f"Total quizzes: {len(quizzes)}")
            print()
            
            # Group by difficulty
            difficulty_counts = {}
            for quiz in quizzes:
                difficulty = quiz.difficulty
                if difficulty not in difficulty_counts:
                    difficulty_counts[difficulty] = []
                difficulty_counts[difficulty].append(quiz.title)
            
            for difficulty, quiz_titles in difficulty_counts.items():
                print(f"{difficulty}: {len(quiz_titles)} quiz(s)")
                for title in quiz_titles:
                    print(f"  - {title}")
                print()
    
    except Exception as e:
        print(f"❌ Error checking quiz data: {e}")

if __name__ == "__main__":
    print("🚀 Difficulty Filter Test Suite")
    print("=" * 40)
    
    check_quiz_data()
    create_test_data()
    test_difficulty_filter()
    
    print("\n💡 Next steps:")
    print("   1. Visit http://127.0.0.1:5000/login")
    print("   2. Login with: <EMAIL> / TestPass123!")
    print("   3. Test the difficulty filter buttons on the student dashboard")
