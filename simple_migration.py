#!/usr/bin/env python3
"""
Simple database migration script to add quiz versioning columns
"""

import sqlite3
import os

def migrate_database():
    """Add versioning columns to existing Quiz table"""
    print("🔧 Starting quiz versioning migration...")
    
    # Database path
    db_path = os.path.join('instance', 'quiz.db')
    
    if not os.path.exists(db_path):
        print("❌ Database file not found at:", db_path)
        print("   Please ensure the Flask app has been run at least once to create the database")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'version' in columns:
            print("✅ Versioning columns already exist, skipping migration")
            conn.close()
            return True
        
        print("📊 Adding versioning columns to Quiz table...")
        
        # Add new columns using raw SQL
        cursor.execute('ALTER TABLE quiz ADD COLUMN version INTEGER DEFAULT 1 NOT NULL')
        cursor.execute('ALTER TABLE quiz ADD COLUMN is_active BOOLEAN DEFAULT 1 NOT NULL') 
        cursor.execute('ALTER TABLE quiz ADD COLUMN original_quiz_id INTEGER')
        
        # Update existing quizzes to have proper default values
        cursor.execute('UPDATE quiz SET version = 1, is_active = 1, original_quiz_id = NULL')
        
        conn.commit()
        
        # Verify the migration
        cursor.execute("SELECT COUNT(*) FROM quiz WHERE version = 1 AND is_active = 1")
        count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ Successfully added versioning columns and updated {count} existing quizzes")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def verify_migration():
    """Verify the migration was successful"""
    print("\n🧪 Verifying migration...")
    
    db_path = os.path.join('instance', 'quiz.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(quiz)")
        columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = ['version', 'is_active', 'original_quiz_id']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        
        # Check data
        cursor.execute("SELECT id, title, version, is_active FROM quiz LIMIT 5")
        quizzes = cursor.fetchall()
        
        print(f"✅ Found {len(quizzes)} quizzes with versioning data:")
        for quiz in quizzes:
            print(f"  - ID {quiz[0]}: {quiz[1]} (v{quiz[2]}, active={quiz[3]})")
        
        conn.close()
        print("✅ Migration verification successful")
        return True
        
    except Exception as e:
        print(f"❌ Migration verification failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Quiz Versioning Migration")
    print("=" * 40)
    
    success = migrate_database()
    if success:
        verify_migration()
        print("\n💡 Migration complete!")
        print("   - All existing quizzes are now version 1 and active")
        print("   - Quiz versioning system is ready to use")
        print("   - Edit operations will now create new versions instead of modifying originals")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
