<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graph Tool Demo</title>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/jsxgraph@1.7.0/distrib/jsxgraph.css" />
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/jsxgraph@1.7.0/distrib/jsxgraphcore.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .demo-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .demo-btn.active {
            background: #007bff;
            color: white;
        }
        .demo-btn:hover {
            background: #e9ecef;
        }
        .demo-btn.active:hover {
            background: #0056b3;
        }
        .graph-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            height: 500px;
            margin-bottom: 20px;
        }
        .instructions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .equation-input {
            margin: 10px 0;
        }
        .equation-input input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
            margin-right: 10px;
        }
        .coordinate-display {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>📊 Graph Tool Demo</h1>
            <p>Interactive graphing tool for mathematical visualization</p>
        </div>
        
        <div class="instructions">
            <h3>How to Use:</h3>
            <ul>
                <li><strong>Point Tool:</strong> Click anywhere on the graph to place points</li>
                <li><strong>Line Tool:</strong> Click two points to draw a line between them</li>
                <li><strong>Function Tool:</strong> Enter an equation and click Plot to graph functions</li>
                <li><strong>Navigation:</strong> Drag to pan, scroll to zoom</li>
            </ul>
        </div>
        
        <div class="demo-controls">
            <button class="demo-btn active" onclick="setTool('point')" id="tool-point">● Point</button>
            <button class="demo-btn" onclick="setTool('line')" id="tool-line">─ Line</button>
            <button class="demo-btn" onclick="setTool('function')" id="tool-function">f(x) Function</button>
            <button class="demo-btn" onclick="clearGraph()">Clear All</button>
            <button class="demo-btn" onclick="resetView()">Reset View</button>
            <button class="demo-btn" onclick="toggleGrid()">Toggle Grid</button>
        </div>
        
        <div class="equation-input" id="function-input" style="display: none;">
            <label>Equation (y = ):</label>
            <input type="text" id="equation-input" placeholder="e.g., x^2, 2*x + 3, sin(x)" />
            <button class="demo-btn" onclick="plotFunction()">Plot</button>
        </div>
        
        <div class="graph-container">
            <div id="graph-board" style="width: 100%; height: 100%;"></div>
        </div>
        
        <div class="coordinate-display">
            <strong>Coordinates:</strong> <span id="coordinate-info">Move mouse over graph to see coordinates</span>
        </div>
        
        <div class="instructions">
            <h3>Example Equations to Try:</h3>
            <ul>
                <li><code>x^2</code> - Parabola</li>
                <li><code>2*x + 3</code> - Linear function</li>
                <li><code>sin(x)</code> - Sine wave</li>
                <li><code>cos(x)</code> - Cosine wave</li>
                <li><code>x^3 - 3*x</code> - Cubic function</li>
                <li><code>sqrt(x)</code> - Square root function</li>
                <li><code>1/x</code> - Hyperbola</li>
            </ul>
        </div>
    </div>

    <script>
        let graphBoard = null;
        let currentTool = 'point';
        let graphElements = [];
        let linePoints = [];
        let isGridVisible = true;

        // Initialize the graph
        function initGraph() {
            graphBoard = JXG.JSXGraph.initBoard('graph-board', {
                boundingbox: [-10, 10, 10, -10],
                axis: true,
                grid: true,
                showCopyright: false,
                showNavigation: true,
                zoom: {
                    factorX: 1.25,
                    factorY: 1.25,
                    wheel: true,
                    needshift: false
                },
                pan: {
                    enabled: true,
                    needshift: false
                }
            });

            // Add event listeners
            graphBoard.on('down', function(e) {
                if (currentTool === 'point') {
                    const coords = getMouseCoords(e);
                    createPoint(coords[0], coords[1]);
                } else if (currentTool === 'line') {
                    const coords = getMouseCoords(e);
                    const point = createPoint(coords[0], coords[1]);
                    linePoints.push(point);
                    
                    if (linePoints.length === 2) {
                        createLine();
                        linePoints = [];
                    }
                }
            });

            graphBoard.on('move', function(e) {
                const coords = getMouseCoords(e);
                updateCoordinateDisplay(coords[0], coords[1]);
            });
        }

        function getMouseCoords(e) {
            const pos = graphBoard.getUsrCoordsOfMouse(e);
            return [pos[0], pos[1]];
        }

        function updateCoordinateDisplay(x, y) {
            document.getElementById('coordinate-info').textContent = `(${x.toFixed(2)}, ${y.toFixed(2)})`;
        }

        function setTool(tool) {
            currentTool = tool;
            
            // Update button states
            document.querySelectorAll('.demo-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`tool-${tool}`).classList.add('active');
            
            // Show/hide function input
            const functionInput = document.getElementById('function-input');
            functionInput.style.display = tool === 'function' ? 'block' : 'none';
            
            // Reset line points when switching tools
            linePoints = [];
        }

        function createPoint(x, y) {
            const point = graphBoard.create('point', [x, y], {
                name: `P${graphElements.length + 1}`,
                size: 4,
                fillColor: '#007bff',
                strokeColor: '#007bff'
            });
            
            graphElements.push({type: 'point', element: point});
            return point;
        }

        function createLine() {
            if (linePoints.length === 2) {
                const line = graphBoard.create('line', linePoints, {
                    strokeColor: '#28a745',
                    strokeWidth: 2
                });
                
                graphElements.push({type: 'line', element: line});
            }
        }

        function plotFunction() {
            const equation = document.getElementById('equation-input').value.trim();
            if (!equation) {
                alert('Please enter an equation');
                return;
            }
            
            try {
                let jsxEquation = equation
                    .replace(/\^/g, '**')
                    .replace(/(\d)([a-zA-Z])/g, '$1*$2')
                    .replace(/sin/g, 'Math.sin')
                    .replace(/cos/g, 'Math.cos')
                    .replace(/tan/g, 'Math.tan')
                    .replace(/sqrt/g, 'Math.sqrt')
                    .replace(/pi/g, 'Math.PI');
                
                const func = graphBoard.create('functiongraph', [
                    function(x) {
                        try {
                            return eval(jsxEquation.replace(/x/g, x));
                        } catch (e) {
                            return NaN;
                        }
                    }
                ], {
                    strokeColor: '#dc3545',
                    strokeWidth: 2
                });
                
                graphElements.push({type: 'function', element: func});
                document.getElementById('equation-input').value = '';
                
            } catch (error) {
                alert('Invalid equation. Please check your syntax.');
            }
        }

        function clearGraph() {
            graphElements.forEach(item => graphBoard.removeObject(item.element));
            graphElements = [];
            linePoints = [];
        }

        function resetView() {
            graphBoard.setBoundingBox([-10, 10, 10, -10]);
        }

        function toggleGrid() {
            isGridVisible = !isGridVisible;
            graphBoard.setAttribute({grid: isGridVisible});
        }

        // Initialize when page loads
        window.addEventListener('load', initGraph);
    </script>
</body>
</html>
