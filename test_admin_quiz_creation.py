#!/usr/bin/env python3
"""
Test script to verify admin quiz creation functionality
"""

import requests
from bs4 import BeautifulSoup
from app import app, db, User, Quiz, Question
from werkzeug.security import generate_password_hash

def setup_test_admin():
    """Ensure test admin user exists"""
    print("🔧 Setting up test admin user...")
    
    try:
        with app.app_context():
            # Check if admin user exists
            admin_user = User.query.filter_by(email='<EMAIL>').first()
            if not admin_user:
                admin_user = User(
                    name='Test Admin',
                    email='<EMAIL>',
                    password=generate_password_hash('AdminPass123!'),
                    unhashed_password='AdminPass123!',
                    role='admin',
                    is_verified=True
                )
                db.session.add(admin_user)
                db.session.commit()
                print("✅ Created test admin user")
            else:
                print("✅ Test admin user already exists")
                
    except Exception as e:
        print(f"❌ Error setting up admin user: {e}")
        db.session.rollback()

def test_admin_dashboard_quiz_buttons():
    """Test that admin dashboard has quiz creation buttons"""
    print("\n🧪 Testing Admin Dashboard Quiz Buttons")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # Login as admin
    login_data = {
        'email': '<EMAIL>',
        'password': 'AdminPass123!'
    }
    
    try:
        # Login
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if login_response.status_code != 302 or 'dashboard' not in login_response.headers.get('Location', ''):
            print("❌ Failed to login as admin")
            return
        
        print("✅ Successfully logged in as admin")
        
        # Get admin dashboard
        dashboard_response = session.get(f"{base_url}/admin/dashboard")
        
        if dashboard_response.status_code == 200:
            soup = BeautifulSoup(dashboard_response.text, 'html.parser')
            
            # Check for Create Quiz button
            create_quiz_link = soup.find('a', href='/teacher/create-quiz')
            if create_quiz_link:
                print("✅ Create Quiz button found on admin dashboard")
            else:
                print("❌ Create Quiz button not found on admin dashboard")
            
            # Check for My Quizzes button
            my_quizzes_link = soup.find('a', href='/my-quizzes')
            if my_quizzes_link:
                print("✅ My Quizzes button found on admin dashboard")
            else:
                print("❌ My Quizzes button not found on admin dashboard")
                
        else:
            print(f"❌ Failed to load admin dashboard (Status: {dashboard_response.status_code})")
            
    except Exception as e:
        print(f"❌ Error during dashboard testing: {e}")

def test_admin_quiz_creation_access():
    """Test that admin can access quiz creation page"""
    print("\n🧪 Testing Admin Quiz Creation Access")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # Login as admin
    login_data = {
        'email': '<EMAIL>',
        'password': 'AdminPass123!'
    }
    
    try:
        # Login
        session.post(f"{base_url}/login", data=login_data)
        
        # Try to access quiz creation page
        create_quiz_response = session.get(f"{base_url}/teacher/create-quiz")
        
        if create_quiz_response.status_code == 200:
            print("✅ Admin can access quiz creation page")
            
            # Check if form is present
            soup = BeautifulSoup(create_quiz_response.text, 'html.parser')
            form = soup.find('form', {'id': 'create-quiz-form'})
            if form:
                print("✅ Quiz creation form is present")
            else:
                print("❌ Quiz creation form not found")
                
        else:
            print(f"❌ Admin cannot access quiz creation page (Status: {create_quiz_response.status_code})")
            
        # Try to access my quizzes page
        my_quizzes_response = session.get(f"{base_url}/my-quizzes")
        
        if my_quizzes_response.status_code == 200:
            print("✅ Admin can access my quizzes page")
        else:
            print(f"❌ Admin cannot access my quizzes page (Status: {my_quizzes_response.status_code})")
            
    except Exception as e:
        print(f"❌ Error during access testing: {e}")

def test_admin_quiz_creation():
    """Test creating a quiz as admin"""
    print("\n🧪 Testing Admin Quiz Creation")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # Login as admin
    login_data = {
        'email': '<EMAIL>',
        'password': 'AdminPass123!'
    }
    
    try:
        # Login
        session.post(f"{base_url}/login", data=login_data)
        
        # Create a test quiz
        quiz_data = {
            'quiz_title': 'Admin Test Quiz',
            'quiz_description': 'A test quiz created by admin',
            'time_limit': '30',
            'difficulty': 'medium',
            'grade_a': '90',
            'grade_b': '80',
            'grade_c': '70',
            'grade_d': '60',
            'question[]': ['What is 2+2?'],
            'question_type[]': ['mcq'],
            'question_marks[]': ['1'],
            'option1[]': ['3'],
            'option2[]': ['4'],
            'option3[]': ['5'],
            'option4[]': ['6'],
            'correct_answer[0]': ['2'],
            'total_marks': '1'
        }
        
        # Submit quiz creation form
        create_response = session.post(f"{base_url}/teacher/create-quiz", data=quiz_data, allow_redirects=False)
        
        if create_response.status_code == 302:
            # Check if redirected to admin dashboard
            location = create_response.headers.get('Location', '')
            if 'admin/dashboard' in location:
                print("✅ Quiz created successfully, redirected to admin dashboard")
            else:
                print(f"⚠️  Quiz created but redirected to: {location}")
        else:
            print(f"❌ Quiz creation failed (Status: {create_response.status_code})")
            
    except Exception as e:
        print(f"❌ Error during quiz creation testing: {e}")

def verify_admin_quiz_in_database():
    """Verify that admin-created quiz exists in database"""
    print("\n🧪 Verifying Admin Quiz in Database")
    print("=" * 40)
    
    try:
        with app.app_context():
            # Find admin user
            admin_user = User.query.filter_by(email='<EMAIL>').first()
            if not admin_user:
                print("❌ Admin user not found")
                return
                
            # Find quizzes created by admin
            admin_quizzes = Quiz.query.filter_by(teacher_id=admin_user.id).all()
            
            print(f"📊 Admin has created {len(admin_quizzes)} quiz(es)")
            
            for quiz in admin_quizzes:
                print(f"  - {quiz.title} (ID: {quiz.id}, Difficulty: {quiz.difficulty})")
                print(f"    Created by: {quiz.teacher.name} (Role: {quiz.teacher.role})")
                
            if admin_quizzes:
                print("✅ Admin quizzes found in database")
            else:
                print("⚠️  No admin quizzes found in database")
                
    except Exception as e:
        print(f"❌ Error checking database: {e}")

def test_admin_quiz_visibility():
    """Test that admin quizzes are visible in my quizzes page"""
    print("\n🧪 Testing Admin Quiz Visibility")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # Login as admin
    login_data = {
        'email': '<EMAIL>',
        'password': 'AdminPass123!'
    }
    
    try:
        # Login
        session.post(f"{base_url}/login", data=login_data)
        
        # Get my quizzes page
        my_quizzes_response = session.get(f"{base_url}/my-quizzes")
        
        if my_quizzes_response.status_code == 200:
            soup = BeautifulSoup(my_quizzes_response.text, 'html.parser')
            
            # Check for admin badge
            admin_badges = soup.find_all('span', class_='creator-badge admin')
            if admin_badges:
                print(f"✅ Found {len(admin_badges)} admin badge(s) on my quizzes page")
            else:
                print("⚠️  No admin badges found (may be normal if no admin quizzes exist)")
                
            # Check for quiz cards
            quiz_cards = soup.find_all('div', class_='quiz-card')
            print(f"📊 Found {len(quiz_cards)} quiz card(s) on my quizzes page")
            
        else:
            print(f"❌ Failed to load my quizzes page (Status: {my_quizzes_response.status_code})")
            
    except Exception as e:
        print(f"❌ Error during visibility testing: {e}")

if __name__ == "__main__":
    print("🚀 Admin Quiz Creation Test Suite")
    print("=" * 50)
    
    setup_test_admin()
    test_admin_dashboard_quiz_buttons()
    test_admin_quiz_creation_access()
    test_admin_quiz_creation()
    verify_admin_quiz_in_database()
    test_admin_quiz_visibility()
    
    print("\n💡 Next steps:")
    print("   1. Visit http://127.0.0.1:5000/login")
    print("   2. Login with: <EMAIL> / AdminPass123!")
    print("   3. Go to Admin Dashboard")
    print("   4. Test 'Create New Quiz' and 'Manage My Quizzes' buttons")
    print("   5. Create a quiz and verify it appears with 'By Admin' badge")
    print("   6. Check that admin quizzes appear in 'View All Quizzes' with admin indicator")
