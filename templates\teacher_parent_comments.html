{% extends "base.html" %}

{% block title %}Parent Comments{% endblock %}

{% block content %}
<div class="container comments-container">
    <div class="comments-header">
        <h1>Parent Comments</h1>
        <p class="text-muted">View feedback and concerns from parents about their children's quiz performance.</p>
        <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-secondary back-btn">Back to Dashboard</a>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <h3>Filter Comments</h3>
        <form method="GET" class="filter-form">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="quiz_id">Filter by Quiz:</label>
                    <select id="quiz_id" name="quiz_id" class="form-control">
                        <option value="">All Quizzes</option>
                        {% for quiz in teacher_quizzes %}
                        <option value="{{ quiz.id }}" {% if quiz_filter == quiz.id|string %}selected{% endif %}>
                            {{ quiz.title }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="student_id">Filter by Student:</label>
                    <select id="student_id" name="student_id" class="form-control">
                        <option value="">All Students</option>
                        {% for student in students_with_attempts %}
                        <option value="{{ student.id }}" {% if student_filter == student.id|string %}selected{% endif %}>
                            {{ student.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="comments_only" value="true" {% if comments_only %}checked{% endif %}>
                        Only quizzes with comments
                    </label>
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                    <a href="{{ url_for('view_parent_comments') }}" class="btn btn-outline">Clear Filters</a>
                </div>
            </div>
        </form>
    </div>

    <!-- Comments Section -->
    <div class="comments-section">
        {% if comments %}
            <div class="comments-count">
                <h3>{{ comments|length }} Comment{{ 's' if comments|length != 1 else '' }} Found</h3>
            </div>
            
            {% for comment in comments %}
            <div class="comment-card">
                <div class="comment-header">
                    <div class="comment-meta">
                        <h4>{{ comment.student.name }}</h4>
                        <span class="quiz-title">{{ comment.quiz.title }}</span>
                        <span class="comment-date">{{ comment.timestamp.strftime('%b %d, %Y at %I:%M %p') }}</span>
                    </div>
                    <div class="parent-info">
                        <span class="parent-name">From: {{ comment.parent.name }}</span>
                        <span class="parent-email">({{ comment.parent.email }})</span>
                    </div>
                </div>
                
                <div class="comment-body">
                    <div class="comment-text">{{ comment.comment_text }}</div>
                </div>
                
                <div class="comment-actions">
                    <a href="{{ url_for('view_child_result', attempt_id=comment.attempt_id) }}" class="btn btn-sm btn-info">
                        View Quiz Result
                    </a>
                    <a href="{{ url_for('compose', recipient_id=comment.parent_id) }}" class="btn btn-sm btn-primary">
                        Send Message
                    </a>
                    <button type="button" class="btn btn-sm btn-secondary toggle-reply-btn" data-comment-id="{{ comment.id }}">
                        Quick Reply
                    </button>
                </div>

                <!-- Quick Reply Form (initially hidden) -->
                <div class="reply-form-container" id="reply-form-{{ comment.id }}" style="display: none;">
                    <form method="POST" action="{{ url_for('submit_teacher_reply') }}" class="reply-form">
                        <input type="hidden" name="parent_comment_id" value="{{ comment.id }}">
                        <div class="form-group">
                            <textarea name="reply_text" placeholder="Write a quick reply to {{ comment.parent.name }}..."
                                      class="form-control reply-textarea" maxlength="2000" rows="3" required></textarea>
                            <div class="char-count">0/2000 characters</div>
                        </div>
                        <div class="reply-actions">
                            <button type="submit" class="btn btn-sm btn-primary">Send Reply</button>
                            <button type="button" class="btn btn-sm btn-outline cancel-reply-btn">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-comments">
                <div class="no-comments-icon">💬</div>
                <h3>No Comments Found</h3>
                {% if quiz_filter or student_filter or comments_only %}
                <p>No parent comments match your current filters. Try adjusting your search criteria.</p>
                <a href="{{ url_for('view_parent_comments') }}" class="btn btn-primary">View All Comments</a>
                {% else %}
                <p>No parents have left comments on quiz attempts yet. Comments will appear here when parents provide feedback on their children's quiz performance.</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<style>
.comments-container {
    max-width: 1000px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.comments-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.comments-header h1 {
    color: #007bff;
    margin-bottom: 0.5rem;
}

.text-muted {
    color: #666;
    margin-bottom: 1rem;
}

.back-btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #6c757d;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.back-btn:hover {
    background-color: #545b62;
}

/* Filters Section */
.filters-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.filters-section h3 {
    margin-bottom: 1rem;
    color: #333;
}

.filter-form {
    margin: 0;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    margin-top: 1.5rem;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-outline {
    background-color: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background-color: #007bff;
    color: white;
}

/* Comments Section */
.comments-count {
    margin-bottom: 1.5rem;
}

.comments-count h3 {
    color: #333;
    font-size: 1.2rem;
}

.comment-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    overflow: hidden;
}

.comment-header {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comment-meta h4 {
    margin: 0 0 0.25rem 0;
    color: #007bff;
    font-size: 1.1rem;
}

.quiz-title {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.25rem;
}

.comment-date {
    font-size: 0.85rem;
    color: #666;
}

.parent-info {
    text-align: right;
}

.parent-name {
    display: block;
    font-weight: 500;
    color: #333;
}

.parent-email {
    font-size: 0.85rem;
    color: #666;
}

.comment-body {
    padding: 1.5rem;
}

.comment-text {
    color: #333;
    line-height: 1.6;
    white-space: pre-wrap;
    font-size: 1rem;
}

.comment-actions {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

/* Reply Forms */
.reply-form-container {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.reply-form {
    margin: 0;
}

.reply-textarea {
    resize: vertical;
    min-height: 80px;
}

.char-count {
    text-align: right;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.reply-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* No Comments State */
.no-comments {
    text-align: center;
    padding: 3rem 2rem;
    color: #666;
}

.no-comments-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-comments h3 {
    color: #333;
    margin-bottom: 1rem;
}

.no-comments p {
    margin-bottom: 1.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .parent-info {
        text-align: left;
    }
    
    .comment-actions {
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle reply forms
    const toggleButtons = document.querySelectorAll('.toggle-reply-btn');
    const cancelButtons = document.querySelectorAll('.cancel-reply-btn');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const commentId = this.getAttribute('data-comment-id');
            const replyForm = document.getElementById(`reply-form-${commentId}`);

            if (replyForm.style.display === 'none') {
                replyForm.style.display = 'block';
                this.textContent = 'Hide Reply';
                this.classList.remove('btn-secondary');
                this.classList.add('btn-warning');
            } else {
                replyForm.style.display = 'none';
                this.textContent = 'Quick Reply';
                this.classList.remove('btn-warning');
                this.classList.add('btn-secondary');
            }
        });
    });

    cancelButtons.forEach(button => {
        button.addEventListener('click', function() {
            const replyForm = this.closest('.reply-form-container');
            const commentId = replyForm.id.replace('reply-form-', '');
            const toggleButton = document.querySelector(`[data-comment-id="${commentId}"]`);

            replyForm.style.display = 'none';
            toggleButton.textContent = 'Quick Reply';
            toggleButton.classList.remove('btn-warning');
            toggleButton.classList.add('btn-secondary');

            // Clear the textarea
            const textarea = replyForm.querySelector('.reply-textarea');
            textarea.value = '';
            updateCharCount(textarea);
        });
    });

    // Character count for reply textareas
    const textareas = document.querySelectorAll('.reply-textarea');

    function updateCharCount(textarea) {
        const charCount = textarea.parentNode.querySelector('.char-count');
        const count = textarea.value.length;
        charCount.textContent = `${count}/2000 characters`;

        if (count > 1800) {
            charCount.style.color = '#dc3545';
        } else if (count > 1500) {
            charCount.style.color = '#ffc107';
        } else {
            charCount.style.color = '#666';
        }
    }

    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            updateCharCount(this);
        });
        updateCharCount(textarea); // Initial count
    });
});
</script>
{% endblock %}
