#!/usr/bin/env python3
"""
Test script for in-place quiz editing (when no attempts exist)
This verifies that quizzes without attempts can still be edited in place
without creating new versions.
"""

import requests
import time

BASE_URL = "http://127.0.0.1:5000"

def login_user(email, password):
    """Login and return session cookies"""
    session = requests.Session()
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print(f"Failed to get login page: {response.status_code}")
        return None
    
    # Login
    login_data = {
        'email': email,
        'password': password
    }
    response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200 and "dashboard" in response.url:
        print(f"✅ Successfully logged in as {email}")
        return session
    else:
        print(f"❌ Failed to login as {email}")
        return None

def create_test_quiz(session):
    """Create a test quiz for in-place editing"""
    unique_title = f'In-Place Edit Test {int(time.time())}'
    quiz_data = {
        'quiz_title': unique_title,
        'quiz_description': 'A quiz to test in-place editing',
        'time_limit': '20',
        'total_marks': '5',
        'grade_a': '90',
        'grade_b': '80',
        'grade_c': '70',
        'grade_d': '60',
        'difficulty': 'Easy',
        'num_questions': '1',
        'question_type_1': 'mcq',
        'question_text_1': 'What is 1 + 1?',
        'question_marks_1': '5',
        'mcq_option1_1': '1',
        'mcq_option2_1': '2',
        'mcq_option3_1': '3',
        'mcq_option4_1': '4',
        'correct_answer_1': 'option2'
    }
    
    response = session.post(f"{BASE_URL}/teacher/create-quiz", data=quiz_data)
    if response.status_code == 200:
        print("✅ Test quiz created successfully")
        return unique_title
    else:
        print(f"❌ Failed to create test quiz: {response.status_code}")
        return None

def get_quiz_id_by_title(session, title):
    """Get quiz ID by title from my-quizzes page"""
    response = session.get(f"{BASE_URL}/my-quizzes")
    if response.status_code == 200:
        content = response.text
        import re
        pattern = r'<h3>([^<]+)</h3>.*?/teacher/edit-quiz/(\d+)'
        matches = re.findall(pattern, content, re.DOTALL)
        for quiz_title, quiz_id in matches:
            if title.strip() in quiz_title.strip():
                return int(quiz_id)
    return None

def edit_quiz_without_attempts(session, quiz_id, original_title):
    """Edit a quiz that has NO attempts (should edit in place)"""
    edit_data = {
        'quiz_title': f'{original_title} - Edited In Place',
        'quiz_description': 'Updated description for in-place editing test',
        'time_limit': '25',
        'total_marks': '10',
        'grade_a': '85',
        'grade_b': '75',
        'grade_c': '65',
        'grade_d': '55',
        'difficulty': 'Medium',
        'num_questions': '2',
        'question_type_1': 'mcq',
        'question_text_1': 'What is 2 + 2?',
        'question_marks_1': '5',
        'mcq_option1_1': '3',
        'mcq_option2_1': '4',
        'mcq_option3_1': '5',
        'mcq_option4_1': '6',
        'correct_answer_1': 'option2',
        'question_type_2': 'mcq',
        'question_text_2': 'What is 3 + 3?',
        'question_marks_2': '5',
        'mcq_option1_2': '5',
        'mcq_option2_2': '6',
        'mcq_option3_2': '7',
        'mcq_option4_2': '8',
        'correct_answer_2': 'option2'
    }
    
    response = session.post(f"{BASE_URL}/teacher/edit-quiz/{quiz_id}", data=edit_data)
    if response.status_code == 200:
        print("✅ Quiz edited successfully (should be in-place)")
        return True
    else:
        print(f"❌ Failed to edit quiz: {response.status_code}")
        return False

def verify_in_place_editing(session, quiz_id, original_title):
    """Verify that in-place editing worked correctly"""
    # Check that the quiz was edited in place (same ID, updated content)
    response = session.get(f"{BASE_URL}/my-quizzes")
    if response.status_code == 200:
        content = response.text
        if "Edited In Place" in content:
            print("✅ Quiz was edited in place (title updated)")
        else:
            print("❌ Quiz title was not updated")
    
    # Verify no archived versions were created
    response = session.get(f"{BASE_URL}/my-quizzes?view=archived")
    if response.status_code == 200:
        content = response.text
        if original_title not in content:
            print("✅ No archived version created (correct for in-place editing)")
        else:
            print("❌ Archived version was created (should not happen for in-place editing)")

def main():
    print("🧪 Starting In-Place Quiz Editing Test")
    print("=" * 50)
    
    # Test as teacher
    print("\n1. Testing as Teacher...")
    teacher_session = login_user("<EMAIL>", "Teacher123!")
    if not teacher_session:
        return
    
    # Create test quiz
    print("\n2. Creating test quiz...")
    quiz_title = create_test_quiz(teacher_session)
    if not quiz_title:
        return
    
    # Get quiz ID
    quiz_id = get_quiz_id_by_title(teacher_session, quiz_title)
    if not quiz_id:
        print("❌ Could not find created quiz ID")
        return
    print(f"✅ Found quiz ID: {quiz_id}")
    
    # Edit the quiz WITHOUT any student attempts
    print("\n3. Editing quiz without attempts (should be in-place)...")
    if not edit_quiz_without_attempts(teacher_session, quiz_id, quiz_title):
        return
    
    # Verify in-place editing
    print("\n4. Verifying in-place editing...")
    verify_in_place_editing(teacher_session, quiz_id, quiz_title)
    
    print("\n" + "=" * 50)
    print("🎉 In-Place Quiz Editing Test Complete!")
    print("\nThis test verifies that:")
    print("- Quizzes without attempts are edited in place")
    print("- No new versions are created")
    print("- No archived versions are created")
    print("- The same quiz ID retains the updated content")

if __name__ == "__main__":
    main()
