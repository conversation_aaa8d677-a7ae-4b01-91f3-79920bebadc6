{% extends "base.html" %}

{% block title %}Manage Student Reports{% endblock %}

{% block content %}
<div class="container report-manage-container">
    <h1>Manage Student Report Comments</h1>
    <p class="text-muted">Add or update comments that will appear on student report cards viewable by parents.</p>

    {% if students %}
        {% for student in students %}
        <div class="student-comment-card">
            <div class="student-header">
                <h4>{{ student.name }} <small>({{ student.email }})</small></h4>
                <a href="{{ url_for('teacher_export_student_report', student_id=student.id) }}"
                   class="btn btn-sm btn-danger export-btn" title="Download Report Card as PDF">
                    📄 Export PDF
                </a>
            </div>
            <form method="POST" action="{{ url_for('manage_student_reports') }}">
                <input type="hidden" name="student_id" value="{{ student.id }}">
                <div class="form-group">
                    <label for="comment_{{ student.id }}">Teacher Comment:</label>
                    <textarea id="comment_{{ student.id }}" name="report_comment" rows="3" class="form-control">{{ student.report_comment | default('', true) }}</textarea>
                </div>
                <button type="submit" class="btn btn-sm btn-success">Save Comment</button>
            </form>
        </div>
        {% endfor %}
    {% else %}
        <p>No students found.</p>
    {% endif %}

     <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-secondary back-btn">Back to Dashboard</a>

</div>

<style>
.report-manage-container {
    max-width: 800px;
    margin: 2rem auto;
}

.report-manage-container h1 {
    text-align: center;
    margin-bottom: 0.5rem;
}
.report-manage-container .text-muted {
    text-align: center;
    margin-bottom: 2rem;
}

.student-comment-card {
    background-color: #fff;
    padding: 1.5rem 2rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.06);
}

.student-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.student-comment-card h4 {
    margin-bottom: 0;
    color: #007bff;
}
.student-comment-card h4 small {
    font-size: 0.85em;
    color: #6c757d;
    font-weight: normal;
}

.export-btn {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap;
}

.export-btn:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
    text-decoration: none;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.4rem;
    font-size: 0.9rem;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.6rem 0.8rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
textarea.form-control {
    resize: vertical;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}
.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.back-btn {
     display: block;
     width: fit-content;
     margin: 2rem auto 0 auto;
}
</style>
{% endblock %} 