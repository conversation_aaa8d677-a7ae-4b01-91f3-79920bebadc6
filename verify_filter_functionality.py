#!/usr/bin/env python3
"""
Verify that the difficulty filter actually filters quizzes correctly
"""

import requests
from bs4 import BeautifulSoup
from app import app, db, Quiz

def count_quizzes_by_difficulty():
    """Count quizzes in database by difficulty"""
    print("📊 Database Quiz Counts by Difficulty")
    print("=" * 40)
    
    try:
        with app.app_context():
            all_quizzes = Quiz.query.all()
            
            counts = {
                'All': len(all_quizzes),
                'Easy': len(Quiz.query.filter_by(difficulty='Easy').all()),
                'Medium': len(Quiz.query.filter_by(difficulty='Medium').all()),
                'Hard': len(Quiz.query.filter_by(difficulty='Hard').all())
            }
            
            for difficulty, count in counts.items():
                print(f"   {difficulty}: {count} quiz(s)")
            
            return counts
    
    except Exception as e:
        print(f"❌ Error counting quizzes: {e}")
        return {}

def test_filter_accuracy():
    """Test that the filter actually shows the correct number of quizzes"""
    print("\n🎯 Testing Filter Accuracy")
    print("=" * 40)
    
    # Get expected counts from database
    expected_counts = count_quizzes_by_difficulty()
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # Login as test student
    login_data = {
        'email': '<EMAIL>',
        'password': 'TestPass123!'
    }
    
    try:
        # Login
        login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if login_response.status_code != 302:
            print("❌ Failed to login")
            return
        
        print("✅ Logged in successfully")
        
        # Test each difficulty filter
        for difficulty in ['All', 'Easy', 'Medium', 'Hard']:
            print(f"\n🔍 Testing {difficulty} filter:")
            
            # Make request with filter
            dashboard_url = f"{base_url}/student/dashboard"
            params = {'difficulty': difficulty} if difficulty != 'All' else {}
            
            response = session.get(dashboard_url, params=params)
            
            if response.status_code == 200:
                # Parse HTML to count quiz cards
                soup = BeautifulSoup(response.text, 'html.parser')
                quiz_cards = soup.find_all('div', class_='quiz-card')
                actual_count = len(quiz_cards)
                expected_count = expected_counts.get(difficulty, 0)
                
                print(f"   Expected: {expected_count} quiz(s)")
                print(f"   Actual:   {actual_count} quiz(s)")
                
                if actual_count == expected_count:
                    print(f"   ✅ PASS - Filter working correctly")
                else:
                    print(f"   ❌ FAIL - Filter not working correctly")
                
                # Check if correct difficulty badge is shown
                if difficulty != 'All' and quiz_cards:
                    difficulty_badges = soup.find_all('span', class_='difficulty')
                    correct_badges = [badge for badge in difficulty_badges 
                                    if badge.text.strip().lower() == difficulty.lower()]
                    
                    if len(correct_badges) == actual_count:
                        print(f"   ✅ All displayed quizzes have correct difficulty")
                    else:
                        print(f"   ⚠️  Some quizzes may have incorrect difficulty")
                
                # Check if active filter button is highlighted
                active_buttons = soup.find_all('a', class_='filter-btn active')
                if active_buttons:
                    active_text = active_buttons[0].text.strip()
                    if active_text == difficulty:
                        print(f"   ✅ Correct filter button is active")
                    else:
                        print(f"   ⚠️  Wrong filter button is active: {active_text}")
                else:
                    print(f"   ⚠️  No active filter button found")
            
            else:
                print(f"   ❌ Failed to load dashboard (Status: {response.status_code})")
    
    except Exception as e:
        print(f"❌ Error during testing: {e}")

def test_url_parameters():
    """Test that URL parameters work correctly"""
    print("\n🔗 Testing URL Parameters")
    print("=" * 30)
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # Login first
    login_data = {
        'email': '<EMAIL>',
        'password': 'TestPass123!'
    }
    
    try:
        session.post(f"{base_url}/login", data=login_data)
        
        # Test direct URL access with parameters
        test_urls = [
            ('/student/dashboard', 'Default (All)'),
            ('/student/dashboard?difficulty=All', 'All parameter'),
            ('/student/dashboard?difficulty=Easy', 'Easy parameter'),
            ('/student/dashboard?difficulty=Medium', 'Medium parameter'),
            ('/student/dashboard?difficulty=Hard', 'Hard parameter'),
            ('/student/dashboard?difficulty=Invalid', 'Invalid parameter (should default to All)')
        ]
        
        for url_path, description in test_urls:
            print(f"\n📝 Testing: {description}")
            print(f"   URL: {url_path}")
            
            response = session.get(f"{base_url}{url_path}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS - Page loaded correctly")
                
                # Check if filter buttons are present
                if 'filter-btn' in response.text:
                    print(f"   ✅ Filter buttons are present")
                else:
                    print(f"   ❌ Filter buttons are missing")
            else:
                print(f"   ❌ FAILED - Status code: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Error testing URL parameters: {e}")

if __name__ == "__main__":
    print("🔍 Comprehensive Filter Verification")
    print("=" * 50)
    
    count_quizzes_by_difficulty()
    test_filter_accuracy()
    test_url_parameters()
    
    print("\n🎉 Verification completed!")
    print("\n💡 Manual testing suggestions:")
    print("   1. Visit http://127.0.0.1:5000/login")
    print("   2. Login as: <EMAIL> / TestPass123!")
    print("   3. Click each difficulty filter button and verify:")
    print("      - Only quizzes of that difficulty are shown")
    print("      - The correct button is highlighted")
    print("      - URL updates with the correct parameter")
    print("   4. Test browser back/forward buttons")
    print("   5. Test direct URL access with parameters")
