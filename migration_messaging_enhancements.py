#!/usr/bin/env python3
"""
Database Migration Script: Add messaging enhancements
Adds thread_id and message_type fields to the message table
"""

import sqlite3
import os
from datetime import datetime

def get_database_path():
    """Get the path to the SQLite database"""
    # Check if we're in the instance folder
    if os.path.exists('instance/quiz_management.db'):
        return 'instance/quiz_management.db'
    # Check if we're in the root folder
    elif os.path.exists('quiz_management.db'):
        return 'quiz_management.db'
    else:
        # Create instance folder if it doesn't exist
        os.makedirs('instance', exist_ok=True)
        return 'instance/quiz_management.db'

def backup_database(db_path):
    """Create a backup of the database before migration"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"⚠ Warning: Could not create backup: {e}")
        return None

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def add_messaging_enhancements(cursor):
    """Add thread_id and message_type columns to message table"""
    
    # Check if message table exists
    if not check_table_exists(cursor, 'message'):
        print("❌ FAILED: message table does not exist")
        return False
    
    # Add thread_id column if it doesn't exist
    if not check_column_exists(cursor, 'message', 'thread_id'):
        cursor.execute("""
            ALTER TABLE message 
            ADD COLUMN thread_id VARCHAR(50)
        """)
        print("✓ Added thread_id column to message table")
    else:
        print("✓ thread_id column already exists")
    
    # Add message_type column if it doesn't exist
    if not check_column_exists(cursor, 'message', 'message_type'):
        cursor.execute("""
            ALTER TABLE message 
            ADD COLUMN message_type VARCHAR(20) DEFAULT 'normal' NOT NULL
        """)
        print("✓ Added message_type column to message table")
    else:
        print("✓ message_type column already exists")
    
    # Create index on thread_id for better performance
    try:
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_message_thread_id 
            ON message(thread_id)
        """)
        print("✓ Created index on thread_id")
    except Exception as e:
        print(f"⚠ Warning: Could not create index on thread_id: {e}")
    
    return True

def verify_migration(cursor):
    """Verify that the migration was successful"""
    print("\n=== Verifying Migration ===")
    
    # Check table structure
    cursor.execute("PRAGMA table_info(message)")
    columns = cursor.fetchall()
    
    expected_columns = ['thread_id', 'message_type']
    found_columns = [col[1] for col in columns]
    
    for col in expected_columns:
        if col in found_columns:
            print(f"✓ Column {col} exists")
        else:
            print(f"❌ Column {col} missing")
            return False
    
    # Check indexes
    cursor.execute("PRAGMA index_list(message)")
    indexes = cursor.fetchall()
    
    thread_id_index_exists = any('thread_id' in str(index) for index in indexes)
    if thread_id_index_exists:
        print("✓ thread_id index exists")
    else:
        print("⚠ thread_id index not found")
    
    # Check data integrity
    cursor.execute("SELECT COUNT(*) FROM message")
    message_count = cursor.fetchone()[0]
    print(f"✓ Message table contains {message_count} records")
    
    return True

def main():
    """Main migration function"""
    print("=== Messaging Enhancements Migration ===")
    print("Adding thread_id and message_type fields to message table")
    print()
    
    # Get database path
    db_path = get_database_path()
    print(f"Database path: {db_path}")
    
    # Check if database exists
    if not os.path.exists(db_path):
        print(f"❌ FAILED: Database not found at {db_path}")
        print("Please run the application first to create the database.")
        return False
    
    # Create backup
    backup_path = backup_database(db_path)
    
    # Connect to database
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON")
        
        print("\n=== Starting Migration ===")
        
        # Add messaging enhancements
        if not add_messaging_enhancements(cursor):
            print("❌ FAILED: Could not add messaging enhancements")
            conn.rollback()
            return False
        
        # Commit changes
        conn.commit()
        print("✓ Migration completed successfully")
        
        # Verify migration
        if verify_migration(cursor):
            print("\n✅ Migration verification passed!")
        else:
            print("\n⚠ Migration verification failed!")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Migration error: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Messaging enhancements migration completed successfully!")
        print("You can now use the enhanced messaging features.")
    else:
        print("\n💥 Migration failed!")
        print("Please check the error messages above and try again.")
    
    input("\nPress Enter to exit...")
