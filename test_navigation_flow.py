#!/usr/bin/env python3
"""
Navigation Flow Test Suite
=========================

This script tests the navigation flow to ensure:
1. Login and signup pages don't show logout/home buttons
2. Logout buttons are present on all dashboards
3. Already logged-in users are redirected from auth pages
4. Users must sign in again when returning to auth pages
"""

import requests
import time
from urllib.parse import urljoin

# Configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_DELAY = 1  # seconds between tests

# Test user credentials
test_users = {
    'teacher': {
        'email': '<EMAIL>',
        'password': 'Teacher123!'
    },
    'parent': {
        'email': '<EMAIL>', 
        'password': 'Pass@123'
    },
    'student': {
        'email': '<EMAIL>',
        'password': 'Pass@123'
    },
    'admin': {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
}

class TestSession:
    def __init__(self):
        self.session = requests.Session()

def print_step(message):
    print(f"✓ {message}")

def print_error(message):
    print(f"✗ {message}")

def print_test_header(title):
    print(f"\n{'='*50}")
    print(f"=== {title} ===")
    print('='*50)

def test_auth_pages_no_navigation():
    """Test that login and signup pages don't show navigation"""
    print_step("Testing authentication pages for navigation elements")
    
    session = TestSession()
    
    try:
        # Test login page
        response = session.session.get(f"{BASE_URL}/login")
        if response.status_code == 200:
            content = response.text
            
            # Check that navigation header HTML element is not present
            if '<header class="main-header">' not in content:
                print_step("Login page correctly hides navigation header")
            else:
                print_error("Login page still shows navigation header")
                return False
            
            # Check that logout links are not present
            if 'Logout' not in content:
                print_step("Login page correctly hides logout button")
            else:
                print_error("Login page still shows logout button")
                return False
        else:
            print_error(f"Failed to access login page: {response.status_code}")
            return False
        
        # Test signup page
        response = session.session.get(f"{BASE_URL}/signup")
        if response.status_code == 200:
            content = response.text
            
            # Check that navigation header HTML element is not present
            if '<header class="main-header">' not in content:
                print_step("Signup page correctly hides navigation header")
            else:
                print_error("Signup page still shows navigation header")
                return False
            
            # Check that logout links are not present
            if 'Logout' not in content:
                print_step("Signup page correctly hides logout button")
            else:
                print_error("Signup page still shows logout button")
                return False
        else:
            print_error(f"Failed to access signup page: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print_error(f"Auth pages test failed: {e}")
        return False

def login_user(session, email, password, expected_role):
    """Login a user and verify successful authentication"""
    try:
        # Submit login form
        login_data = {
            'email': email,
            'password': password
        }
        
        response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=True)
        
        if response.status_code == 200:
            # Check if we're redirected to dashboard (successful login)
            if "dashboard" in response.url:
                print_step(f"Successfully logged in as {expected_role}: {email}")
                return True
            else:
                print_error(f"Login failed - unexpected redirect: {response.url}")
                return False
        else:
            print_error(f"Login failed with status code: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Login error: {e}")
        return False

def test_dashboard_logout_buttons():
    """Test that all dashboards have logout buttons"""
    print_step("Testing logout buttons on dashboards")
    
    dashboard_tests = [
        ('teacher', 'teacher_dashboard'),
        ('student', 'student_dashboard'), 
        ('parent', 'parent_dashboard'),
        ('admin', 'admin_dashboard')
    ]
    
    for role, dashboard_name in dashboard_tests:
        if role not in test_users:
            print_error(f"No test credentials for {role}")
            continue
            
        session = TestSession()
        
        # Login as the user
        if not login_user(session.session, test_users[role]['email'], 
                         test_users[role]['password'], role):
            print_error(f"Failed to login as {role}")
            continue
        
        # Access dashboard
        dashboard_url = f"{BASE_URL}/{role}/dashboard" if role != 'admin' else f"{BASE_URL}/admin/dashboard"
        response = session.session.get(dashboard_url)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for logout button in navigation or dashboard
            if 'Logout' in content or 'logout' in content:
                print_step(f"{role.title()} dashboard has logout button")
            else:
                print_error(f"{role.title()} dashboard missing logout button")
                return False
        else:
            print_error(f"Failed to access {role} dashboard: {response.status_code}")
            return False
        
        time.sleep(TEST_DELAY)
    
    return True

def test_logged_in_user_redirect():
    """Test that logged-in users are redirected from auth pages"""
    print_step("Testing logged-in user redirect from auth pages")
    
    session = TestSession()
    
    # Login as teacher
    if not login_user(session.session, test_users['teacher']['email'], 
                     test_users['teacher']['password'], 'teacher'):
        print_error("Failed to login for redirect test")
        return False
    
    # Try to access login page while logged in
    response = session.session.get(f"{BASE_URL}/login", allow_redirects=False)
    if response.status_code == 302:
        # Should redirect to dashboard
        location = response.headers.get('Location', '')
        if 'dashboard' in location:
            print_step("Logged-in user correctly redirected from login page")
        else:
            print_error(f"Unexpected redirect from login page: {location}")
            return False
    else:
        print_error("Logged-in user not redirected from login page")
        return False
    
    # Try to access signup page while logged in
    response = session.session.get(f"{BASE_URL}/signup", allow_redirects=False)
    if response.status_code == 302:
        # Should redirect to dashboard
        location = response.headers.get('Location', '')
        if 'dashboard' in location:
            print_step("Logged-in user correctly redirected from signup page")
        else:
            print_error(f"Unexpected redirect from signup page: {location}")
            return False
    else:
        print_error("Logged-in user not redirected from signup page")
        return False
    
    return True

def test_logout_functionality():
    """Test that logout works correctly"""
    print_step("Testing logout functionality")
    
    session = TestSession()
    
    # Login as teacher
    if not login_user(session.session, test_users['teacher']['email'], 
                     test_users['teacher']['password'], 'teacher'):
        print_error("Failed to login for logout test")
        return False
    
    # Logout
    response = session.session.get(f"{BASE_URL}/logout", allow_redirects=True)
    if response.status_code == 200:
        # Should be redirected to login page
        if 'login' in response.url:
            print_step("Logout correctly redirects to login page")
        else:
            print_error(f"Logout redirected to unexpected page: {response.url}")
            return False
    else:
        print_error(f"Logout failed: {response.status_code}")
        return False
    
    # Try to access dashboard after logout (should redirect to login)
    response = session.session.get(f"{BASE_URL}/teacher/dashboard", allow_redirects=True)
    if 'login' in response.url:
        print_step("Access to dashboard after logout correctly redirects to login")
    else:
        print_error("Dashboard access after logout doesn't redirect to login")
        return False
    
    return True

def main():
    """Main test execution"""
    print("Navigation Flow Test Suite")
    print("=" * 50)
    print("🧪 Starting Navigation Tests")
    
    all_tests_passed = True
    
    tests = [
        ("Authentication Pages Navigation", test_auth_pages_no_navigation),
        ("Dashboard Logout Buttons", test_dashboard_logout_buttons),
        ("Logged-in User Redirect", test_logged_in_user_redirect),
        ("Logout Functionality", test_logout_functionality)
    ]
    
    for test_name, test_func in tests:
        print_test_header(test_name)
        
        try:
            if not test_func():
                all_tests_passed = False
        except Exception as e:
            print_error(f"Test {test_name} failed with error: {e}")
            all_tests_passed = False
        
        time.sleep(TEST_DELAY)
    
    # Final results
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All navigation tests passed!")
        print("✅ Navigation flow is working correctly.")
    else:
        print("❌ Some tests failed!")
        print("Please check the error messages above.")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
