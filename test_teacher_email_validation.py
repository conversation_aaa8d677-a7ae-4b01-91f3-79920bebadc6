#!/usr/bin/env python3
"""
Test script to verify teacher email validation
"""

import requests
import json

# Test data for teacher signup
test_cases = [
    {
        "name": "Valid Teacher",
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirm_password": "TestPass123!",
        "role": "teacher",
        "expected_result": "success"  # Should pass validation
    },
    {
        "name": "Invalid Teacher Gmail",
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirm_password": "TestPass123!",
        "role": "teacher",
        "expected_result": "error"  # Should fail validation
    },
    {
        "name": "Invalid Teacher Yahoo",
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirm_password": "TestPass123!",
        "role": "teacher",
        "expected_result": "error"  # Should fail validation
    },
    {
        "name": "Invalid Teacher Other Domain",
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirm_password": "TestPass123!",
        "role": "teacher",
        "expected_result": "error"  # Should fail validation
    }
]

def test_teacher_email_validation():
    """Test teacher email validation by making POST requests to signup endpoint"""
    base_url = "http://127.0.0.1:5000"
    signup_url = f"{base_url}/signup"
    
    print("Testing Teacher Email Validation")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Email: {test_case['email']}")
        print(f"Expected: {test_case['expected_result']}")
        
        # Prepare form data
        form_data = {
            'name': test_case['name'],
            'email': test_case['email'],
            'password': test_case['password'],
            'confirm_password': test_case['confirm_password'],
            'role': test_case['role']
        }
        
        try:
            # Make POST request to signup
            response = requests.post(signup_url, data=form_data, allow_redirects=False)
            
            # Check response
            if response.status_code == 302:  # Redirect
                location = response.headers.get('Location', '')
                if 'verify-otp' in location:
                    result = "success"
                    print(f"Result: SUCCESS - Redirected to OTP verification")
                elif 'signup' in location:
                    result = "error"
                    print(f"Result: ERROR - Redirected back to signup (validation failed)")
                else:
                    result = "unknown"
                    print(f"Result: UNKNOWN - Redirected to {location}")
            else:
                result = "error"
                print(f"Result: ERROR - Status code {response.status_code}")
            
            # Check if result matches expectation
            if result == test_case['expected_result']:
                print("✅ PASS")
            else:
                print("❌ FAIL")
                
        except Exception as e:
            print(f"Result: ERROR - Exception: {e}")
            print("❌ FAIL")

if __name__ == "__main__":
    test_teacher_email_validation()
