#!/usr/bin/env python3
"""
Comprehensive test script for quiz question randomization functionality
Tests:
1. Question order randomization for different students
2. Order preservation during quiz attempt
3. Answer mapping to correct question IDs
4. Randomization disabled for quizzes with ≤2 questions
5. Randomization toggle in quiz creation and editing
"""

import requests
import json
import time
import random
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TEST_TEACHER_EMAIL = "<EMAIL>"
TEST_TEACHER_PASSWORD = "password123"
TEST_STUDENT_EMAILS = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
TEST_STUDENT_PASSWORD = "password123"

class QuizRandomizationTester:
    def __init__(self):
        self.session = requests.Session()
        self.teacher_session = requests.Session()
        self.student_sessions = {}
        
    def login_teacher(self):
        """Login as teacher"""
        print("🔐 Logging in as teacher...")
        response = self.teacher_session.post(f"{BASE_URL}/login", data={
            'email': TEST_TEACHER_EMAIL,
            'password': TEST_TEACHER_PASSWORD
        })
        
        if response.status_code == 200 and "dashboard" in response.url:
            print("✅ Teacher login successful")
            return True
        else:
            print(f"❌ Teacher login failed: {response.status_code}")
            return False
    
    def login_students(self):
        """Login multiple students"""
        print("🔐 Logging in students...")
        for i, email in enumerate(TEST_STUDENT_EMAILS):
            session = requests.Session()
            response = session.post(f"{BASE_URL}/login", data={
                'email': email,
                'password': TEST_STUDENT_PASSWORD
            })
            
            if response.status_code == 200 and "dashboard" in response.url:
                self.student_sessions[f"student{i+1}"] = session
                print(f"✅ Student {i+1} login successful")
            else:
                print(f"❌ Student {i+1} login failed: {response.status_code}")
                return False
        return True
    
    def create_test_quiz(self, randomize=True, num_questions=5):
        """Create a test quiz with specified randomization setting"""
        print(f"📝 Creating test quiz with {num_questions} questions (randomize: {randomize})...")
        
        quiz_data = {
            'quiz_title': f'Randomization Test Quiz - {datetime.now().strftime("%H:%M:%S")}',
            'quiz_description': f'Test quiz with {num_questions} questions for randomization testing',
            'time_limit': '30',
            'total_marks': str(num_questions * 2),  # 2 marks per question
            'grade_a': '90',
            'grade_b': '80',
            'grade_c': '70',
            'grade_d': '60',
            'difficulty': 'medium'
        }
        
        # Add randomization checkbox if enabled
        if randomize:
            quiz_data['randomize_questions'] = 'on'
        
        # Add questions
        for i in range(num_questions):
            quiz_data[f'question[{i}]'] = f'Question {i+1}: What is {i+1} + {i+1}?'
            quiz_data[f'question_type[{i}]'] = 'mcq'
            quiz_data[f'question_marks[{i}]'] = '2'
            quiz_data[f'option1[{i}]'] = str((i+1) * 2)  # Correct answer
            quiz_data[f'option2[{i}]'] = str((i+1) * 2 + 1)
            quiz_data[f'option3[{i}]'] = str((i+1) * 2 + 2)
            quiz_data[f'option4[{i}]'] = str((i+1) * 2 + 3)
            quiz_data[f'correct_answer_{i}'] = '1'  # First option is correct
        
        response = self.teacher_session.post(f"{BASE_URL}/teacher/create-quiz", data=quiz_data)
        
        if response.status_code == 200:
            print("✅ Test quiz created successfully")
            # Extract quiz ID from redirect or response
            # This is a simplified approach - in real testing you'd parse the response properly
            return True
        else:
            print(f"❌ Failed to create test quiz: {response.status_code}")
            return False
    
    def get_quiz_list(self):
        """Get list of available quizzes"""
        print("📋 Getting quiz list...")
        response = self.student_sessions['student1'].get(f"{BASE_URL}/student/dashboard")
        
        if response.status_code == 200:
            # In a real test, you'd parse the HTML to extract quiz IDs
            # For now, we'll assume quiz IDs 1-10 exist
            print("✅ Quiz list retrieved")
            return list(range(1, 11))  # Simplified - return quiz IDs 1-10
        else:
            print(f"❌ Failed to get quiz list: {response.status_code}")
            return []
    
    def get_question_order(self, student_session, quiz_id):
        """Get the question order for a specific student and quiz"""
        print(f"🔍 Getting question order for quiz {quiz_id}...")
        response = student_session.get(f"{BASE_URL}/quiz/{quiz_id}/attempt")
        
        if response.status_code == 200:
            # In a real test, you'd parse the HTML to extract question order
            # For demonstration, we'll simulate different orders
            html_content = response.text
            
            # Extract question IDs from the HTML (simplified)
            # In reality, you'd use BeautifulSoup or similar to parse properly
            question_ids = []
            lines = html_content.split('\n')
            for line in lines:
                if 'id="question-' in line:
                    # Extract question ID from HTML
                    start = line.find('id="question-') + len('id="question-')
                    end = line.find('"', start)
                    if end > start:
                        try:
                            question_id = int(line[start:end])
                            question_ids.append(question_id)
                        except ValueError:
                            pass
            
            print(f"✅ Found question order: {question_ids}")
            return question_ids
        else:
            print(f"❌ Failed to get question order: {response.status_code}")
            return []
    
    def test_randomization_different_students(self, quiz_id):
        """Test that different students get different question orders"""
        print(f"\n🎲 Testing randomization for different students on quiz {quiz_id}...")
        
        orders = {}
        for student_name, session in self.student_sessions.items():
            order = self.get_question_order(session, quiz_id)
            orders[student_name] = order
            print(f"  {student_name}: {order}")
        
        # Check if orders are different
        order_values = list(orders.values())
        if len(set(map(tuple, order_values))) > 1:
            print("✅ Different students received different question orders")
            return True
        else:
            print("⚠️  All students received the same question order")
            return False
    
    def test_order_preservation(self, quiz_id):
        """Test that question order is preserved for the same student"""
        print(f"\n🔒 Testing order preservation for quiz {quiz_id}...")
        
        student_session = self.student_sessions['student1']
        
        # Get order first time
        order1 = self.get_question_order(student_session, quiz_id)
        time.sleep(1)  # Small delay
        
        # Get order second time
        order2 = self.get_question_order(student_session, quiz_id)
        
        if order1 == order2:
            print("✅ Question order preserved across multiple requests")
            return True
        else:
            print(f"❌ Question order changed: {order1} -> {order2}")
            return False
    
    def test_small_quiz_no_randomization(self):
        """Test that quizzes with ≤2 questions don't get randomized"""
        print(f"\n🔢 Testing small quiz (≤2 questions) randomization...")
        
        # Create a quiz with 2 questions
        if not self.create_test_quiz(randomize=True, num_questions=2):
            return False
        
        # Test with multiple students - should get same order
        quiz_id = 1  # Simplified - assume it's quiz ID 1
        orders = {}
        for student_name, session in self.student_sessions.items():
            order = self.get_question_order(session, quiz_id)
            orders[student_name] = order
        
        # All orders should be the same for small quizzes
        order_values = list(orders.values())
        if len(set(map(tuple, order_values))) == 1:
            print("✅ Small quiz correctly shows same order for all students")
            return True
        else:
            print("❌ Small quiz incorrectly randomized")
            return False
    
    def run_all_tests(self):
        """Run all randomization tests"""
        print("🚀 Starting Quiz Randomization Tests")
        print("=" * 60)
        
        # Login
        if not self.login_teacher():
            return False
        
        if not self.login_students():
            return False
        
        # Create test quiz
        if not self.create_test_quiz(randomize=True, num_questions=5):
            return False
        
        # Get available quizzes
        quiz_ids = self.get_quiz_list()
        if not quiz_ids:
            return False
        
        test_quiz_id = quiz_ids[0]  # Use first available quiz
        
        # Run tests
        tests_passed = 0
        total_tests = 3
        
        if self.test_randomization_different_students(test_quiz_id):
            tests_passed += 1
        
        if self.test_order_preservation(test_quiz_id):
            tests_passed += 1
        
        if self.test_small_quiz_no_randomization():
            tests_passed += 1
        
        # Summary
        print("\n" + "=" * 60)
        print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 All randomization tests passed!")
            return True
        else:
            print("⚠️  Some tests failed. Check implementation.")
            return False

def main():
    """Main test function"""
    print("Quiz Randomization Test Suite")
    print("=" * 40)
    print("This script tests the quiz question randomization functionality.")
    print("Make sure the Flask app is running on http://localhost:5000")
    print()
    
    input("Press Enter to start tests...")
    
    tester = QuizRandomizationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Some tests failed!")
    
    return success

if __name__ == "__main__":
    main()
